<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   version="1.1"
   id="svg5821"
   height="64px"
   width="64px">
  <defs
     id="defs5823">
    <linearGradient
       id="linearGradient6349">
      <stop
         id="stop6351"
         offset="0"
         style="stop-color:#000000;stop-opacity:1;" />
      <stop
         id="stop6353"
         offset="1"
         style="stop-color:#000000;stop-opacity:0;" />
    </linearGradient>
    <linearGradient
       id="linearGradient3377">
      <stop
         id="stop3379"
         offset="0"
         style="stop-color:#0019a3;stop-opacity:1;" />
      <stop
         id="stop3381"
         offset="1"
         style="stop-color:#0069ff;stop-opacity:1;" />
    </linearGradient>
    <linearGradient
       gradientTransform="matrix(-1,0,0,1,2199.356,0)"
       gradientUnits="userSpaceOnUse"
       y2="1190.875"
       x2="1267.9062"
       y1="1190.875"
       x1="901.1875"
       id="linearGradient3383"
       xlink:href="#linearGradient3377" />
    <radialGradient
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(-1.4307499,-1.3605156e-7,-1.202713e-8,0.1264801,2674.7488,1244.2826)"
       r="194.40614"
       fy="1424.4465"
       fx="1103.6399"
       cy="1424.4465"
       cx="1103.6399"
       id="radialGradient6355"
       xlink:href="#linearGradient6349" />
  </defs>
  <metadata
     id="metadata5826">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
        <dc:creator>
          <cc:Agent>
            <dc:title>[triplus]</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:title>SketcherWorkbench</dc:title>
        <dc:date>2016-02-26</dc:date>
        <dc:relation>https://www.freecad.org/wiki/index.php?title=Artwork</dc:relation>
        <dc:publisher>
          <cc:Agent>
            <dc:title>FreeCAD</dc:title>
          </cc:Agent>
        </dc:publisher>
        <dc:identifier>FreeCAD/src/Mod/Sketcher/Gui/Resources/icons/SketcherWorkbench.svg</dc:identifier>
        <dc:rights>
          <cc:Agent>
            <dc:title>FreeCAD LGPL2+</dc:title>
          </cc:Agent>
        </dc:rights>
        <cc:license>https://www.gnu.org/copyleft/lesser.html</cc:license>
        <dc:contributor>
          <cc:Agent>
            <dc:title>[agryson] Alexander Gryson</dc:title>
          </cc:Agent>
        </dc:contributor>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     id="layer1">
    <g
       transform="matrix(0.1367863,0,0,0.1367863,-119.15519,-134.86962)"
       id="g3360">
      <rect
         y="1168.7546"
         x="907.65808"
         height="248.5629"
         width="277.8056"
         id="rect3860-36"
         style="color:#000000;fill:none;stroke:#a40000;stroke-width:43.86403656000000240;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" />
      <path
         transform="matrix(7.6396543,0,0,7.2375671,932.95299,913.31967)"
         d="m 48.363636,33.272728 c 0,10.041541 -8.140277,18.181818 -18.181818,18.181818 C 20.140277,51.454546 12,43.314269 12,33.272728 12,23.231187 20.140277,15.09091 30.181818,15.09091 c 10.041541,0 18.181818,8.140277 18.181818,18.181818 z"
         id="path3862-7"
         style="color:#000000;fill:none;stroke:#a40000;stroke-width:5.89896058999999973;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" />
      <rect
         y="1168.7546"
         x="907.65808"
         height="248.5629"
         width="277.8056"
         id="rect3860-3"
         style="color:#000000;fill:none;stroke:#ef2929;stroke-width:14.62134743000000014;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" />
      <path
         transform="matrix(7.6396539,0,0,7.237567,932.95301,913.31967)"
         d="m 48.363636,33.272728 c 0,10.041541 -8.140277,18.181818 -18.181818,18.181818 C 20.140277,51.454546 12,43.314269 12,33.272728 12,23.231187 20.140277,15.09091 30.181818,15.09091 c 10.041541,0 18.181818,8.140277 18.181818,18.181818 z"
         id="path3862-6"
         style="color:#000000;fill:none;stroke:#ef2929;stroke-width:1.96632027999999992;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" />
    </g>
  </g>
</svg>
