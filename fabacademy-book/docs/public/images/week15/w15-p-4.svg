<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-7.5 -8 423 1325" style="max-width: 423px;" xmlns="http://www.w3.org/2000/svg" width="423" id="svg" height="1325"><style>#svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#svg .error-icon{fill:#552222;}#svg .error-text{fill:#552222;stroke:#552222;}#svg .edge-thickness-normal{stroke-width:2px;}#svg .edge-thickness-thick{stroke-width:3.5px;}#svg .edge-pattern-solid{stroke-dasharray:0;}#svg .edge-pattern-dashed{stroke-dasharray:3;}#svg .edge-pattern-dotted{stroke-dasharray:2;}#svg .marker{fill:#333333;stroke:#333333;}#svg .marker.cross{stroke:#333333;}#svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#svg .cluster-label text{fill:#333;}#svg .cluster-label span,#svg p{color:#333;}#svg .label text,#svg span,#svg p{fill:#333;color:#333;}#svg .node rect,#svg .node circle,#svg .node ellipse,#svg .node polygon,#svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#svg .flowchart-label text{text-anchor:middle;}#svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#svg .node .label{text-align:center;}#svg .node.clickable{cursor:pointer;}#svg .arrowheadPath{fill:#333333;}#svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#svg .flowchart-link{stroke:#333333;fill:none;}#svg .edgeLabel{background-color:#e8e8e8;text-align:center;}#svg .edgeLabel rect{opacity:0.5;background-color:#e8e8e8;fill:#e8e8e8;}#svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#svg .cluster text{fill:#333;}#svg .cluster span,#svg p{color:#333;}#svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#svg .today{display:none;}#svg .label foreignObject{overflow:visible;}#svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="6" viewBox="0 0 10 10" class="marker flowchart" id="svg_flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart" id="svg_flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="svg_flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="svg_flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="svg_flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="svg_flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"></g><g class="edgeLabels"></g><g class="nodes"><g transform="translate(-7.5, -8)" class="root"><g class="clusters"><g id="智幻走马灯控制界面" class="cluster default flowchart-label"><rect height="1309" width="407" y="8" x="8" ry="0" rx="0" style="fill:#f9f9f9;stroke:#333;stroke-width:2px;"></rect><g transform="translate(139.5, 8)" class="cluster-label"><foreignObject height="21" width="144"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">智幻走马灯控制界面</span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#svg_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-header LE-status" id="L-header-status-0" d="M211.5,69L211.5,73.167C211.5,77.333,211.5,85.667,211.5,93.117C211.5,100.567,211.5,107.133,211.5,110.417L211.5,113.7"></path><path marker-end="url(#svg_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-status LE-control" id="L-status-control-0" d="M211.5,225L211.5,229.167C211.5,233.333,211.5,241.667,211.5,249.117C211.5,256.567,211.5,263.133,211.5,266.417L211.5,269.7"></path><path marker-end="url(#svg_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-control LE-footer" id="L-control-footer-0" d="M211.5,1185L211.5,1189.167C211.5,1193.333,211.5,1201.667,211.5,1209.117C211.5,1216.567,211.5,1223.133,211.5,1226.417L211.5,1229.7"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(211.5, 51)" data-id="header" data-node="true" id="flowchart-header-0" class="node default default flowchart-label"><rect height="36" width="127" y="-18" x="-63.5" ry="0" rx="0" style="fill:#e6f3ff;stroke:#333;stroke-width:1px;" class="basic label-container"></rect><g transform="translate(-56, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="112"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">智幻走马灯控制</span></div></foreignObject></g></g><g transform="translate(35.5, 111)" class="root"><g class="clusters"><g id="status" class="cluster default flowchart-label"><rect height="106" width="337" y="8" x="8" ry="0" rx="0" style="fill:#f0f0f0;stroke:#333;stroke-width:1px;"></rect><g transform="translate(144.5, 8)" class="cluster-label"><foreignObject height="21" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">状态区域</span></div></foreignObject></g></g></g><g class="edgePaths"><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-conn LE-ip" id="L-conn-ip-0" d="M115,61L119.167,61C123.333,61,131.667,61,140,61C148.333,61,156.667,61,160.833,61L165,61"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(74, 61)" data-id="conn" data-node="true" id="flowchart-conn-1" class="node default default flowchart-label"><rect height="36" width="82" y="-18" x="-41" ry="0" rx="0" style="fill:#fff;stroke:#333;stroke-width:1px;" class="basic label-container"></rect><g transform="translate(-33.5, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="67"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">● 已连接</span></div></foreignObject></g></g><g transform="translate(242.5, 61)" data-id="ip" data-node="true" id="flowchart-ip-2" class="node default default flowchart-label"><rect height="34" width="155" y="-17" x="-77.5" ry="0" rx="0" style="fill:#fff;stroke:#333;stroke-width:1px;" class="basic label-container"></rect><g transform="translate(-70, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="140"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">IP: 192.168.1.100</span></div></foreignObject></g></g></g></g><g transform="translate(38.5, 267)" class="root"><g class="clusters"><g id="control" class="cluster default flowchart-label"><rect height="910" width="331" y="8" x="8" ry="0" rx="0" style="fill:#f9f9f9;stroke:#333;stroke-width:1px;"></rect><g transform="translate(139.5, 8)" class="cluster-label"><foreignObject height="21" width="68"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">LED 控制</span></div></foreignObject></g></g></g><g class="edgePaths"></g><g class="edgeLabels"></g><g class="nodes"><g transform="translate(103, 35)" class="root"><g class="clusters"><g id="leds" class="cluster default flowchart-label"><rect height="618" width="126" y="8" x="8" ry="0" rx="0" style=""></rect><g transform="translate(21, 8)" class="cluster-label"><foreignObject height="21" width="100"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">LED 状态显示</span></div></foreignObject></g></g></g><g class="edgePaths"><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-led0 LE-led1" id="L-led0-led1-0" d="M71,86L71,90.167C71,94.333,71,102.667,71,111C71,119.333,71,127.667,71,131.833L71,136"></path><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-led1 LE-led2" id="L-led1-led2-0" d="M71,189L71,193.167C71,197.333,71,205.667,71,214C71,222.333,71,230.667,71,234.833L71,239"></path><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-led2 LE-led3" id="L-led2-led3-0" d="M71,292L71,296.167C71,300.333,71,308.667,71,317C71,325.333,71,333.667,71,337.833L71,342"></path><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-led3 LE-led4" id="L-led3-led4-0" d="M71,395L71,399.167C71,403.333,71,411.667,71,420C71,428.333,71,436.667,71,440.833L71,445"></path><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-led4 LE-led5" id="L-led4-led5-0" d="M71,498L71,502.167C71,506.333,71,514.667,71,523C71,531.333,71,539.667,71,543.833L71,548"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(71, 59.5)" data-id="led0" data-node="true" id="flowchart-led0-3" class="node default default flowchart-label"><rect height="53" width="56" y="-26.5" x="-28" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-20.5, -19)" style="" class="label"><rect></rect><foreignObject height="38" width="41"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">○<br/>LED0</span></div></foreignObject></g></g><g transform="translate(71, 162.5)" data-id="led1" data-node="true" id="flowchart-led1-4" class="node default default flowchart-label"><rect height="53" width="56" y="-26.5" x="-28" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-20.5, -19)" style="" class="label"><rect></rect><foreignObject height="38" width="41"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">○<br/>LED1</span></div></foreignObject></g></g><g transform="translate(71, 265.5)" data-id="led2" data-node="true" id="flowchart-led2-5" class="node default default flowchart-label"><rect height="53" width="56" y="-26.5" x="-28" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-20.5, -19)" style="" class="label"><rect></rect><foreignObject height="38" width="41"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">○<br/>LED2</span></div></foreignObject></g></g><g transform="translate(71, 368.5)" data-id="led3" data-node="true" id="flowchart-led3-6" class="node default default flowchart-label"><rect height="53" width="56" y="-26.5" x="-28" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-20.5, -19)" style="" class="label"><rect></rect><foreignObject height="38" width="41"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">○<br/>LED3</span></div></foreignObject></g></g><g transform="translate(71, 471.5)" data-id="led4" data-node="true" id="flowchart-led4-7" class="node default default flowchart-label"><rect height="53" width="56" y="-26.5" x="-28" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-20.5, -19)" style="" class="label"><rect></rect><foreignObject height="38" width="41"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">○<br/>LED4</span></div></foreignObject></g></g><g transform="translate(71, 574.5)" data-id="led5" data-node="true" id="flowchart-led5-8" class="node default default flowchart-label"><rect height="53" width="56" y="-26.5" x="-28" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-20.5, -19)" style="" class="label"><rect></rect><foreignObject height="38" width="41"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">○<br/>LED5</span></div></foreignObject></g></g></g></g><g transform="translate(25.5, 703)" class="root"><g class="clusters"><g id="buttons" class="cluster default flowchart-label"><rect height="172" width="281" y="8" x="8" ry="0" rx="0" style=""></rect><g transform="translate(116.5, 8)" class="cluster-label"><foreignObject height="21" width="64"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">控制按钮</span></div></foreignObject></g></g></g><g class="edgePaths"><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-decButton LE-incButton" id="L-decButton-incButton-0" d="M75.5,69L75.5,73.167C75.5,77.333,75.5,85.667,75.5,94C75.5,102.333,75.5,110.667,75.5,114.833L75.5,119"></path><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-offButton LE-onButton" id="L-offButton-onButton-0" d="M206,69L206,73.167C206,77.333,206,85.667,206,94C206,102.333,206,110.667,206,114.833L206,119"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(75.5, 51)" data-id="decButton" data-node="true" id="flowchart-decButton-9" class="node default default flowchart-label"><rect height="36" width="65" y="-18" x="-32.5" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-25, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="50"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">减少 ⬇️</span></div></foreignObject></g></g><g transform="translate(75.5, 137)" data-id="incButton" data-node="true" id="flowchart-incButton-10" class="node default default flowchart-label"><rect height="36" width="65" y="-18" x="-32.5" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-25, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="50"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">增加 ⬆️</span></div></foreignObject></g></g><g transform="translate(206, 51)" data-id="offButton" data-node="true" id="flowchart-offButton-11" class="node default default flowchart-label"><rect height="36" width="96" y="-18" x="-48" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-40.5, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="81"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">全部关闭 ◀️</span></div></foreignObject></g></g><g transform="translate(206, 137)" data-id="onButton" data-node="true" id="flowchart-onButton-12" class="node default default flowchart-label"><rect height="36" width="96" y="-18" x="-48" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-40.5, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="81"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">全部点亮 ▶️</span></div></foreignObject></g></g></g></g></g></g><g transform="translate(211.5, 1263.5)" data-id="footer" data-node="true" id="flowchart-footer-13" class="node default default flowchart-label"><rect height="57" width="218" y="-28.5" x="-109" ry="0" rx="0" style="fill:#f0f0f0;stroke:#333;stroke-width:1px;" class="basic label-container"></rect><g transform="translate(-101.5, -21)" style="" class="label"><rect></rect><foreignObject height="42" width="203"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">智幻走马灯控制系统 v1.0.0<br/>基于 ESP32C3 + Web 技术</span></div></foreignObject></g></g></g></g></g></g></g></svg>