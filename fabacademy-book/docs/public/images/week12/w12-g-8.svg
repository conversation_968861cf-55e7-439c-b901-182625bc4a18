<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -8 261 462" style="max-width: 261px;" xmlns="http://www.w3.org/2000/svg" width="261" id="svg" height="462"><style>#svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#svg .error-icon{fill:#552222;}#svg .error-text{fill:#552222;stroke:#552222;}#svg .edge-thickness-normal{stroke-width:2px;}#svg .edge-thickness-thick{stroke-width:3.5px;}#svg .edge-pattern-solid{stroke-dasharray:0;}#svg .edge-pattern-dashed{stroke-dasharray:3;}#svg .edge-pattern-dotted{stroke-dasharray:2;}#svg .marker{fill:#333333;stroke:#333333;}#svg .marker.cross{stroke:#333333;}#svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#svg .cluster-label text{fill:#333;}#svg .cluster-label span,#svg p{color:#333;}#svg .label text,#svg span,#svg p{fill:#333;color:#333;}#svg .node rect,#svg .node circle,#svg .node ellipse,#svg .node polygon,#svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#svg .flowchart-label text{text-anchor:middle;}#svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#svg .node .label{text-align:center;}#svg .node.clickable{cursor:pointer;}#svg .arrowheadPath{fill:#333333;}#svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#svg .flowchart-link{stroke:#333333;fill:none;}#svg .edgeLabel{background-color:#e8e8e8;text-align:center;}#svg .edgeLabel rect{opacity:0.5;background-color:#e8e8e8;fill:#e8e8e8;}#svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#svg .cluster text{fill:#333;}#svg .cluster span,#svg p{color:#333;}#svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#svg .today{display:none;}#svg .label foreignObject{overflow:visible;}#svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="6" viewBox="0 0 10 10" class="marker flowchart" id="svg_flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart" id="svg_flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="svg_flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="svg_flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="svg_flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="svg_flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#svg_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-B" id="L-A-B-0" d="M122.5,34L122.5,39.75C122.5,45.5,122.5,57,122.5,67.617C122.5,78.233,122.5,87.967,122.5,92.833L122.5,97.7"></path><path marker-end="url(#svg_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-B LE-C" id="L-B-C-0" d="M122.5,137L122.5,142.75C122.5,148.5,122.5,160,122.5,170.617C122.5,181.233,122.5,190.967,122.5,195.833L122.5,200.7"></path><path marker-end="url(#svg_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-D" id="L-C-D-0" d="M122.5,240L122.5,245.75C122.5,251.5,122.5,263,122.5,273.617C122.5,284.233,122.5,293.967,122.5,298.833L122.5,303.7"></path><path marker-end="url(#svg_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-D LE-E" id="L-D-E-0" d="M122.5,343L122.5,348.75C122.5,354.5,122.5,366,122.5,376.617C122.5,387.233,122.5,396.967,122.5,401.833L122.5,406.7"></path></g><g class="edgeLabels"><g transform="translate(122.5, 68.5)" class="edgeLabel"><g transform="translate(-56, -9.5)" class="label"><foreignObject height="19" width="112"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">Voice Capture</span></div></foreignObject></g></g><g transform="translate(122.5, 171.5)" class="edgeLabel"><g transform="translate(-71, -9.5)" class="label"><foreignObject height="19" width="142"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">Package Message</span></div></foreignObject></g></g><g transform="translate(122.5, 274.5)" class="edgeLabel"><g transform="translate(-65.5, -9.5)" class="label"><foreignObject height="19" width="131"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">Publish Message</span></div></foreignObject></g></g><g transform="translate(122.5, 377.5)" class="edgeLabel"><g transform="translate(-76, -9.5)" class="label"><foreignObject height="19" width="152"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">Distribute Message</span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(122.5, 17)" data-id="A" data-node="true" id="flowchart-A-0" class="node default default flowchart-label"><rect height="34" width="226" y="-17" x="-113" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-105.5, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="211"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">SenseCAP Watcher Device</span></div></foreignObject></g></g><g transform="translate(122.5, 120)" data-id="B" data-node="true" id="flowchart-B-1" class="node default default flowchart-label"><rect height="34" width="245" y="-17" x="-122.5" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-115, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="230"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">XIAOZHI AI Cloud Processing</span></div></foreignObject></g></g><g transform="translate(122.5, 223)" data-id="C" data-node="true" id="flowchart-C-3" class="node default default flowchart-label"><rect height="34" width="112" y="-17" x="-56" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-48.5, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="97"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">MQTT Client</span></div></foreignObject></g></g><g transform="translate(122.5, 326)" data-id="D" data-node="true" id="flowchart-D-5" class="node default default flowchart-label"><rect height="34" width="120" y="-17" x="-60" ry="5" rx="5" style="" class="basic label-container"></rect><g transform="translate(-52.5, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="105"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">MQTT Broker</span></div></foreignObject></g></g><g transform="translate(122.5, 429)" data-id="E" data-node="true" id="flowchart-E-7" class="node default default flowchart-label"><rect height="34" width="233" y="-17" x="-116.5" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-109, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="218"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Subscriber Devices/Servers</span></div></foreignObject></g></g></g></g></g></svg>