---
layout: doc
title: "Project: Intellectual Property & Business Planning | Feng Lei Fab Academy 2025"
description: "Fab Academy 2025 Week 19 project, regarding intellectual property decisions and business planning for the Smart Fantasy Lantern project."
head:
  - - meta
    - name: keywords
      content: fab academy, final project, intellectual property, business planning, smart fantasy lantern, personal assignment
  - - meta
    - name: author
      content: Feng Lei
prev:
  text: 'Week 19: Invention, Intellectual Property & Income'
  link: './week19_invention_intellectual_property_income_en'
next:
  text: 'Final Project'
  link: 'https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/en/final-project-en'
---

# Smart Fantasy Lantern Project - Intellectual Property Decisions & Business Planning

## I. Intellectual Property Decisions
### 1.1 License Selection: MIT License
After careful consideration, I have decided to adopt the **MIT License** for the Smart Fantasy Lantern project. The reasons for this choice are as follows:

#### Why Choose MIT License?
+ **Maximize Open Source Value**: MIT License is one of the most permissive open source licenses, allowing anyone to freely use, modify, distribute, and commercialize
+ **Promote Innovation Diffusion**: Lower barriers to entry, enabling more Makers to conduct secondary innovation based on this project
+ **Cultural Heritage Goal**: Aligns with the project's original intention of preserving traditional culture, allowing the Smart Fantasy Lantern concept to spread widely
+ **Education-Friendly**: Convenient for use in educational settings such as schools and training institutions
+ **Commercial Compatibility**: Does not hinder reasonable commercialization based on the project, achieving balance between open source and commercial interests

#### MIT License Specific Content
```plain
MIT License

Copyright (c) 2025 Lei Feng (冯磊)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

### 1.2 Open Source Content Inventory
The following content will be completely open sourced:

**Hardware Design**

+ Complete 3D model files (Fusion 360 format)
+ Laser cutting drawings (DXF format)
+ PCB design files (JLCPCB EDA format)
+ Bill of Materials (BOM)
+ Assembly instruction documentation

**Software Code**

+ Complete firmware source code
+ Web control interface code
+ MQTT communication protocol documentation
+ Programming interface documentation

**Making Tutorials**

+ Detailed making step instructions
+ Troubleshooting guide
+ Detailed technical specifications
+ Video tutorials

### 1.3 Intellectual Property Protection Strategy
Although choosing open source, reasonable protection strategies are still needed:

**Copyright Protection**

+ Clearly mark copyright information in all design files
+ Establish complete version control and timestamp records
+ Regularly backup all design documents

**Trademark Considerations**

+ Register "Smart Fantasy Lantern" trademark to protect project name
+ Establish unique visual identity system
+ Prevent malicious registration and abuse

**Patent Strategy**

+ Do not apply for patents to avoid limiting open source dissemination
+ Establish prior art through open source publication
+ Defensive publication to prevent others from applying blocking patents

## II. Chinese Book Publication Plan
I hope to write a Chinese book to introduce Fab Academy to Chinese readers, along with my learning process over the past six months and my Final Project, to help Chinese people who aspire to become Makers.

### 2.1 Book Content Planning
#### Title: "From Zero to Smart Manufacturing: My Digital Fabrication Journey"
Subtitle: Fab Academy Learning Notes and Smart Fantasy Lantern Complete Creation Guide

#### Content Structure (approximately 300,000 words)
**Part I: Digital Manufacturing Enlightenment (approximately 80,000 words)**

+ Chapter 1: What is Fab Academy? (20,000 words)
    - History and philosophy of Fab Labs
    - Introduction to the global Fab Academy network
    - Curriculum system and teaching methods
    - My application and preparation experience
+ Chapter 2: Overview of Digital Manufacturing Technologies (30,000 words)
    - Introduction to 2D/3D design software
    - Additive manufacturing (3D printing) technology
    - Subtractive manufacturing (laser cutting, CNC) technology
    - Electronic manufacturing and PCB design
+ Chapter 3: Fab Lab Laboratory Culture (20,000 words)
    - Open source hardware movement
    - Maker culture and maker spirit
    - Collaborative learning and knowledge sharing
    - Transformation from ideas to prototypes
+ Chapter 4: Learning Methods and Project Management (10,000 words)
    - Spiral development methodology
    - Time management and project planning
    - Value of failure and iterative thinking
    - Importance of documentation

**Part II: 18-Week Learning Record (approximately 120,000 words)**

+ Chapters 5-22: Weekly learning records (approximately 6,000 words each)
    - Weekly course content summary
    - Personal assignment challenges and solutions
    - Technical difficulty breakthrough process
    - Learning insights and reflections

**Part III: Smart Fantasy Lantern Practice (approximately 80,000 words)**

+ Chapter 23: Project Conception and Design (15,000 words)
    - Thoughts on integrating traditional culture with modern technology
    - Requirement analysis and technical research
    - System architecture design
    - 3D modeling and simulation
+ Chapter 24: Detailed Hardware Manufacturing (20,000 words)
    - Laser cutting process key points
    - 3D printing techniques and optimization
    - PCB design and manufacturing process
    - Electronic component selection and soldering
+ Chapter 25: Software Development Practice (20,000 words)
    - Embedded programming practice
    - Gesture recognition algorithm implementation
    - Web interface development
    - MQTT communication protocol
+ Chapter 26: System Integration and Debugging (15,000 words)
    - Hardware assembly techniques
    - Software debugging methods
    - Performance optimization strategies
    - Troubleshooting guide
+ Chapter 27: Project Evaluation and Reflection (10,000 words)
    - Technical implementation analysis
    - Cost-benefit assessment
    - User experience testing
    - Improvement direction outlook

**Part IV: Innovation Practice (approximately 20,000 words)**

+ Chapter 28: Open Source Community Participation (5,000 words)
    - How to contribute to open source projects
    - GitHub usage tips
    - Community collaboration experience
+ Chapter 29: Commercialization Considerations (5,000 words)
    - Balance between open source and commercial interests
    - Intellectual property strategy
    - Business model exploration
+ Chapter 30: Future Outlook (10,000 words)
    - Digital manufacturing technology trends
    - Personal growth planning
    - Suggestions for China's Maker community

### 2.2 Publishing Strategy
**Traditional Publishing**

+ Target publishers: Discuss with previously collaborated publishers
+ Expected timeline: 6-12 months to complete writing, 3-6 months for publication process
+ Distribution channels: Online and offline bookstores, technical book specialty stores

**Digital Publishing**

+ Simultaneous e-book release: Kindle, iReader, WeChat Reading, and other platforms
+ Open source chapters: Some chapters freely available to promote technology dissemination
+ Supporting resources: Provide online resource downloads (code, 3D models, etc.)

### 2.3 Content Copyright Strategy
**Book Copyright**

+ Retain traditional copyright for commercial publication
+ Some technical chapters adopt Creative Commons license for open access

**Supporting Resources**

+ Technical materials continue using MIT License
+ Video tutorials adopt CC BY-SA License
+ Code examples maintain MIT License