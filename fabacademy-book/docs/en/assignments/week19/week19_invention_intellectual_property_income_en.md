---
layout: doc
title: "Week 19: Invention, Intellectual Property & Income | Feng Lei Fab Academy 2025"
description: "Fab Academy 2025 Week 19 course, exploring invention, intellectual property protection, and how to generate income through innovative projects."
head:
  - - meta
    - name: keywords
      content: fab academy, invention, intellectual property, income, innovation projects, personal assignment
  - - meta
    - name: author
      content: Feng Lei
prev:
  text: 'Week 18: Project Development & Applications Outlook'
  link: '../week18/week18_project_development_and_applications_outlook_en'
next:
  text: 'Smart Fantasy Lantern Project - IP Decisions & Business Planning'
  link: './week19_project_ip_business_plan_en'
---

# Week 19: Invention, Intellectual Property & Income

> This document content was generated by AI after I provided <PERSON> with the [course outline](http://academy.cba.mit.edu/classes/invention_IP_business/index.html) and the video conference course subtitles for refinement and generation.

## Course Overview
This is the final class of Fab Academy 2025, primarily covering the complete lifecycle from invention to commercialization. The course encompasses three core themes: managing the invention process, intellectual property protection strategies, and various business models for converting innovative results into income. The instructor emphasizes that the end of this course is actually the beginning of learning, where students need to apply their learned skills to actual projects and consider how to protect and commercialize their innovative achievements.

## Detailed Course Content
### I. Course Introduction & Important Reminders
#### 1. Fab25 Conference Invitation
+ This year's graduates can apply for free attendance at the Fab25 conference
+ Conference pricing uses a tiered structure, with lower fees for early registration
+ Conference pricing cannot fully cover costs, requiring additional fundraising support

#### 2. Academic Integrity Warning
+ Strictly prohibited to present AI-generated content as original work
+ Must honestly report one's work and properly attribute others' work
+ Need to record all AI prompts and sources
+ Violating academic integrity may result in inability to graduate

### II. The Process and Management of Invention
#### 1. Historical Background of Invention
+ **Science: The Endless Frontier** Report
    - Commissioned by President Roosevelt after WWII, written by Vannevar Bush
    - The report catalyzed the concept of the National Science Foundation
    - Established the modern research funding institution system

#### 2. "Ready-Fire-Aim" Model
The traditional "Ready-Aim-Fire" model can only successfully achieve preset goals, lacking space for innovative surprises. A more effective innovation model is:

+ **Ready**: Deep learning of relevant domain knowledge
+ **Fire**: Without overthinking, conduct free exploration and experimentation
+ **Aim**: Carefully observe and analyze what actually happens

#### 3. Innovation Case Studies
**From Anti-theft Tags to Quantum Computers**

+ Beginning: Practical project researching low-cost anti-theft tags
+ Development: Studying interaction between radio fields and materials
+ Breakthrough: Discovered nonlinear radio response, unexpectedly found quantum computing method
+ Application: Created early quantum computing, driving the entire quantum computing field
+ Extension: Technology eventually applied to commercialization of RFID tag readers

**From Cello Sensors to Automotive Safety**

+ Beginning: Creating sensors for famous cellist Yo-Yo Ma's cello
+ Development: Using electric fields for 3D sensing technology
+ Transformation: Technology applied to magic performances in Las Vegas
+ Commercialization: Eventually became automotive airbag control systems with $100 million annual sales

#### 4. Importance of Ecosystems
**Value of Communication Environment**

+ Economic output of several blocks around MIT equivalent to the world's 10th largest economy
+ Successful innovation ecosystems require: smart talent, interesting challenges, combination of short and long-term goals
+ Globally distributed Fab Lab network forming new innovation ecosystems

**Diversity and Inclusion**

+ Need to focus on who can participate in innovation and how
+ MIT once discovered gender inequality issues and conducted systematic reforms
+ Need to address systemic racism legacy issues
+ Fab All-In group dedicated to promoting diversity and inclusion

### III. Intellectual Property Protection Strategies
#### 1. Patent System Overview
**Patent Types**

+ **Utility Patents**: Protect how inventions work
+ **Design Patents**: Protect design appearance
+ Utility patents usually stronger and more commercially valuable

**Patent Application Process**

1. **Patent Search**: Use USPTO or Google patent search
2. **Disclosure Management**: US allows application within 1 year after public disclosure, most international jurisdictions require application before disclosure
3. **Provisional Application**: Protect early ideas, with 1 year to convert to formal application
4. **Formal Application**: Includes specification and claims
5. **Examination Process**: Check novelty, non-obviousness, utility

**Patent Limitations**

+ No patent police, must enforce rights yourself
+ Enforcement costs extremely high, potentially requiring hundreds of thousands of dollars
+ Only inventions where infringement can be identified and infringement threshold exists are worth patenting
+ Threats from patent trolls and submarine patents
+ Costs: provisional application $100, formal application $1000, complete process about $100,000

#### 2. Copyright Protection
**Copyright Advantages**

+ Automatically obtain copyright upon creation, no application needed
+ Protection period: author's lifetime plus 70 years
+ Extremely low application and maintenance costs
+ Easier to enforce and protect

**Copyright Scope**

+ Protects creative works: code, circuit designs, CAD files, etc.
+ Includes multiple rights: copy, modify, distribute, perform, display
+ Strengthen protection through copyright notices and registration

**Open Source and Business Models**

+ Open source doesn't equal free
+ Companies like Red Hat, Prusa built successful business models based on open source technology
+ Can simultaneously provide open source licenses and commercial licenses

#### 3. License Selection
**Common License Types**

+ Creative Commons: Can mix and match different rights
+ GPL, LGPL, BSD, MIT, Apache: Each has pros and cons
+ FAB License: Simplified single-sentence license, easy to understand and use

**Trademark Considerations**

+ Need to register and continuously protect
+ Must mark trademark status with each use
+ Fab Lab chose not to register trademarks to avoid enforcement burden

### IV. Revenue Models and Commercialization Strategies
#### 1. Commercialization Motivations
+ **Profit**: Most obvious motivation
+ **Impact**: Like Barcelona's environmental sensor project
+ **Lifestyle**: Creating desired work environment and community

#### 2. Basic Business Principles
**Pull vs Push**

+ Push: Having invention wanting to do business (usually fails)
+ Pull: Someone needs your invention (more likely to succeed)
+ Usually solving pain points rather than realizing visions

**Moat Concept**

+ Scale effects
+ Intellectual property protection
+ Unique business perception
+ Other competitive barriers

**Minimum Viable Product (MVP)**

+ Equivalent to spiral development
+ Simplest product demonstrating all core principles
+ Optimize after receiving market feedback

#### 3. Diversified Business Models
**Product Sales Models**

+ **Complete Products**: Like Form Labs' 3D printers
+ **Kit Sales**: Like Prusa's printer kits
+ **Razor-Blade Model**: Low-price equipment, high-price consumables

**Intellectual Property Licensing**

+ ARM processor IP licensing model
+ Business model entirely based on design licensing
+ Facing challenges from open source alternatives (like RISC-V)

**Advertising and Data Models**

+ Google's search advertising model
+ Selling search behavior rather than search results
+ Commercialization of data value

**Service-Oriented Models**

+ Educational services: Fab Academy nodes
+ Customization services: furniture customization
+ Consulting services: laboratory setup and operations
+ Impact services: community project evaluation
+ Infrastructure services: like Amazon AWS

**Innovative Service Models**

+ Rolls-Royce engines: pay per thrust rather than buying engines
+ Selling benefits brought by products rather than products themselves

#### 4. Organizational Structure Selection
**For-Profit Organizations**

+ Sole proprietorship
+ Partnership
+ Limited liability company
+ Employee stock ownership trust
+ Full corporation

**Non-Profit Organizations**

+ 501(c)(3) and other non-profit forms
+ Non-profit doesn't equal free, can have huge budgets
+ Don't generate profit but can have income and pay salaries

**Hybrid Models**

+ Mozilla Foundation + Corporation dual structure
+ Multiple bottom line companies: financial + social responsibility
+ B Corp and benefit corporations
+ Blockchain and alternative currency systems

#### 5. Financing Strategies
**Traditional Financing**

+ Venture capital (relevance declining)
+ Incubator investment
+ Angel investors
+ Friends and family investment

**Emerging Financing Methods**

+ Crowdfunding: Form Labs raised $3 million through Kickstarter
+ Pre-purchase commitments: reduce bank loan risk
+ Bootstrap: rolling development through early revenue

**Financing Advice**

+ Avoid venture capital in early stages
+ Use Fab Labs to reduce product risk
+ Get pre-purchase commitments before applying for bank loans
+ Importance of maintaining control

#### 6. Enterprise Development Challenges
**Standard Development Issues**

+ 1-10 people: early management challenges
+ 10-100 people: personnel issues around 40 people
+ Need professional management team
+ Founder skills vs growth skills differences
+ Investors may require selling the company

**Emerging Support Ecosystem**

+ Distributed incubator concept
+ Using entire Fab Lab network as incubator
+ Continuous support from learning skills to business development

---

## Assignment Requirements
### This Week's Assignment Tasks
#### Minor Assignment: Intellectual Property Decisions
1. **License Selection**: Decide how to disseminate your final project
2. **Protection Strategy**: Choose appropriate license type
3. **Business Planning**: Consider whether to create enterprise or organization

#### Major Assignment: Complete Final Project
**Project Presentation Requirements**

1. **Project Function**: What can your project do?
2. **Background Research**: What related work existed before?
3. **Resource Sources**: What resources were used during development?
4. **Design Content**: What did you design?
5. **Cost Analysis**: Detailed supplier and cost information
6. **Making Process**: What did you make? What processes were used?
7. **Problem Solving**: What questions did you need to answer?
8. **Success and Failure**: What worked, what didn't?
9. **Project Evaluation**: How to judge if the project is successful?
10. **Impact Analysis**: Impact on world and individual, future development directions

**Quality Requirements**

+ Go beyond rough laser cutting level
+ Achieve system integration and finished product quality
+ Apply all learned skills and concepts

**Time Management Advice**

+ Conduct priority triage
+ Apply supply-side time management
+ Use spiral development and parallel task processing

---

## Learning Resources
### Online Resources
1. **Patent Search**
    - [USPTO Patent Search](https://www.uspto.gov/)
    - [Google Patent Search](https://patents.google.com/)
    - [European Patent Office (EPO)](https://www.epo.org/)
2. **Copyright Information**
    - [U.S. Copyright Office](https://www.copyright.gov/)
    - [Creative Commons Licenses](https://creativecommons.org/)
3. **Open Source Hardware**
    - [Open Source Hardware Association (OSHWA)](https://www.oshwa.org/)
4. **Business Development**
    - Form Labs Kickstarter case study
    - Mozilla dual structure model
    - Red Hat open source business model

### Historical Documents
1. **Science: The Endless Frontier** - Vannevar Bush Report
2. **Ready, Fire, Aim** - Innovation methodology lecture
3. MIT Gender Equality Research Report
4. MIT Systemic Racism Research

### Practical Tools
1. **License Generator**: For creating appropriate open source licenses
2. **Cost Calculation Sheet**: Project cost analysis template
3. **Business Model Canvas**: Business model design tool
4. **MVP Planning Template**: Minimum viable product design guide

### Community Resources
1. **Fab All-In Group**: Diversity and inclusion initiative
2. **Distributed Incubator Network**: Startup support system under development
3. **Fab25 Conference**: Global Fab Lab community gathering
4. **Local Fab Labs**: Ongoing technical and business support

---

## Course Summary
This class emphasized an important point: the end of Fab Academy is actually the beginning of real learning. Students learned a vast amount of knowledge through linear coursework, but in future practice, this knowledge will be reintegrated and applied in non-linear ways. Each skill needs to deepen understanding through actual projects, and many students will become leaders in the network.

Successful innovation requires appropriate ecosystem support, including diverse talent, interesting challenges, and a balance of short and long-term goals. The choice of intellectual property protection and commercialization strategies should be based on specific project characteristics and objectives, not one-size-fits-all solutions. Most importantly, always focus on solving actual problems and creating real value, not just the novelty of technology itself.