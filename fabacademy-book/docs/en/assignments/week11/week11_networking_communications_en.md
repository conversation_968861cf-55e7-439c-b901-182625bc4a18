---
layout: doc
title: "Week 11: Network and Communication Technologies | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 11: Learning the fundamentals of network communications and their applications in manufacturing projects, including wired communication protocols, wireless communication technologies, and network architecture design"
head:
  - - meta
    - name: keywords
      content: fab academy, network communications, serial communication, SPI, I2C, WiFi, Bluetooth, ESP32, node networks, modular design, communication protocols
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 10: Individual Assignment: Gesture-Controlled Mini Fan and LED'
  link: '/en/assignments/week10/week10_individual_gesture_fan_led_en'
next:
  text: 'Week 11: Individual Assignment: Dual-Node Gesture-Controlled Smart Network System'
  link: '/en/assignments/week11/week11_individual_gesture_network_system_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Network and Communication Technologies
## Course Overview
This week's course explained the fundamentals of network communications and their applications in manufacturing projects. We learned about various wired communication protocols (such as serial communication, SPI, I2C, etc.) and wireless communication protocols (such as WiFi, Bluetooth, etc.). The course emphasized that network communications are not only used for long-distance connections but can also serve as an important component of system architecture for implementing modularity, parallel processing, and interference isolation.

## Detailed Course Content
### 1. Basic Concepts and Uses of Network Communications
The role of network communications in projects is not limited to connecting devices over long distances but also has several important purposes:

+ **Location Connectivity**: Connecting devices in different physical locations
+ **Parallel Processing**: Distributing tasks among multiple processors, with each processor responsible for specific functions
+ **Modular Design**: Allowing systems to be decomposed into modules for independent development, testing, and reconfiguration
+ **Interference Isolation**: Separating high-voltage/high-current sections from sensitive circuits, reducing electrical interference

### 2. Wired Communication Technologies
#### 2.1 Asynchronous Serial Communication
Asynchronous serial communication is the most basic form of communication, transmitting data through high and low voltage level changes.

**Common Standards**:

+ RS-232, RS-422, RS-485, etc.

**Three Common Serial Communication Methods**:

1. **Serial Broadcast**:
    - Uses one host and multiple nodes
    - Each node has a unique address
    - The host broadcasts to all nodes, and nodes recognize their own addresses and respond
    - Code implementation: Using software "bit-banging" can be implemented on any pin, no specialized hardware needed
2. **Hop Count Method**:
    - Nodes are connected in series (output connected to the next node's input)
    - Message count value increases as it passes through each node
    - End nodes automatically identify and send return messages
    - Suitable for devices like LED strips, no preset addresses needed
3. **Broad Hop Hybrid Method**:
    - Combines the advantages of broadcast and hop count
    - Uses broadcast for sending (quickly reaching all nodes)
    - Uses hop count for receiving (identifying node position)
    - Suitable for network architectures with one master device

#### 2.2 Synchronous Serial Communication
Synchronous serial communication uses separate data and clock lines, with the clock line indicating when to read data, providing better synchronization performance.

**Two Main Synchronous Serial Protocols**:

1. **SPI (Serial Peripheral Interface)**:
    - Uses four lines: data output, data input, chip select, clock
    - Mainly used for communication between processors and peripherals (such as storage cards)
    - Example application: SD card interface
        * Can add GB-level non-volatile storage to projects
        * Formatted with FAT file system
        * Simplified access through library functions
2. **I2C (Inter-Integrated Circuit)**:
    - Uses two lines: SCL (clock) and SDA (data)
    - Implements bidirectional communication through pull-up resistors
    - Each device requires a preset address
    - Widely used for connecting sensors and displays as peripherals

#### 2.3 Asynchronous Clock-less Protocol
**ATP (Asynchronous Token Protocol)**:

+ Designed for communication without the need for shared clocks
+ Uses token passing mechanism
+ Allows processors of different speeds to communicate with each other
+ Can stop and resume communication at any time
+ Suitable for real-time systems and heterogeneous processors

#### 2.4 Other Wired Communication Protocols
+ **USB**: Universal Serial Bus, can implement various USB profiles (keyboard, mouse, MIDI devices, etc.) using libraries
+ **Ethernet**: High-speed network communications
+ **CAN, LIN, MODBUS, DMX**: Used for industrial, automotive, and lighting control systems

### 3. Wireless Communication Technologies
#### 3.1 Wireless Communication Basics
+ **Basic Radio Components**: Oscillators, mixers, power amplifiers, low-noise amplifiers, intermediate frequency, I/Q, demodulators, baseband processors, filters
+ **Antenna Knowledge**: Resonant frequency, antenna gain, impedance matching
+ **Single-Chip Radio**: Chips that integrate all radio functions, simplifying design

#### 3.2 WiFi and Bluetooth Communication
**ESP32 Series**:

+ ESP32-C3 (around $5): Supports WiFi and Bluetooth
+ Through MicroPython, can easily implement:
    - Web server functionality
    - Web client functionality
    - Bluetooth serial communication

**Practical Application Examples**:

1. **WiFi Applications**:
    - Set the processor as an access point
    - Control LED switches through a web browser
    - Use buttons to send messages to the browser
2. **Bluetooth Applications**:
    - Connect using a mobile app or browser that supports Web Bluetooth
    - Implement wireless control and data transfer
    - Can be used for remote control and mobile interfaces

#### 3.3 Other Wireless Technologies
+ **RFID/NFC**: Using the MFRC522 module to implement near-field communication, can be used for tag identification
+ **ISM Band Radio**: Such as nRF24L01+, suitable for simple point-to-point communication
+ **LoRa**: Long-range, low-power communication, suitable for city-scale networks

### 4. Network Protocols and Structures
+ **OSI Seven-Layer Model**: Network communication structure from physical layer to application layer
+ **Wireless Frequency Regulations**: Different regions have different frequency allocations and power limitations
+ **Modulation Techniques**: Different signal modulation methods such as PCM, FSK, PSK, QAM
+ **Channel Sharing**: Multiple access technologies such as TDMA, FDMA, CSMA
+ **Internet Protocols**: Standard internet protocols such as IP, TCP/UDP, HTTP

## Assignment Requirements
### Individual Assignment:
Design, build, and connect a wired or wireless node network, with the following requirements:

1. Nodes must have a network or bus address
2. Nodes must have local input and/or output devices
3. Implement communication between nodes
4. The communication functionality can be integrated into the final project as part of a modular system

### Group Assignment:
Send messages between two projects, implementing cross-project communication.

## Learning Resources
### Wired Communication Resources
1. **Serial Communication**:
    - [RS-232, RS-422, RS-485 Standard Documents](https://pdfserv.maximintegrated.com/en/an/AN723.pdf)
    - [ATtiny412 Serial Bus Example](http://fabacademy.org/2020/labs/leon/students/adrian-torres/week14.html#hello_serial_bus)
2. **I2C and SPI**:
    - [I2C Specification](https://www.nxp.com/docs/en/application-note/AN10216.pdf)
    - [SPI Application Note](http://ww1.microchip.com/downloads/en/AppNotes/Atmel-2585-Setup-and-Use-of-the-SPI_ApplicationNote_AVR151.pdf)
    - [SD Card Specification](https://claude.ai/chat/SD/SD.pdf) and [FAT File System](https://claude.ai/chat/SD/FAT.pdf)
3. **USB Communication**:
    - [TinyUSB Library](https://github.com/hathach/tinyusb)
    - [CircuitPython HID Library](https://docs.circuitpython.org/projects/hid/en/latest)
    - [Arduino HID Library](https://www.arduino.cc/reference/en/libraries/hid-project)

### Wireless Communication Resources
1. **WiFi and Bluetooth**:
    - [ESP32-C3 Datasheet](https://www.espressif.com/en/products/socs/esp32-c3)
    - [ESP32-WROOM Datasheet](https://claude.ai/chat/ESP32/esp32-wroom-32_datasheet_en.pdf)
    - [Web Bluetooth API](https://developer.chrome.com/articles/bluetooth)
2. **RFID and Other Wireless Technologies**:
    - [MFRC522 Datasheet](https://www.nxp.com/docs/en/data-sheet/MFRC522.pdf)
    - [nRF24L01+ Datasheet](https://claude.ai/chat/nRF/nRF24L01.pdf)
    - [LoRa Alliance Official Website](https://lora-alliance.org/)
3. **Antenna and Radio Basics**:
    - [ARRL Handbook](https://www.arrl.org/shop/ARRL-Handbook) (Radio Fundamentals)
    - [Antenna Theory: Analysis and Design](https://www.wiley.com/en-us/Antenna+Theory%3A+Analysis+and+Design%2C+4th+Edition-p-9781118642061)

### Network Protocol Resources
1. **Internet Protocols**:
    - [RFC Document Repository](https://www.rfc-editor.org/)
    - [IPv4 Specification](http://www.ietf.org/rfc/rfc0791.txt) and [IPv6 Specification](http://www.ietf.org/rfc/rfc2460.txt)
    - [HTTP Specification](http://www.ietf.org/rfc/rfc2616.txt)
2. **Python Network Programming**:
    - [Python socket Library Documentation](http://docs.python.org/library/socket.html)
    - [UDP Sending Example](https://claude.ai/chat/socket/udpsnd.py) and [UDP Receiving Example](https://claude.ai/chat/socket/udprcv.py)
3. **Network Analysis Tools**:
    - [Wireshark](http://www.wireshark.org/)

By mastering these network communication technologies, you will be able to design more complex, modular projects and achieve efficient communication and collaboration between devices.