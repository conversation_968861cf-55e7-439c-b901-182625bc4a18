---
layout: doc
title: "Week 10: Output Devices | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 10: Learning about various output device principles and applications, including LEDs, displays, motors, actuators and measuring their power consumption"
head:
  - - meta
    - name: keywords
      content: fab academy, output devices, LED control, motor drivers, PWM, displays, actuators, power measurement, electrical safety, power management
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 9: Individual Assignment 2: Adding a Gesture Sensor to XIAO Extension Board'
  link: '/en/assignments/week09/week09_individual_gesture_sensor_en'
next:
  text: 'Week 10: Group Assignment: Measuring Output Device Power Consumption'
  link: '/en/assignments/week10/week10_group_power_measurement_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Week 10: Output Devices
> This document content was generated by AI based on the [course syllabus](http://academy.cba.mit.edu/classes/output_devices/index.html) and video conference subtitles that I provided to Claude 3.7 Sonnet.
>

## Course Overview
This week's course introduces various output devices and their control methods, preparing us for the upcoming machine building course. We will learn how to connect and control LEDs, displays, motors, and various actuators, as well as how to measure their power consumption. This knowledge is crucial for the Fab 2.0 era (transitioning from buying machines to making machines). The main topics covered in this course include:

1. Electrical safety basics
2. Power management and supply methods
3. Current measurement techniques
4. LED control (monochrome, RGB, and arrays)
5. Displays (LCD, OLED, TFT)
6. Motor control (DC motors, servo motors, stepper motors, brushless motors)
7. Other actuators (speakers, relays, artificial muscles, etc.)

## Detailed Course Content
### 1. Electrical Safety
Electrical safety is the primary consideration when dealing with output devices, especially as we begin to use higher voltages and currents:

+ **Effects of current on the human body**:
    - ~1 mA: Safe range
    - ~10 mA: Electric shock sensation, muscle contraction
    - ~100 mA: Potentially fatal, can cause cardiac fibrillation
+ **Human body resistance**:
    - External skin: Megaohm level
    - Internal tissues: Kiloohm level
+ **Insulation breakdown**: Approximately 1 kilovolt per millimeter
+ **Safety considerations**:
    - Power capacitors can store charge for a long time, requiring safe discharge
    - Induced back EMF (as in motors) can produce high voltages
    - Polarity protection (diodes, MOSFETs)
    - Level shifting (using MOSFETs)
    - Connector polarity design
    - Use of circuit protection components

Safety recommendations for handling power electronics: Do not work alone when tired; keep the work area tidy; maintain a calm and focused mindset; be prepared to disconnect power immediately.

Reference link: [Electrical Safety Basics](https://www.esfi.org/)

### 2. Power Management
Providing adequate power for output devices is key, with several power supply options available:

#### USB Power
+ **USB PD (Power Delivery)**: Allows USB to provide higher voltage and current
+ **USB QC (Quick Charge)**: Proprietary protocol easier to implement than USB PD
+ USB power adapters, hubs, and battery packs can be used
+ USB power modules and meters can be used for monitoring

#### Power Supply Types
+ **Switching power supplies**: Efficient but potentially noisy
+ **Linear power supplies**: Low noise but less efficient
+ **Desktop power supplies**: Adjustable voltage and current, suitable for testing

#### Batteries
+ **Lithium Polymer (LiPo) batteries**: 
    - High energy density, widely used
    - Requires dedicated charging controllers
    - Fire risk exists, should be stored properly

#### Wireless Power
+ Transfers energy over short distances through magnetic fields
+ Transmits lower power over longer distances

Reference links:

+ [Wire gauge guide](https://www.powerstream.com/Wire_Size.htm)
+ [USB power transmission](https://www.renesas.com/us/en/support/engineer-school/usb-power-delivery-02-fast-role-swap-programmable-power-supply)

### 3. Current Measurement Techniques
Understanding the power consumption of output devices is essential for selecting appropriate power supplies. There are several methods to measure current:

1. **Using desktop power supplies**: Directly read the current meter display
2. **Sense resistor method**: Connect a small resistor (e.g., 1 ohm) in series in the circuit and measure the voltage drop across it
3. **Magnetic field sensing**: Measure the strength of the magnetic field around a conductor
4. **Inductive detection**: For AC loads, coils can be used to sense changing magnetic fields

### 4. LED Control
#### 4.1 Basic LED Control
+ LEDs require current-limiting resistors (calculation method: (supply voltage - LED forward voltage) / desired current)
+ Can be controlled using Arduino or MicroPython
+ Sample code (MicroPython): 

```python
from machine import Pin
import time

led = Pin(2, Pin.OUT)  # Using GPIO 2 to connect the LED
while True:
    led.value(1)  # Turn on LED
    time.sleep(0.5)
    led.value(0)  # Turn off LED
    time.sleep(0.5)
```

#### 4.2 PWM Control of LED Brightness
PWM (Pulse Width Modulation) is an important technique for controlling LED brightness:

+ Controls brightness by rapidly switching the LED on and off (using high and low pulses)
+ The human eye perceives rapid flickering as different brightness levels
+ Duty cycle determines brightness (high duty cycle = bright, low duty cycle = dim)

Sample code (MicroPython):

```python
from machine import Pin, PWM
import time

pwm = PWM(Pin(2))  # Create PWM object on GPIO 2
pwm.freq(1000)     # Set frequency to 1000Hz

# Fade effect
while True:
    # Gradually brighten
    for duty in range(0, 65535, 1024):
        pwm.duty_u16(duty)
        time.sleep(0.01)
    # Gradually dim
    for duty in range(65535, 0, -1024):
        pwm.duty_u16(duty)
        time.sleep(0.01)
```

#### 4.3 Charlieplexing
Charlieplexing is a technique for controlling multiple LEDs with fewer pins:

+ Utilizes tri-state logic (high, low, high impedance)
+ With n pins, can control n(n-1) LEDs
+ Each pin can act as both a row and a column

Working principle:

+ Only one LED is lit at a time
+ Through rapid switching, the human eye perceives all LEDs as simultaneously lit
+ Three pin states: output high, output low, high impedance state (input mode)

Example applications: LED matrices, bar graphs, indicators

#### 4.4 RGB LED Control
RGB LEDs contain red, green, and blue LEDs that can be mixed to produce various colors:

+ Can be common anode or common cathode type
+ Mix colors by controlling the brightness of each of the three LEDs
+ Blue LEDs are typically less efficient and may require different current-limiting resistors

#### 4.5 High-Power LED Driving
For high-power LEDs:

+ Processor pins cannot directly provide sufficient current (typically limited to 20-50mA)
+ Use MOSFETs as switching elements
+ N-channel MOSFETs for low-side switching, P-channel MOSFETs for high-side switching
+ Multiple LEDs can be connected in series (total voltage drop close to power supply voltage) to reduce the required current-limiting resistors
+ Parallel multiple series LEDs to increase overall brightness

### 5. Displays
#### 5.1 LCD Displays
Basic use of Liquid Crystal Displays (LCDs):

+ Common HD44780 controller
+ Can use parallel or I2C communication (I2C requires additional adapter chips like PCF8574)
+ Note that I2C requires pull-up resistors (typically 1-10kΩ)

#### 5.2 OLED Displays
Organic Light Emitting Diode (OLED) displays:

+ Commonly uses SSD1306 controller
+ Can communicate via I2C or SPI
+ Brighter, better contrast, and lower power consumption than LCDs
+ Common resolution is 128×64 pixels

MicroPython sample code:

```python
from machine import Pin, I2C
import ssd1306

# Create I2C object
i2c = I2C(0, scl=Pin(22), sda=Pin(21))

# Create OLED object
oled = ssd1306.SSD1306_I2C(128, 64, i2c)

# Display text
oled.text("Hello World!", 0, 0)
oled.show()

# Draw graphics
oled.rect(10, 20, 30, 30, 1)  # Rectangle
oled.show()
```

#### 5.3 TFT Displays
Thin Film Transistor (TFT) displays:

+ Common controllers include ILI9341, ST7735, etc.
+ Communicate via SPI
+ Support full-color display with higher resolution
+ Suitable for scenarios requiring more complex graphics and UI

#### 5.4 E-Ink Displays
+ Non-volatile (maintain display content when powered off)
+ Lower refresh rate
+ Extremely low power consumption, suitable for battery-powered devices
+ Good readability in strong light

### 6. Motor Control
#### 6.1 DC Motors
DC motor basics:

+ Require an H-bridge driver for bidirectional control
+ H-bridge basic principle: A circuit composed of four switches (typically MOSFETs)
+ Can use integrated H-bridge chips (such as DRV8251A, TB67H451, A4950, etc.)

H-bridge operation modes:

+ Forward: Top-left and bottom-right switches closed
+ Reverse: Top-right and bottom-left switches closed
+ Braking: Both top or both bottom switches closed simultaneously
+ Coasting: All switches open

Advantages of using integrated H-bridge chips:

+ Built-in charge pump (generates high-voltage switching signals)
+ Has comparators (for hardware PWM)
+ Built-in protection features (overcurrent, overtemperature protection)

Considerations:

+ Power decoupling (using capacitors of different values)
+ Use thick circuit traces to handle high currents
+ Thermal design (heat dissipation)

#### 6.2 Speaker Control
Speakers can be viewed as special motors and can also be driven using H-bridges:

+ Use PWM to generate tones
+ Wavetable Synthesis can produce more complex sounds
+ Can play pre-recorded audio samples

Code example (tone generation):

```python
from machine import Pin, PWM
import time

speaker = PWM(Pin(15))
speaker.freq(440)  # Set frequency to 440Hz (A note)
speaker.duty_u16(32767)  # 50% duty cycle

time.sleep(1)  # Play for 1 second
speaker.duty_u16(0)  # Stop sound
```

#### 6.3 Servo Motors
Servo motor control:

+ Standard servos use 50Hz PWM signals
+ Pulse width typically between 1-2ms
+ 1ms = 0 degrees, 2ms = 180 degrees (specific values depend on servo model)
+ Continuous rotation servos can be used as wheels (pulse width controls direction and speed)

Code example:

```python
from machine import Pin, PWM
import time

servo = PWM(Pin(15))
servo.freq(50)  # Standard servo frequency is 50Hz

# Turn to 0 degrees
servo.duty_ns(1000000)  # 1ms pulse
time.sleep(1)

# Turn to 90 degrees
servo.duty_ns(1500000)  # 1.5ms pulse
time.sleep(1)

# Turn to 180 degrees
servo.duty_ns(2000000)  # 2ms pulse
```

#### 6.4 Brushless DC Motors (BLDC)
Advantages and control of brushless motors:

+ Higher efficiency, lower noise, longer lifespan
+ Typically have three-phase windings
+ Require three half-bridge drivers (or three-phase H-bridge)
+ Often controlled using Electronic Speed Controllers (ESC)
+ ESCs accept the same PWM signals as servos

Types of brushless motors:

+ Inrunner
+ Outrunner
+ Pancake
+ Gimbal motors

#### 6.5 Stepper Motors
Stepper motor control:

+ Typical step angle is 1.8 degrees (200 steps/revolution)
+ Requires two H-bridges or dedicated stepper drivers
+ Supports full-step, half-step, and microstepping
+ Step/direction interface simplifies control

Driver options:

+ Using two H-bridges (such as DRV8251A)
+ Dedicated stepper motor drivers (such as DRV8428)
+ Commercial driver modules (such as Trinamic, Pololu products)

Advantages of microstepping:

+ Smoother motion
+ Lower noise
+ Higher precision

### 7. Other Actuators
#### 7.1 Solid State Relays
Used to control AC loads:

+ Electrical isolation through optocouplers
+ Switch the high-voltage side (hot side) to ensure safety
+ Can be used to control household appliances, heating elements, etc.

#### 7.2 Artificial Muscles and Soft Actuators
Innovative actuator technologies:

+ Shape Memory Alloys (SMA)
+ Artificial muscles made from fishing line
+ Piezoelectric materials
+ Soft robotic actuators
+ Pneumatic/hydraulic systems

These technologies can provide unique motion characteristics, suitable for applications requiring mimicry of natural movements.

## Assignment Requirements
### Group Assignment
Measure the power consumption of output devices:

1. Choose an output device (LED, display, motor, etc.)
2. Measure its power consumption using appropriate methods (USB power meter, sense resistor, power supply display, etc.)
3. Record power consumption changes under different operating conditions (such as different motor speeds, different LED brightness)
4. Analyze results and discuss implications for system design

### Individual Assignment
Add an output device to your microcontroller board and program it to work:

1. Choose an output device related to your final project
2. Correctly connect it to your previously designed microcontroller board
3. Write a program to control the device
4. Document the process and show results (including circuit design, code, and demonstration video)

## Learning Resources
### Electrical Safety
+ [Electrical Safety Basics](https://www.esfi.org/)
+ [Reverse Polarity Protection Circuit Design](https://www.monolithicpower.com/designing-a-reverse-polarity-protection-circuit-part-i)
+ [Logic Level Shifting Basics](https://www.digikey.com/en/blog/logic-level-shifting-basics)
+ [Circuit Protection Components](https://www.digikey.com/en/products/category/circuit-protection/9)

### Power Management
+ [Wire Gauge Guide](https://www.powerstream.com/Wire_Size.htm)
+ [USB Power Delivery](https://www.renesas.com/us/en/support/engineer-school/usb-power-delivery-02-fast-role-swap-programmable-power-supply)
+ [Li-ion and LiPoly Battery Guide](https://learn.adafruit.com/li-ion-and-lipoly-batteries?view=all)
+ [Wireless Power Transmission Research](https://science.sciencemag.org/content/317/5834/83.full)

### LED Control
+ [Charlieplexing Technique](http://inventory.fabcloud.io/static/docs/components/Charlieplexing.pdf)
+ [NeoPixel/WS2812B Resources](https://github.com/adafruit/Adafruit_NeoPixel)
+ [NeoPixel Library in MicroPython](https://docs.micropython.org/en/latest/library/neopixel.html)

### Displays
+ [HD44780 LCD Controller](http://inventory.fabcloud.io/static/docs/datasheets/44780.pdf)
+ [SSD1306 OLED Controller](http://inventory.fabcloud.io/static/docs/datasheets/SSD1306.pdf)
+ [ILI9341 TFT Controller](http://inventory.fabcloud.io/static/docs/datasheets/ILI9341.pdf)
+ [U8g2 Display Library](https://github.com/olikraus/u8g2/)

### Motor Control
+ [Integrated Circuit H-bridges](https://www.allegromicro.com/en/Products/Motor-Driver-And-Interface-ICs.aspx)
+ [Texas Instruments Motor Drivers](http://www.ti.com/motor-drivers/overview.html)
+ [Trinamic Motor Control Products](https://www.trinamic.com/products)
+ [Pololu Motion Control Modules](https://www.pololu.com/category/9/motion-control-modules)

### Innovative Actuators
+ [Making Artificial Muscles Using Fishing Line](https://www.instructables.com/Fabricating-Fishing-Line-Artificial-Muscle-at-Home)
+ [Harvard University Soft Robotics Group](https://gmwgroup.harvard.edu/soft-robotics)
+ [Shape-Changing Fiber Research](https://news.mit.edu/2023/shape-shifting-fiber-can-produce-morphing-fabrics-1026)

### Recommended Example Projects
+ [Lingdong's OLED Output Device Project](https://fab.cba.mit.edu/classes/863.21/CBA/people/lingdong/site/16-output-device.html) - Showcases advanced applications of OLED displays, including 3D rendering and menu systems