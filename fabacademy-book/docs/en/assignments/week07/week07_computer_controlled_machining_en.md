---
layout: doc
title: "Week 7: Computer-Controlled Machining | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week Seven: Learning to fabricate large-scale items using CNC mills, understanding machine types, materials, tool selection, fixturing methods, machining strategies, and safety procedures"
head:
  - - meta
    - name: keywords
      content: fab academy, computer-controlled machining, CNC, CNC mill, ShopBot, large-scale fabrication, woodworking, cutting tools, materials, safety procedures
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 6: Individual Assignment: First PCB Design Attempt'
  link: '/en/assignments/week06/week06_individual_assignment_pcb_design_en'
next:
  text: 'Week 7: Group Assignment: CNC Cutting'
  link: '/en/assignments/week07/week07_group_assignment_cnc_cutting_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Week 7: Fab Academy 2025 Computer-Controlled Machining Course Outline
> This document content was refined by AI (Claude 3.7 Sonnet) based on the [course syllabus](http://academy.cba.mit.edu/classes/computer_machining/index.html) and transcripts of the video conference course that I provided.
>

## Course Overview
This course introduces computer-controlled machining techniques, focusing on using large CNC mills (such as ShopBot) to create large-scale items (meter-scale). The content covers machine types, processing materials, tool selection, fixturing methods, machining strategies, and safety procedures. Students will learn how to design, machine, and assemble large items, such as furniture or structural components, without using screws or glue.

## Detailed Course Content
### I. Course Introduction
This week we will learn how to create large items, involving greater forces, larger materials, and more important safety considerations. Compared to previous laser cutting, we will be working on a larger scale, typically using 4×8 feet (approximately 1.2×2.4 meters) boards as material.

### II. Successful Case Demonstrations
1. Student work showcase: Such as Sam's boat, demonstrating the possibilities achievable in this course.
2. Standing desk: A standing desk designed by a student for his girlfriend, which later developed into a commercial project.
3. Open furniture platforms: Platforms like OpenDesk and AtFab provide open-source furniture designs.
4. Housing projects: From Shelter 2.0 emergency shelters to complete WikiHouse residences, even including fully functional homes like Fab Lab House.

### III. Machine Equipment Introduction
#### 1. Small Handheld Devices
+ Shaper: Handheld mill with computer vision, can be brought to the workpiece rather than bringing the workpiece to the machine
+ Maslow: An affordable alternative using ropes and anchors instead of large gantries

#### 2. Medium-sized Equipment
+ ShopBot: The most common CNC mill, typically 4×8 feet in size, priced at $20,000-30,000
+ Onsrud: More robust machines capable of more aggressive machining, requiring less frequent adjustment, priced around $100,000

#### 3. Large/Professional Equipment
+ Haas/Hurco: Priced around $300,000, featuring multiple degrees of freedom (5-axis) movement capabilities, can machine titanium and stainless steel
+ Zund: Equipped with mobile vacuum tables and high-speed heads, used for advertising displays, racing composites, etc.
+ Self-made machines: Such as the Fellesverkstedet project, where machines can be built using parts manufactured by the machine itself

### IV. Processing Materials
#### 1. Common Materials
+ Cardboard: Triple-fold cardboard can be used to make lightweight furniture, best cut with knives rather than milled
+ Rigid foam: Used for building insulation, easy to process, can serve as molds for building casting
+ Plywood: Laminated wood, attractive but more expensive, about $100 per sheet
+ Medium Density Fiberboard (MDF): Wood fibers mixed with resin, poorer structural performance but smooth surface, suitable for applications requiring smooth surfaces
+ Medium Density Overlay (MDO): Plywood core with MDF surface, combining the advantages of both
+ Oriented Strand Board (OSB): Bonded wood chips, decent structural performance and inexpensive ($10-15 per sheet), rough surface but can be sanded and coated

#### 2. Other Materials
+ High-Density Polyethylene (HDPE): Cutting board material, easy to process
+ Polycarbonate (Lexan): High hardness, high toughness polymer, cannot be cut with ordinary lasers but can be milled
+ Garolite: Fiber-reinforced composite material, easy to process and good rigidity
+ Aluminum and aluminum composites: Such as Hylite, aluminum encasing a polymer core, lightweight and easy to process and fold

### V. Tool Selection
#### 1. Tool Types
+ Differences between drill bits and end mills: Drill bits are designed only for vertical cutting, end mills can cut laterally and vertically
+ End mills versus woodworking tools: Woodworking tools (such as router bits) are designed for 2D cutting, end mills can create any 3D shape

#### 2. End Mill Characteristics
+ Flutes: Commonly have 2, 3, or 4 flutes; more flutes produce smoother surfaces but reduced chip evacuation space
+ Coatings: Ceramic coatings can significantly extend tool life
+ Center cutting capability: Center-cutting tools can cut vertically into material, non-center cutting tools require lateral entry
+ Up-cut/down-cut: Up-cut tools leave burrs on top but facilitate chip evacuation, down-cut tools produce clean surfaces on top but leave burrs on the bottom
+ Tool head shape: Flat end mills vs. ball end mills, ball end mills are suitable for smooth curves and entering narrow spaces

### VI. Speed and Feed
+ Chip Load: The amount of material cut per flute per revolution, typically 0.001-0.010 inches. Formula: Feed rate (inches/minute)/(RPM×number of flutes)
+ Cut Depth: The depth of cutting per pass, typically around the tool diameter
+ Step-over: The overlap amount of adjacent paths, typically half of the tool diameter

### VII. Workpiece Fixturing
#### 1. Fixturing Methods
+ Vises: Professional machining vises are much more expensive than ordinary vises, with flatter, more precise surfaces
+ Bar clamps: Simple fixtures, but not suitable for large areas
+ Screws: Using electric screwdrivers and woodworking screws, can distribute fixing force
+ Special nails: Such as Raptor Nails, made of polymer, can be milled through directly
+ Vacuum tables: The preferred choice in production environments, requires regular maintenance
+ Other methods: Wedges, weights, glue, tape, or encapsulation

#### 2. Sacrifice Layer and Calibration
+ Sacrifice layer: Place a layer of material on the machine bed, mill it flat first, then fix the workpiece on top
+ Calibration: Regularly check machine accuracy to ensure cut square components are indeed square

### VIII. Bending and Joining Techniques
#### 1. Bending Techniques
+ Flexures: Cut multiple small beams, each can bend slightly, making rigid plates flexible
+ Kerfing: Not cutting completely through, retaining a continuous outer surface while allowing bending
+ Steam bending: Softening wood with steam, bending it into shape, then fixing
+ Special plywood: Unidirectional lamination, poorer structural performance but easy to bend

#### 2. Connection Techniques
+ Woodworking joints: Hundreds of traditional connection methods, such as wedge joints are a good choice, allowing for some variation in material thickness
+ Reciprocal Frame: A structural design utilizing joints to create special structures
+ Tensegrity: Combining tension elements (ropes) and compression elements (beams) to create seemingly floating structures
+ Maxwell Criterion: Calculating degrees of freedom and constraints to ensure structural stability

### IX. Tool Path Strategies
#### 1. Cutting Direction and Methods
+ Climb milling vs. conventional milling: Climb milling applies greater force to the machine but leaves cleaner cuts, conventional milling has less force but rougher surfaces
+ Roughing vs. finishing: Roughing quickly removes material, finishing produces smooth surfaces
+ 2D, 2.5D, and 3D machining: From simple 2D cutting to complete 3D surface machining

#### 2. Special Considerations
+ T-slots and dogbone shapes: Leaving extra space for joints, compensating for tool shape limitations
+ Avoiding collisions: Considering the size of tools, tool holders, and spindles, ensuring sufficient space
+ Tabs and skins: Keeping parts fixed during the cutting process, preventing parts from falling off when cutting is complete
+ Nesting: Efficiently arranging parts to maximize material utilization
+ Lead-in and lead-out: Controlling how the tool enters and exits the material
+ Test cuts and air cuts: Conducting tests before actual cutting to verify program correctness
+ Simulation and prototyping: Using software to simulate the cutting process, or making small-scale prototypes with cardboard/3D printing

### X. CAM Software
+ VCarvePro: ShopBot companion software
+ Fusion 360: Integrated CAD and CAM, seamless transition
+ FreeCAD Path: Open-source option, continuously improving functionality
+ Mods: Can perform basic contour cutting, roughing, and finishing

### XI. File Formats
+ G-code: The most universal format, but different machines may have subtle differences
+ SBP: ShopBot-specific format
+ Other proprietary formats

### XII. Safety Operations
#### 1. Common Hazards
+ Debris, cuts, burns, and impacts: Material debris is sharp, tools and materials become hot, parts may fly out
+ Fire risk: Heat generated by excessive cutting may ignite sawdust, particularly dangerous in dust collectors
+ Tool breakage: Small mill breakage is merely annoying, large tool breakage may produce high-speed fragments

#### 2. Safety Precautions
+ Personal protection: Safety goggles, appropriate footwear, no loose clothing, tied-back hair
+ Sensory alertness: Observe, listen, and smell to understand normal versus abnormal conditions
+ Correct operation: Never use hands instead of tools, do not reach into operating tools
+ Emergency stop: Know how to stop the machine before starting
+ Assistance and condition: Do not operate large equipment alone, ensure good physical and mental condition

## Assignment Requirements
### Group Assignment
1. Complete the lab's safety training
2. Test the machine's runout, alignment, fixturing, speed, feed, materials, and tool paths

### Individual Assignment
1. Design, mill, and assemble a large item (approximately meter-scale)
2. Try not to use fasteners or glue (bonus points)
3. Try to include curved surfaces (bonus points)

### Notes
+ Plan your time reasonably, don't leave all machining work for the last minute
+ Safety is the primary consideration, ensure operation under appropriate supervision

## Learning Resources
### 1. Machines and Equipment
+ [Shaper Tools](https://www.shapertools.com/)
+ [Maslow CNC](https://www.maslowcnc.com/)
+ [ShopBot](https://www.shopbottools.com/)
+ [Tormach](https://www.tormach.com/)
+ [Haas](https://www.haascnc.com/)

### 2. Material Sources
+ [McMaster-Carr](http://www.mcmaster.com/#raw-materials) (Comprehensive materials)
+ [US Plastics](https://www.usplastic.com/) (Plastic materials)
+ Local wood and metal suppliers

### 3. Connection and Design Techniques
+ [Digital Joints Reference Poster](https://www.instructables.com/50-Digital-Joints-poster-visual-reference/)
+ [Bending Technique Resources](https://www.google.com/search?q=steam+bending&tbm=isch)
+ [Maxwell Criterion](https://digital.nls.uk/scientists/archive/74629052)

### 4. Software Tools
+ [CAMotics](https://camotics.org/) (Open-source simulation)
+ [Fusion 360 CAM Tutorial](https://www.autodesk.com/products/fusion-360/blog/getting-started-introduction-to-cam-and-toolpaths/)
+ [OpenBuilds CAM](https://cam.openbuilds.com/)
+ [Deepnest](https://deepnest.io/) (Parts nesting optimization)

### 5. Safety Resources
+ [Safety Training Guide](http://ehs.mit.edu/site/training)
+ [Workshop Safety Review](http://www.popularmechanics.com/home/<USER>/yale-students-tragic-death-prompts-a-shop-safety-review)