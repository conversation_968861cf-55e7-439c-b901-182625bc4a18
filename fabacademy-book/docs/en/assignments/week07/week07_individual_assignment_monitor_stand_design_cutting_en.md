---
layout: doc
title: "Week 7: Individual Project: Monitor Stand Design and Cutting | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 7 Individual Assignment: Designing and fabricating a large monitor stand, achieving simple and effective assembly through a design without fasteners"
head:
  - - meta
    - name: keywords
      content: fab academy, monitor stand, furniture design, Fusion 360, parametric design, CNC cutting, board processing, fastener-free design
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 7: Group Assignment: CNC Cutting'
  link: '/en/assignments/week07/week07_group_assignment_cnc_cutting_en'
next:
  text: 'Week 8: Electronics Production'
  link: '/en/assignments/week08/week08_electronics_production_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Week 7 Individual Project: Monitor Stand Design and Cutting

The requirement for this class is to design, mill, and assemble a large item (approximately meter-scale), and attempt to do so without using fasteners or glue (bonus points). I decided to create a wide monitor stand for my 1.6m × 0.6m adjustable desk.

![](/images/week07/w7-p-1.jpg)

> The appearance of my 1.6m × 0.6m adjustable desk. I want to add a monitor stand on the desk to make more effective use of desktop space
>

## Fusion 360 Design Process
Design goal: For a 1.6m × 0.6m computer adjustable desk, design a 1.5m × 0.25m monitor stand with a height of 0.12m.

**Parametric Design**: Through parametric design, adjustments can be easily made to accommodate processing and fitting issues later, corresponding to different board thicknesses.

### Step One: Basic Drawing and Parametric Design
1. Create reference desktop
    - Enter the sketch environment, create a basic 2D sketch in the top view
    - Design a rectangle with dimensions of 1600mm × 250mm as the top plate base
    - Add R30.00 rounded corners to the four corners of the rectangle to make the edges more aesthetically pleasing and safe

The sketch design is shown below.

![](/images/week07/w7-p-2.png)

> Creating a reference adjustable desktop sketch using parametric design
>

2. Then extrude the reference desktop body. I also specified a wood texture effect, as shown below.

![](/images/week07/w7-p-3.png)

> Extruding the adjustable desktop to evaluate the effect of the monitor stand
>

### Step Two: Top Plate Drawing and Feature Addition
1. Set key parameters for the top plate sketch
    - Length: 1500mm
    - Width: 250mm
    - Board thickness: 18mm (according to CNC processing specifications)
    - Corner radius: R25.00 (four corners)
2. Refine the sketch, add necessary connection holes and positioning holes
    - Draw multiple rectangular holes (40mm × 18mm) at both ends of the top plate for support slot connections. Before cutting, Engineer Xu adjusted the rectangular holes to 46mm × 18mm to facilitate smooth installation
    - Set symmetry constraints to ensure balanced design
    - Add guide lines and positioning points at the center position
3. Add dimension markings
    - Total top plate length: 1500mm
    - Total width: 250mm
    - Connection slot length: 40mm
    - Connection slot width: 18mm

![](/images/week07/w7-p-4.png)

> Parametrically designed top plate sketch, with the initial design having embedded board slot holes of 40mm × 18mm
>

According to actual processing requirements, I adjusted the width of the square slot holes from the original 40mm to 46mm to allow the board material to be inserted smoothly.

![](/images/week07/w7-p-5.png)

> The modified slot holes adjusted to 46×18 mm, and the slot hole depths on the left and right sides were also adjusted to 43mm
>

4. Extrude the top plate
    - Select the main body sketch
    - Use the "Extrude" command to create a solid
    - Set extrusion thickness: 18mm
    - Apply appropriate material (wood texture)

![](/images/week07/w7-p-6.png)

> Render of the extruded top plate
>

### Step Three: Support Design
1. Create support sketch
    - Draw the support cross-section on an appropriate work plane (xz)
    - Design the support shape, creating 2 teeth to stably support the top plate
    - Support teeth width: 40mm
    - Support teeth height: 18mm
    - Support width: 150mm
    - Support height: 120mm
    - Support internal corner radius: R20.00

![](/images/week07/w7-p-7.png)

2. Extrude the support
    - Extrude the support sketch to an appropriate thickness
    - Copy the support, evenly distribute a total of 7 supports at the bottom of the top plate
    - Ensure reasonable spacing between supports to provide sufficient support

![](/images/week07/w7-p-8.png)

### Step Four: Overall Effect
The final completed effect is shown below. The entire structure is connected through board inlays, requiring no additional nails.

![](/images/week07/w7-p-9.png)

> Rendering of the final desktop monitor stand
>

![](/images/week07/w7-p-10.png)

> You can see the traces of reserved width for easy insertion in the rendering
>

## Exporting for Processing Preparation
1. Confirm the correct positioning and dimensions of each component.
2. Export the design sketches (top plate and supports) as DXF files for CNC processing.

![](/images/week07/w7-p-11.png)

> Right-click on the sketch to export the support DXF
>

Now I have 2 DXF files ready for cutting.

![](/images/week07/w7-p-12.png)

> Top plate and support DXF files exported from Fusion sketches
>

Next, I combined the two DXF files in Adobe Illustrator and arranged the 7 supports needed.

![](/images/week07/w7-p-13.png)

> Combining DXF files into one in Adobe Illustrator, and copying and arranging the required number of supports for actual cutting
>

I finally exported a new DXF file.

## MasterCAM X6 Processing Preparation
In MasterCAM, under the guidance of Engineer Xu Guoqun, we performed the following steps:

1. **Import Design Files**: Import DXF files and define the board space, as shown below.

![](/images/week07/w7-p-14.png)

> Importing DXF
>

2. **Layout Processing Design Files and Tool Paths**: This part of the work is relatively complex, mostly operated by Engineer Xu while we observed.

Setting up the tool path contour

![](/images/week07/w7-p-15.png)

> Setting up the tool path contour
>

![](/images/week07/w7-p-16.png)

> Setting up the tool, including key parameters such as tool diameter 8mm, feed rate 5000, spindle speed 15000, etc.
>

![](/images/week07/w7-p-17.png)

> Setting cutting parameters
>

![](/images/week07/w7-p-18.png)

> Common parameter settings for tool contour milling
>

Tool paths also need to be managed in sequence, arranging the cutting paths according to the "inside to outside, small to large" principle introduced earlier.

![](/images/week07/w7-p-19.png)

> Planning the tool cutting paths
>

By simulating the tool path, details of the cutting process can be checked. Note that the tool lines for holes that need to be emptied move inward from the lines, while the tool lines for cutting outer contours are outward from the lines. As shown in the simulated tool path below, the cutting process is performed in layers.

![](/images/week07/w7-p-20.png)

> Layered tool cutting path simulation
>

![](/images/week07/w7-p-21.png)

> The tool cutting process can be simulated in 3D mode
>

3. **Set Zero Point**: After completing the layout, set the processing zero point.
4. **Generate Processing Files**: After setting the correct parameters, generate the final [G-Code](https://en.wikipedia.org/wiki/G-code) processing file.

![](/images/week07/w7-p-22.png)

> Final G-Code file for CNC cutting my desk
>

## CNC Cutting Process
During the cutting process, we followed these steps:

1. **Place the Processing Board**
2. **Secure the Board Material**
3. **Adjust the Processing Zero Point**
4. **Turn on the Air Treatment Equipment**
5. **Confirm Processing Settings**
6. **Monitor the CNC Cutting Process**

**Small parts that lose connection with the main board during the cutting process should be promptly paused (press F10 key), wait for the blade to stop rotating, then remove them, otherwise they may easily damage displaced parts on the last lap.**

![](/images/week07/w7-p-23.jpg)

> Need to promptly clean small parts that detach from the main board material. Note that moving active parts should be done after pausing and when the drill bit has stopped rotating
>

7. **Clean the Processing Area**

## Assembling the Monitor Stand
After cutting all the parts, I assembled them:

1. **Check the Parts**
2. **Final Cutting Effect**
3. **Inner Corner Adjustment**
4. **Assembly Connection**: Use a rubber hammer to knock the adjusted board materials together to complete the product.

![](/images/week07/w7-p-24.jpg)

> Printed computer desk parts
>

![](/images/week07/w7-p-25.jpg)

> Because of the 3mm allowance above and below, the supporting legs easily fit into the top plate
>

![](/images/week07/w7-p-26.jpg)

> After installation, I tested the support capability, and it's very sturdy
>

## Results Display
I filled the gaps between the stand and the top plate with a hot glue gun to make the support firmly bond with the top plate.

![](/images/week07/w7-p-27.jpg)

> Filled the seams with a hot glue gun. The first piece of furniture I designed and manufactured in my life is complete
>

I'm very satisfied with the actual use effect.

![](/images/week07/w7-p-28.jpg)

> It was put into use immediately, placed on my desktop
>

## Design Source Files
Get my monitor stand Fusion design file: [https://a360.co/41OZWJv](https://a360.co/41OZWJv)

Download DXF files and G-Code zip file: [Lei-Feng-desk-dxf-nc.zip](/images/week07/Lei-Feng-desk-dxf-nc.zip)

## Experience Summary
1. **Design Considering Processing Limitations**: When designing, consider the characteristics of CNC processing, avoiding structures that are difficult to process such as inner corners.
2. **Importance of Tool Selection**: Quality tools are essential for successful cutting; soft tools easily break, leading to work interruptions.
3. **Advantages of Parametric Design**: Through parametric design, it's easy to adapt to different material thicknesses and adjust dimensions.
4. **Inner Hole Design**: For small square inner holes that need to be inlaid with board material, the width should be increased by 3mm on both sides to ensure the board material can be inserted.
5. **Safety First**: Throughout the process, safety remains the primary consideration, including wearing proper protective equipment and understanding emergency stop procedures.