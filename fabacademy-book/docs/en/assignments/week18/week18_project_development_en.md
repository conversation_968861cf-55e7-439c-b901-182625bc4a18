---
layout: doc
title: "Week 18: Project Development and Applications Outlook | Feng Lei Fab Academy 2025"
description: "Fab Academy 2025 Week 18 course, covering final project planning, project management principles, and reviewing application prospects of learned skills."
head:
  - - meta
    - name: keywords
      content: fab academy, project development, applications outlook, final project, course notes, week 18
  - - meta
    - name: author
      content: <PERSON>
aside: true
outline: deep
prev:
  text: 'Week 17 Assignment: Smart Fantasy Lantern'
  link: '/en/assignments/week17/week17_wildcard_week_project_en'
next:
  text: 'Week 19: Invention, Intellectual Property and Income'
  link: '/en/assignments/week19/week19_invention_intellectual_property_income_en'
---

# Week 18: Project Development and Applications Outlook

> This document was generated by providing Claude 3.7 Sonnet with the [course outline](http://academy.cba.mit.edu/classes/applications_implications/index.html) content and the subtitles from the course's video conference sessions, which were then refined by AI.
>

## Course Overview
This week's course marks the transition from learning individual skills to integrating these skills to complete the final project. The core content includes final project planning, key principles of project management, and reviewing the broad application areas where learned skills can be applied, preparing for the final "masterpiece." Students need to begin specifically planning their final projects and start preparing materials needed for project presentations.

## Detailed Course Content

### Part 1: Applications Outlook (Reviewing Application Areas of Learned Skills)
This section aims to inspire students' final project ideas by showcasing examples of how learned skills are applied in different fields. These skills are not just independent; when integrated, they can create impactful projects.

Here are some areas where Fab Lab skills can be applied:

+ **Medical**: For example, during the pandemic, the Fab Lab network played an important role in rapid prototyping and production of personal protective equipment (PPE), material testing, etc.
+ **Electronics**: Students have learned extensive electronics fabrication and can commercialize these skills, such as Modern Device company selling electronic products they developed.
+ **Test Equipment**: For example, students can make their own oscilloscopes and other testing tools instead of purchasing them.
+ **Consumer Electronics**: For example, making a complete boombox.
+ **Phone**: Early Fab Lab projects included cases of making functional phones.
+ **Terminal**: For example, making a computer terminal that can connect to remote servers.
+ **Laptop**: For example, Bunnie Huang's open-source laptop, or Nadia Peek's open architecture laptop designed for easy experimentation and feature addition.
+ **Network**: For example, the FabFi project, which uses Fab Lab equipment to make parabolic antennas in areas lacking telecommunications infrastructure (such as Afghanistan, Kenya), achieving long-distance (such as 10km) wireless network connections, even building city-wide internet.
+ **Satellite**: CubeSat is a standardized nanosatellite platform, many of whose components can be manufactured in Fab Labs, making personal space projects increasingly feasible. Fab 25 even plans to have satellite workshops.
+ **Machines**: Students have completed machine-making week assignments; one of the core skills is manufacturing your own Fab Lab equipment.
+ **Materials**: For example, the Materiom project, which uses common items like coffee grounds, eggshells, and seaweed to create open-source material libraries and recipes.
+ **Robots**: For example, Will Langford's beautiful bouncing robot, or Otherlab's soft robots and assistive exoskeletons made using inflatable structures.
+ **Inflatables**: Otherlab uses sewing to make large inflatable robots and explores ways to drive them.
+ **Boats**: For example, Sam Calisch's kayak made with composite materials.
+ **Bicycles**: For example, Kenny MacCarthy's composite bicycle frame.
+ **Skis**: There are multiple Fab Lab projects for making skis.
+ **Drones**: For example, Danielle Ingrassia making all aspects of drones from scratch, including controllers.
+ **Cars**: Barcelona Fab Lab had the ambitious Fab Car (electric vehicle) project, demonstrating rapid prototyping capabilities for automobiles.
+ **Environment**: For example, the Smart Citizen project, developed from Fab Academy's environmental sensing projects, conducting regional data collection through citizen science and impacting urban planning.
+ **Energy**: For example, using ShopBot to make wind turbines for power generation.
+ **Agriculture/Food**: For example, the Aquapioneers project (aquaponics system), or AgriLab focused on building tools for smart agriculture. Fab Labs can also make tools for biological laboratories.
+ **Biology**: Fab Labs can be used to manufacture many tools needed for biological experiments, such as pipetting robots, PCR machines, centrifuges, etc.
+ **Microscope**: For example, Manu Prakash's Foldscope, a folding microscope costing only $1, which has had tremendous global impact.
+ **Scientific Instruments**: For example, OpenFlexure Microscope or open-source versions of Raman spectrometers, aimed at democratizing the manufacture of advanced scientific instruments.
+ **Prosthetics**: There are Fab Care groups focused on Fab Lab manufacturing of assistive technology and open-source prosthetics projects.
+ **Shoes**: For example, making high-performance athletic shoes through mold casting, with soles designed combining soft and hard materials.
+ **Clothes**: For example, the Iterate project, an open-source knitting machine for making your own clothing.
+ **Toys**: For example, beautiful interactive toys made by Vogue Fab Lab, emphasizing surface finishing and aesthetics.
+ **Art**: Fab Lab technology can be combined with traditional crafts, such as Haystack Mountain School of Crafts integrating digital manufacturing into glass, printmaking, and other artistic creations.
+ **Musical Instruments**: For example, a series of beautiful instruments made by Alex from Vogue, such as bass guitars. Iceland has also made many guitars.
+ **Furniture**: For example, a Fab Lab in a mixed community in Israel where children designed and made their own furniture, activating community space.
+ **Houses**: One of the most ambitious projects is Barcelona Fab Lab's Fab Lab House, a complete solar house including interior furniture.
+ **Labs**: Danielle Ingrassia's Open Lab Starter Kit project provides a complete set of open-source designed Fab Lab machines. Fab Labs themselves can be upgraded through homemade furniture and equipment.
+ **Communities**: For example, Blair Evans' leadership of a Fab Lab in Detroit, playing a leading role in building community around the lab, using the lab as an engine for community transformation.
+ **Cities**: The Fab City initiative, originating from Barcelona, aims to promote cities using Fab Labs to achieve self-sufficiency, producing what they consume, moving from smart cities to manufacturing cities.
+ **Economies**: Fab Labs fundamentally change how economies operate by localizing means of production, affecting how people live, learn, work, and play.

### Part 2: Final Project Planning and Implementation
The final project is a "masterpiece" that demonstrates students' mastery of skills learned in the course.

#### 1. The True Meaning of a "Masterpiece"
The word "masterpiece" originally did not refer to great works of art, but to works created by guild members to prove they had mastered the guild's skills. Therefore, Fab Academy's final project is not required to be a life's work or world-changing invention, but should clearly demonstrate students' comprehensive application and proficient mastery of skills covered in the course. Its core lies in skill integration.

#### 2. Key Questions for Planning Your Final Project
Students need to create a dedicated website for their final project and begin answering the following questions:

+ **What will it do?** Define the project's functionality.
+ **Who's done what beforehand?** Conduct background research to understand related work.
+ **What sources will you use?** List literature, code libraries, designs, etc., that the project will rely on.
+ **What will you design?** Clarify the personally original design parts.
+ **What materials and components will be used?**
+ **Where will they come from?** Sources of materials and components.
+ **How much will they cost?** Estimate costs.
+ **What parts and systems will be made?**
+ **What processes will be used?** Manufacturing processes involved.
+ **What questions need to be answered?** Key questions the project explores and solves.
+ **How will it be evaluated?** Establish criteria for project success.
+ **Schedule for completion** Need to create a detailed completion timeline.

#### 3. Core Requirements for the Final Project
The final project must demonstrate comprehensive application of the following skills:

+ **2D and 3D design**.
+ **Additive and subtractive fabrication processes**: This is not just milling circuit boards and 3D printing.
+ **Electronics design and production**.
+ **Embedded microcontroller design, interfacing, and programming**.
+ **System integration and packaging**.
+ **Make rather than buy** parts in the project.
+ Projects can be completed individually or collaboratively, but must demonstrate personal mastery of skills and be able to operate independently.

### Part 3: Elements of Project Management
In the final sprint phase of project development, the following project management principles are crucial:

+ **Murphy's Law**: Not "things will go wrong, so it's not my fault," but "anything that can go wrong will go wrong, so you need to foresee and prevent it." It's not an excuse, but requires foresight.
+ **80/20 Principle**, or more like 95/5 rule: 20% of work takes 80% of time (or 5% of work takes 95% of time). Those things you think will be simple at the end actually take most of the time.
+ **Triage**: You need to judge which parts can be completed and which cannot, and decisively abandon parts that cannot be completed. Don't worry about simple things; focus on parts that need attention.
+ **Documentation during development**: You don't have time to supplement documentation at the end; the only way to complete it is to document as you go.
+ **Demand- vs. supply-side time management**: You can't expect to end the project by completing tasks; you must plan your time supply well, arrange how to use time in advance, and create detailed schedules.
+ **Spiral development, DevOps**: Don't try to complete everything at once, but first complete a small version of the project, then add features, continuously iterating back to a completed project version.
+ **Serial vs. parallel tasks**: Don't let all tasks proceed serially, because blocking of one task affects all subsequent tasks; try to make tasks progress in parallel.
+ **System integration**: Don't forget you need to design the system's integration plan.
+ **Finish quality**: The final project should go beyond rough laser-cut boxes, demonstrating good surface finishing, design ergonomics, etc. Using the same tools, you can make rough or exquisite items.

## This Week's Assignment Requirements
This week's assignment requires students to begin laying a solid foundation for the final project:

1. **Final Project Site and Planning Draft**:
    - Establish your final project page.
    - Initially answer all key questions listed in "Part 2: Final Project Planning and Implementation" on that page.
2. **Presentation Materials Draft**:
    - Prepare a draft of the final project **summary slide (presentation.png)**.
        * Format: PNG image (presentation.png).
        * Size: 1920x1080 pixels.
        * Content: Clearly show who you are and what your project is, as a visual thumbnail.
    - Prepare a draft of the final project **video clip (presentation.mp4)**.
        * Format: MP4, using HTML5 compatible encoding (such as H.264, avoid H.265).
        * Resolution: 1080p.
        * Duration: About 1 minute (draft can be very short, even a few seconds, mainly to test the process).
        * Size: Final video approximately < 25MB (roughly 10MB/minute).
3. **File Placement and Link Checking**:
    - Place `presentation.png` and `presentation.mp4` files in the root directory of your student repository (i.e., the directory containing your homepage `index.html`).
    - Check that these files are correctly linked to the final presentation schedule (usually updates automatically, but confirmation needed).
    - **Important Note**: The slides and video submitted this week are drafts/placeholders, aimed at ensuring smooth process flow, and can be updated and replaced with final versions at any time.

## Resources and Past Project Reviews
+ **Past Project Examples**:
    - Numerous final project cases can be found on the [Project Development course page](Project Development.html).
    - Many excellent final projects from previous years were also shown and discussed in this course's recorded lectures, which can serve as inspiration and reference.
+ **Final Presentation Schedule**: Check if your slide and video links appear in the [Final Presentation Schedule](http://fabacademy.org/2024/schedule.html) (link is an example, please find the correct link for the current year).

Please make full use of Saturday's open time and Monday's tutorial time to actively advance your project and prepare for the final presentation.