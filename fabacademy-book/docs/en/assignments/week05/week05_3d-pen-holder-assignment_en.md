---
layout: doc
title: "Week 5: 3D Printed Spiral Pen Holder | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 5 Extra Assignment: Design and 3D print a spiral pen holder"
head:
  - - meta
    - name: keywords
      content: fab academy, 3D printing, pen holder, Fusion 360, Bambu Lab A1 Combo, PETG
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 5: 3D Printed Diatom Structure'
  link: '/en/assignments/week05/week05_individual_diatom_en'
next:
  text: 'Week 6: Electronics Design'
  link: '/en/assignments/week06/week06_electronics_design_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
---

# Week 5: 3D Printed Spiral Pen Holder

## Project Overview
This project is about designing and making a hollow spiral pen holder. I used Fusion 360 to create a parametric spiral structure, then used array and boolean operations to complete the hollow design. The final print was made using a Bambu Lab A1 Combo 3D printer. This project demonstrates the complete workflow of 3D parametric design and manufacturing.

## Materials & Tools
+ Design software: Autodesk Fusion 360
+ Slicer: Bambu Studio
+ 3D Printer: Bambu Lab A1 Combo 3D (0.4mm nozzle)
+ Filament: Bambu PETG Translucent (light blue)

## Detailed Workflow
### 1. Design Concept
The goal was to create a pen holder that is both practical and visually appealing. I chose a spiral structure as the main design element because it:
+ Provides sufficient structural strength
+ Creates unique visual effects
+ Reduces material usage
+ Showcases the advantages of 3D printing

Design parameters:
+ Diameter: 75mm
+ Height: 85mm
+ Wall thickness: 2mm
+ Spiral density: 0.25 turns/height
+ Number of hollow elements: 36

### 2. Fusion 360 Modeling Steps
#### 2.1 Create Spiral Structure
1. Open Fusion 360 and create a new design
2. Click "Create" > "Coil"
3. Select XY plane as the base
4. Set parameters:
    - Diameter: 75mm
    - Turns: 0.25
    - Height: 85mm
    - Section: Square
    - Section size: 2mm (inner)
5. Press Enter to finish

![](/images/week05/w05-p3-1.png)

> Create a square-section spiral solid according to the parameters.
>

#### 2.2 Mirror
1. Click "Create" > "Mirror"
2. Select the spiral as the object
3. Choose the bottom plane as the mirror plane
4. Set operation to "Join"
5. Confirm

![](/images/week05/w05-p3-2.png)

> Select the square section as the mirror plane.
>

#### 2.3 Circular Pattern
1. Click "Create" > "Pattern" > "Circular Pattern"
2. Select the spiral as the object
3. Select Z axis as the pattern axis
4. Set quantity to 36
5. Confirm

![](/images/week05/w05-p3-2-2.png)

> Use pattern to quickly form the mesh.
>

#### 2.4 Combine Spirals
1. Select all spirals
2. Click "Modify" > "Combine"
3. Click OK to finish
4. Rename as "Spiral Structure", temporarily hide

![](/images/week05/w05-p3-3.png)

> Merge the mesh into a single body.
>

#### 2.5 Create Base and Top
1. Click "Create" > "Cylinder"
2. Select XY plane, click origin
3. Set diameter to 75mm, height -2mm (downward)
4. Use "Move" to copy, set Z to 87mm for the top disk

![](/images/week05/w05-p3-4.png)

> Create the bottom and copy for the top disk.
>

#### 2.6 Shell Operation
1. Select the top disk
2. Click "Modify" > "Shell"
3. Select top and bottom faces
4. Set inner thickness to 2mm
5. Confirm

![](/images/week05/w05-p3-5.png)

> Use shell to open the top disk.
>

#### 2.7 Combine All Components
1. Show the hidden spiral structure
2. Select all components
3. Click "Modify" > "Combine"
4. Confirm

![](/images/week05/w05-p3-6.png)

> Merge the mesh and top/bottom into one body.
>
Get the design source file: [https://a360.co/42Xdxhh](https://a360.co/42Xdxhh)

### 3. Prepare Print File
1. Export STEP from [Fusion 360](https://www.autodesk.com/products/fusion-360/)
2. Open Bambu Studio
3. Import STEP file
4. Check model integrity
5. Set print parameters:
    - Nozzle: 0.4mm
    - Layer height: 0.2mm
    - Infill: 15%, grid
    - Support: None (design doesn't need support)
6. Preview slicing, print time ~2 hours

![](/images/week05/w05-p3-7.png)

> Preview slicing in Bambu Studio.
>
Download the STEP ZIP file: [vase.step.zip](/fabacademy-book/docs/public/images/week05/vase.step.zip)

### 4. 3D Printing
1. Prepare printer:
    - Clean platform
    - Use PETG Translucent
    - Confirm nozzle & bed temp (auto)
2. Start print, monitor first layer
3. Print time ~2h 53min
4. Wait for cooldown, remove print

![](/images/week05/w05-p3-8.jpg)

> Print just finished.
>

### 5. Post-processing
1. Clean bottom burrs
2. Check spiral joints and strength
3. Test stability and usability

## Results & Analysis
### Showcase
The final hollow spiral pen holder:

![](/images/week05/w05-p3-9.jpg)

### Design Analysis
1. **Strength**: Spiral structure provides solid support.
2. **Visual**: Hollow design creates light/shadow effects.
3. **Material**: Hollow design saves ~65% material.
4. **Print Quality**: Layer lines visible but acceptable.
5. **Practicality**: Holds multiple pens, functional.

## Summary & Reflection
### Successes
1. Used parametric design for complex spiral
2. Efficient use of array and boolean ops
3. No support needed, less material waste
4. Good balance of aesthetics and function

### Improvements
1. Add weight to base for stability
2. Try different infill for strength/material balance
3. Explore spiral parameter variations
4. Try gradient/multicolor PLA for aesthetics

### Learnings
This project deepened my understanding of parametric design in 3D modeling. Spiral structures seem complex, but with proper parameters and tools, they can be efficiently created and modified. 3D printing makes such complex hollow shapes easy to produce, which is hard for traditional manufacturing.

### References
+ [Fusion 360 Coil Tool Guide](https://help.autodesk.com/view/fusion360/ENU/?guid=SLD-COIL-SOLID)
