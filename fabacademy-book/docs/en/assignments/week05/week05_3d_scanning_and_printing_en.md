---
layout: doc
title: "Week 5: 3D Scanning and Printing | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 5 Learning Guide for 3D Scanning and Printing Technologies"
head:
  - - meta
    - name: keywords
      content: fab academy, 3D printing, 3D scanning, photogrammetry, additive manufacturing, PLA, PETG, stereolithography
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 4: AI-based XIAO MG24 Sense Application Development'
  link: '/en/assignments/week04/week04_individual_assignment_ai_xiao_mg24_sense_app_en'
next:
  text: 'Week 5: Group Assignment: 3D Printer Design Rules Testing'
  link: '/en/assignments/week05/week05_group_assignment_3d_printer_test_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---
# Week 5: 3D Scanning and Printing Technologies

> This document was generated by AI synthesis after I provided [Grok 3](https://grok.com/chat/) with the course outline link [http://academy.cba.mit.edu/classes/scanning_printing/index.html](http://academy.cba.mit.edu/classes/scanning_printing/index.html) and the transcript from the video conference session for this course.
>

### Summary:
**Key Points:**  

+ This guide is based on the Fab Academy 3D Scanning and Printing course, suitable for beginners, covering the fundamentals of 3D printing and scanning.  
+ It includes material selection (such as PLA and PETG), design rules, printing and scanning techniques, related software, and assignments.  
+ Surprisingly, 3D printing not only creates complex shapes but also reduces waste and is suitable for manufacturing nested components.

#### What are 3D Printing and Scanning?
3D printing (additive manufacturing) is a method of creating three-dimensional objects by adding material layer by layer, different from traditional subtractive manufacturing (cutting away material). 3D scanning is the process of capturing the shape of real objects and converting them into digital data.  

+ Advantages of 3D printing include handling complex designs, reducing waste, and being suitable for nested component manufacturing.  
+ Scanning technologies such as photogrammetry and laser scanning help create digital models.

#### Materials and Safety
Common materials include PLA (easy to print but brittle, glass transition temperature around 60°C) and PETG (more durable, around 80°C).  

+ Safety note: Printers may release particles and volatile organic compounds, requiring ventilation; food-contact items need safe materials.

#### Design and Technology
Design considerations include support structures, wall thickness, and surface finish. Printing technologies include FDM (Fused Deposition Modeling), SLA (Stereolithography), and more. Scanning technologies include photogrammetry (rebuilding models from multiple photos) and laser scanning (measuring distances with lasers).

#### Software and Assignments
Use software like MeshLab (mesh processing) and PrusaSlicer (slicing) to prepare prints. Assignments include designing and printing an object that cannot be made through subtractive manufacturing, and scanning an object (with optional printing).

---

### Detailed Report
This guide is based on the Fab Academy course "3D Scanning and Printing" outline ([http://academy.cba.mit.edu/classes/scanning_printing/index.html](http://academy.cba.mit.edu/classes/scanning_printing/index.html)) and the transcript from Professor Neil's video, aimed at providing comprehensive and accessible guidance for beginners. The following detailed content covers the course's core topics and technical details.

#### Course Background and Overview
Fab Academy is a course focused on digital manufacturing, with the "3D Scanning and Printing" module exploring the basic principles, materials, design rules, workflows, and applications of additive manufacturing and three-dimensional scanning. The course content is based on Neil Gershenfeld's lectures, combining interactive discussions and practical assignments, emphasizing hands-on capabilities and sustainability.

#### Fundamentals of 3D Printing
3D printing (additive manufacturing) creates three-dimensional objects by adding material layer by layer, contrasting with subtractive manufacturing (cutting from a block of material). Its main advantages include:  

+ **High Complexity at Low Cost**: Can easily handle complex geometric shapes that might be expensive with traditional methods.  
+ **Nested Component Access**: Allows printing of mutually nested components without additional assembly.  
+ **Near-Net Shape Manufacturing**: Printed objects are close to the final shape, reducing post-processing requirements.  
+ **Reduced Waste**: Adds material only where needed, more environmentally friendly than subtractive manufacturing.

The transcript mentions that 3D printing limitations include resolution, time cost, and material limitations, but these have minimal impact on beginners' understanding of the overall concept.

#### Material Selection and Safety Considerations
Material selection for 3D printing is crucial for print quality and application. Common materials include:  

+ **PLA (Polylactic Acid)**: Derived from renewable resources (such as corn starch), easy to print but relatively brittle, with a glass transition temperature of about 60°C.  
+ **PETG (Polyethylene Terephthalate Glycol-Modified)**: Petroleum-based, easily recyclable, more durable than PLA, with a glass transition temperature of about 80°C and better UV resistance.  
+ **Composite Materials**: Such as metal or wood-filled materials, enhancing strength or changing appearance.  
+ **Other Polymers**: ABS, HIPS, TPU, PVA, each with specific properties suitable for different applications.

**Safety Considerations**:  

+ Printing processes may release ultrafine particles and volatile organic compounds (VOCs), requiring good ventilation or the use of certified printers.  
+ For food-contact items, materials meeting safety standards must be selected, as emphasized in the transcript.  
+ Material storage also requires attention, as some materials (like PLA) are highly hygroscopic and need to be stored dry.

#### Design Rules and Best Practices
When designing 3D print models, follow these rules to ensure successful printing:  

+ **Support Structures**: Overhanging parts may need support to prevent collapse.  
+ **Unsupported Angles**: Different printers and materials have different limits for unsupported angles, generally recommended to be less than 45 degrees.  
+ **Wall Thickness**: Walls must be thick enough to ensure stability, but excessive thickness wastes material; 1-2mm is recommended as a starting point for beginners.  
+ **Anisotropy**: Due to layer-by-layer printing, the mechanical properties of printed items may vary along different axes, which needs consideration during design.  
+ **Surface Finish**: Layer lines may affect surface smoothness, which can be improved through post-processing (such as sanding).  
+ **Infill Rate**: Internal fill structures can be adjusted to balance strength and material use; beginners can start with a 20% infill rate.

The transcript mentions that design rule testing is part of the group assignment, emphasizing the importance of practice.

#### 3D Printing Processes
3D printing technologies are diverse, each suited for different applications:  

+ **Fused Deposition Modeling (FDM/FFF)**: Builds by extruding molten material layer by layer, suitable for beginners and desktop printers like Ultimaker or Prusa.  
+ **Stereolithography (SLA)**: Uses lasers to cure liquid resin, suitable for high-precision models like jewelry designs.  
+ **Selective Laser Sintering (SLS)**: Laser-sinters powder materials, suitable for functional parts, no support needed.  
+ **Binder Jetting**: Sprays binder onto a powder bed to form objects, suitable for rapid prototyping.

The transcript mentions other processes like two-photon nanolithography and bioprinting, suitable for advanced applications, but beginners can focus on FDM and SLA.

#### 3D Scanning Technologies
3D scanning is the process of converting real objects into digital models, with common techniques including:  

+ **Photogrammetry**: Reconstructs 3D models from multiple photos, using software like Meshroom or Polycam, suitable for beginners.  
+ **Laser Scanning**: Uses lasers to measure distances, generating point cloud data, suitable for high-precision needs like artifact preservation.  
+ **Structured Light Scanning**: Projects light patterns and captures deformation, suitable for medium-precision applications such as face scanning.  
+ **LIDAR (Light Detection and Ranging)**: Uses laser pulses to measure distances, common in large environment scanning like architectural measurements.

The transcript mentions tools like OpenKinect, Skanect, and Scaniverse, emphasizing the impact of lighting, background, and surface treatment on results during scanning.

#### Software Tools and Workflow
3D printing and scanning require various software support:  

+ **Mesh Processing Software**: Such as MeshLab, netfabb, meshmixer, used to process scan data or CAD models, converting to STL format.  
+ **Slicing Software**: Such as PrusaSlicer, Cura, Slic3r, slicing 3D models into print layers, generating G-code.  
+ **Print Control Software**: Such as Printrun, OctoPrint, Repetier, used to control printer operation.  
+ **Firmware**: Such as Klipper, optimizing printer performance.  
+ **Sharing Platforms**: Such as Sketchfab, Thingiverse, Printables, for sharing and viewing 3D models.

The transcript mentions the practical use of these tools, emphasizing that beginners should start by familiarizing themselves with PrusaSlicer and MeshLab.

#### Assignments and Practice
The course includes the following assignments to help beginners practice what they've learned:  

+ **Group Assignment**: Test the design rules of 3D printers, such as support angles, wall thickness, etc., and record the results.  
+ **Individual Assignment**:  
    - Design and 3D print a small object (a few cubic centimeters, considering print time limitations) that cannot be completed through subtractive manufacturing, such as internal cavity structures.  
    - 3D scan an object, optionally print the scan result, practicing photogrammetry or laser scanning techniques.

The transcript mentions that assignments also include preparation for AI design and embedded processing discussions, suitable for extended learning.

#### Summary and Further Reading
This guide provides beginners with a comprehensive introduction to 3D printing and scanning, covering material selection, safe design, workflows, and practical assignments. The transcript content shows that the course emphasizes interaction and practice, with Professor Neil highlighting sustainability and innovative applications such as bioprinting and augmented reality (SLAM).  

Beginners can refer to the following resources for deeper learning:  

+ Fab Academy Course Outline ([http://academy.cba.mit.edu/classes/scanning_printing/index.html](http://academy.cba.mit.edu/classes/scanning_printing/index.html))  
+ FabCloud Inventory List ([http://inventory.fabcloud.io/?purpose=3D%20Scanning%20and%20Printing](http://inventory.fabcloud.io/?purpose=3D%20Scanning%20and%20Printing))  
+ PrusaSlicer Official Page ([https://www.prusa3d.com/page/prusaslicer_421](https://www.prusa3d.com/page/prusaslicer_421))  
+ MeshLab Official Website ([http://www.meshlab.net/](http://www.meshlab.net/))

#### Key References
+ [Fab Academy 3D Scanning and Printing Course Outline](http://academy.cba.mit.edu/classes/scanning_printing/index.html)  
+ [FabCloud 3D Scanning and Printing Inventory List](http://inventory.fabcloud.io/?purpose=3D%20Scanning%20and%20Printing)  
+ [PrusaSlicer Official Download and Documentation](https://www.prusa3d.com/page/prusaslicer_421)  
+ [MeshLab Mesh Processing Software Official Website](http://www.meshlab.net/)