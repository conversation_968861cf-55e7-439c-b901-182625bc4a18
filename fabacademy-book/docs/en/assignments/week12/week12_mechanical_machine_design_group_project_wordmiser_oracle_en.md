---
layout: doc
title: "Week 12: Mechanical and Machine Design Group Project: Cross-border AI Writing Machine - The WordMiser Oracle | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 12 Mechanical and Machine Design Group Project, introducing the design, implementation, and team collaboration process of the cross-border AI writing machine (The WordMiser Oracle)."
head:
  - - meta
    - name: keywords
      content: fab academy, Fab Lab, mechanical design, machine design, AI writing machine, cross-border collaboration, SenseCAP Watcher, MQTT, Arduino, Gcode
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Fab Ecosystem Lecture Notes'
  link: '/en/assignments/week12/week12_fab_ecosystem_lecture_en'
next:
  text: 'Week 12: Mechanical and Machine Design Group Project: Cross-border AI Writing Machine - The WordMiser Oracle Personal Contribution'
  link: '/en/assignments/week12/week12_wordmiser_oracle_personal_contribution_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Mechanical and Machine Design Group Project: Cross-border AI Writing Machine - "The WordMiser Oracle"

See this week's group assignment: [https://fabacademy.org/2025/labs/chaihuo/docs/week12/week12_group_assignment/](https://fabacademy.org/2025/labs/chaihuo/docs/week12/week12_group_assignment/)

<iframe width="560" height="315" src="https://www.youtube.com/embed/sVUsoqN4CzQ" frameborder="0" allowfullscreen></iframe>

## Project Overview
"The WordMiser Oracle" is a cross-border collaborative project that connects the SenseCAP Watcher AI assistant at Chaihuo Makerspace in Shenzhen with a mechanical writing machine at the Macau Science Center, creating a unique interactive experience.

The core idea of the project is to create a "WordMiser" AI oracle, which must limit its answers to 25 characters and have them physically written out by the mechanical writing machine. This design aims to remind people that in an age of information explosion, concise expression is often more valuable than lengthy content.

The project achieves real-time communication between the two locations via the MQTT protocol:

+ **Shenzhen Side**: Uses the SenseCAP Watcher AI assistant to receive user questions and generate concise answers.
+ **Macau Side**: Designs a mechanical writing machine to receive AI-generated text and physically write it on paper.

![](/images/week12/w12-g-1.png)

> System architecture of The WordMiser Oracle
>

### Team Members
+ [**Chon Kit Kuok**](https://fabacademy.org/2025/labs/chaihuo/students/chonkit-kuok/docs/week12/week12_mechanical_machine_design/): Responsible for writing machine hardware and programming
+ [**Long Wai Chan**](https://fabacademy.org/2025/labs/chaihuo/students/longwai-chan/docs/week_assignment/week12/machine_design/): Responsible for mechanical structure design
+ [**Hongtai Liu**](https://fabacademy.org/2025/labs/chaihuo/students/hongtai-liu/docs/assignments/week12/01-individual-contribution/): Responsible for SenseCAP Watcher AI programming
+ [**Lei Feng**](/en/assignments/week12/week12_wordmiser_oracle_personal_contribution_en): Responsible for MQTT text-to-G-code converter design

## Phase One: Machine Design and Construction
In the first phase, our goal was to build a basic writing/drawing machine capable of receiving text or images and performing writing/drawing.

### Machine Structure Selection
After researching and comparing different machine structures, we chose the **Cartesian coordinate robot structure** because the X and Y axes are controlled separately, making it easier to control and program.

### Hardware Components
To save time and focus on improvement and application, we assembled the machine using as many existing resources in the lab as possible:

| Component | Specification | Quantity |
| --- | --- | --- |
| Aluminum profile | 20 × 20 × 25cm | 2 |
| Aluminum profile | 20 × 40 × 35cm | 2 |
| Aluminum profile | 20 × 40 × 29.7cm | 2 |
| Stepper motor | Nema 17 | 3 |
| Microcontroller | Arduino Nano | 1 |
| Stepper motor driver | A4988 | 2 |
| Arduino CNC Shield | Rev2.7 | 1 |

![](/images/week12/w12-g-2.jpg)

> Assembling the hardware of the writing machine
>

Machine design_v1
<iframe src="https://a360.co/4cJhU4b" width="700" height="500"></iframe>


### Firmware and Control
We used GRBL as the firmware to control the CNC machine. GRBL is a popular open-source firmware for controlling CNC machines with Arduino. It interprets G-code commands and controls the stepper motors accordingly.

#### Steps to Upload GRBL Firmware
1. Download GRBL firmware from GitHub
2. Add the library to Arduino IDE
3. Upload the firmware to the Arduino board

#### Connecting the CNC Shield
According to the wiring diagram, we connected the stepper motors, servos, and limit switches to the CNC shield, then connected a 12V power supply to drive the stepper motors.

![](/images/week12/w12-g-3.jpg)

> CNC shield wiring diagram
>

![](/images/week12/w12-g-5.jpg)

> Using `G-codePlot` extension to convert images to G-code
>

We then tested whether the relevant G-code could run on the machine.
![](/images/week12/w12-g-6.jpg)

> G-code simulation in the UGS interface
>

[test_drawing.mp4](/images/week12/test_drawing.mp4)

![](/images/week12/w12-g-7.jpg)

## Phase Two: AI Integration and Communication
We integrated the SenseCAP Watcher AI assistant and implemented the following features:

+ Offline speech recognition
+ Local inference and command parsing
+ Access to online large language models
+ MQTT communication extension

#### MQTT Configuration
We configured the MQTT client:

+ Server address: `broker.emqx.io`
+ Topic: `fablab/chaihuo/machine/text`
+ Port: `1883`
+ Authentication (username/password): None

#### AI Response Handling
After the AI generates a response, we wrap it in a standard format and publish it to a specific topic via MQTT.

### MQTT Text-to-G-code Conversion
We developed a Python program to convert the received text into G-code and send it to the GRBL controller. The main functions include:

1. **MQTT Client**:
    - Subscribes to the specified topic and receives text messages
2. **Text-to-G-code Converter**:
    - Converts text into G-code instructions
    - Supports English letters, numbers, and some punctuation
3. **G-code Sender**:
    - Sends G-code to the GRBL controller via serial port
4. **Status and Error Handling**:
    - Manages program status and error handling
5. **Configuration and CLI**:
    - Provides full configuration options via `config.yaml`
    - Supports precise control of pen height parameters and feed rate

MQTT G-code converter program GitHub repo: [MQTT-text-G-code-GRBL](https://github.com/mouseart/MQTT-text-Gcode-GRBL)

When running the project program, if the MQTT content "Life is a journey of growth." is received, the following output can be seen in the programming environment:

```cpp

--- MQTT Message Received ---
Topic: fablab/chaihuo/machine/text
Raw Payload: b'Life is a journey of growth.'
Decoded Text Payload: Life is a journey of growth.
Checking Serial Port: ser=Exists, is_open=True
Converting text to G-code...
Warning: Character '.' not found in definitions. Skipping.
Processing 377 G-code line(s) generated from text:
[G-code->GRBL] G0 F1500.0 X0.00 Y6.04        
[GRBL<-] ok
[G-code->GRBL] M03 S150
[GRBL<-] ok
[G-code->GRBL] G1 F1500.0 X0.00 Y0.00
[GRBL<-] ok
[G-code->GRBL] G1 F1500.0 X3.56 Y0.00
[GRBL<-] ok
[G-code->GRBL] M05 F1500.0
[GRBL<-] ok
    
...Omit intermediate content
    
[GRBL<-] ok
[G-code->GRBL] G1 F1500.0 X122.24 Y0.00
[GRBL<-] ok
[G-code->GRBL] M05 F1500.0
[GRBL<-] ok
[G-code->GRBL] G0 F1000.0 X123.74 Y0.00
[GRBL<-] ok
Finished processing G-code from message.
Sending post-message G-code (input did not end with newline)...
[G-code->GRBL] G1 F500.0 X0.00 Y-8.00
[GRBL<-] ok
Successfully sent post-message command: G1 F500.0 X0.00 Y-8.00
--- End MQTT Message Processing ---
Disconnected from MQTT broker with reason code Unspecified error
Successfully connected to MQTT broker with reason code Success
Subscribed to topic: fablab/chaihuo/machine/text

```

## Project Demonstration
The project workflow is as follows:

1. At Chaihuo Makerspace in Shenzhen, a user asks the AI via SenseCAP Watcher: What's the meaning of life?

![](/images/week12/w12-g-9.jpg)

2. The AI generates a short answer (limited to 25 characters): "Life is a journey of growth." and sends it via MQTT.

![](/images/week12/w12-g-10.jpg)

3. The program at Macau Science Center receives the text and converts it to G-code.

![](/images/week12/w12-g-11.jpg)

4. The writing machine executes the G-code instructions and physically writes the answer on paper.

![](/images/week12/w12-g-12.jpg)

## Project Summary and Reflection
The "WordMiser Oracle" project is not only a technical challenge but also an exploration of "how to rethink the value of communication in the digital age." Through the relatively "slow" medium of physical writing, we hope to remind people that sometimes "less is more" in this age of information explosion, and concise expression may be more valuable than lengthy content.

The project successfully achieved cross-border technical collaboration, integrating AI, IoT, and mechanical control technologies to create a unique interactive experience.

### Future Improvements
1. Support drawing more characters and symbols
2. Optimize the drawing path algorithm to improve writing speed and quality
3. Develop a more user-friendly interface to enhance system usability
4. Add more error recovery mechanisms to improve system stability in complex environments
5. Improve the hardware structure to increase the writing area

## Personal Contributions
### [Chon Kit Kuok](https://fabacademy.org/2025/labs/chaihuo/students/chonkit-kuok/docs/week12/week12_mechanical_machine_design/)
+ Writing machine hardware and programming
+ MQTT data receiver

### [Long Wai Chan](https://fabacademy.org/2025/labs/chaihuo/students/longwai-chan/docs/week_assignment/week12/machine_design/)
+ Mechanical structure design
+ Hardware connection and upgrades

### [Hongtai Liu](https://fabacademy.org/2025/labs/chaihuo/students/hongtai-liu/docs/assignments/week12/01-individual-contribution/)
+ SenseCAP Watcher AI programming

### [Lei Feng](/en/assignments/week12/week12_wordmiser_oracle_personal_contribution_en)
+ MQTT text-to-G-code converter design
    - Text-to-G-code conversion
    - MQTT transmission
+ Project video directing and post-production

## References
+ GRBL: [https://github.com/robottini/grbl-servo](https://github.com/robottini/grbl-servo)
+ Universal G-Code Sender: [https://winder.github.io/ugs_website/](https://winder.github.io/ugs_website/)
+ G-codePlot: [https://github.com/arpruss/G-codeplot](https://github.com/arpruss/gcodeplot)
+ SenseCAP Watcher: [https://www.seeedstudio.com/watcher](https://www.seeedstudio.com/watcher)
+ XIAOZHI AI: [https://github.com/78/xiaozhi-esp32](https://github.com/78/xiaozhi-esp32)
+ MQTT-text-G-code-GRBL: [https://github.com/mouseart/MQTT-text-G-code-GRBL](https://github.com/mouseart/MQTT-text-Gcode-GRBL)
