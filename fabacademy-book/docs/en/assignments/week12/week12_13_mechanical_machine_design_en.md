---
layout: doc
title: "Week 12-13: Mechanical & Machine Design | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 12-13 Group Assignment: Collaboratively design and build a mechanical structure and automated machine, covering mechanical principles, structural assembly, motion control, and teamwork."
head:
  - - meta
    - name: keywords
      content: fab academy, group assignment, mechanical design, machine design, mechanical structure, automation, teamwork, motion control, mechanical principles
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 11: Individual Assignment - Dual-Node Gesture-Controlled Smart Network System'
  link: '/en/assignments/week11/week11_individual_gesture_network_system_en'
next:
  text: 'Week 12: Fab Ecosystem Lecture'
  link: '/en/assignments/week12/week12_fab_ecosystem_lecture_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Mechanical & Machine Design

## Course Overview
This section focuses on the core principles and practical methods of mechanical design and machine automation. The course is divided into two main parts: the first introduces the design principles and key components of mechanisms; the second explores the drive, control, and integration technologies required to turn mechanisms into automated machines. This week's assignment is a group project, requiring students to collaborate to design and build a functional machine, demonstrating the full process from mechanism design to automation control.

## Detailed Course Content
### 1. Basic Concepts and Principles
#### 1. Definition of a Machine
A complete machine consists of four key elements:

+ Mechanism: The structural part that enables motion
+ Actuation: The power source that provides motion
+ Automation: The system that controls motion
+ Application: The actual use of the machine

#### 2. Fundamentals of Material Mechanics
+ Stress-strain curve: The behavior of materials under force
    - Stress: The external force applied ("stress sounds like press")
    - Strain: The material's response ("strain sounds like pain")
    - Linear region: Elastic deformation, reversible
    - Nonlinear region: Plastic flow, permanent deformation
    - Failure point: Ultimate strength of the material
+ Key parameters:
    - Stiffness: Slope of the curve
    - Strength: Failure point
    - Toughness: Area under the curve

#### 3. Structural Design Principles
+ Constraints and Degrees of Freedom (DoF): According to James Clark Maxwell's theory
    - Structures need appropriate constraints to prevent unwanted motion
    - At the same time, retain necessary degrees of freedom
    - Example: A bookshelf wobbles without cross-bracing; adding bracing stabilizes it

#### 4. Backlash and Hysteresis
+ Hysteresis and Backlash:
    - Gaps in mechanical joints cause positional inaccuracy when changing direction
    - Example: The position error when a nut rotates back and forth on a screw

#### 5. Force Loops
+ Force Loops: The path of force transmission in a machine
    - Example: In a milling machine, from the cutting tool to the workpiece through all connections
    - Precision depends on the cumulative error in the force loop
    - Design principle: Minimize the force loop to reduce error accumulation

#### 6. Kinematic Coupling
+ Kinematic Coupling: High-precision positioning method
    - Uses combinations of spheres and grooves to achieve micron-level positioning
    - Three balls and three grooves yield only one stable position, ensuring repeatable alignment

#### 7. Precision and Accuracy
+ Precision: Repeatability, how closely results cluster
+ Accuracy: How close results are to the target value
+ The ideal machine has both high precision and high accuracy

### 2. Machine Materials and Components
#### 1. Common Materials
+ Plastics: e.g., HDPE
+ Metals: e.g., aluminum profiles
+ Rubber and foam: For energy absorption
+ Garolite: Machinable PCB material
+ Wood: Not recommended for precision machines (sensitive to temperature/humidity)
+ Cement: Provides stiffness and mass
+ Ceramics: For high-hardness parts

#### 2. Fasteners
+ Nuts and bolts:
    - Plain washer: Distributes load
    - Lock washer: Prevents loosening
    - Lock nut: Built-in elastomer prevents loosening
+ Other fastening methods:
    - Heat set inserts: Threaded connections in 3D-printed parts
    - Rivets: Quick, permanent connections
    - Pins: Prevent movement, limit travel

#### 3. Frame Design
+ Profile frames:
    - Aluminum profiles: Low cost, high stiffness
    - T-slots: Easy to mount components
    - Accessories: Sliding nuts, angle brackets, etc.
+ Self-aligning connections:
    - Machined parts can be designed with self-aligning features
    - Snap-fit connections enable accurate assembly
    - Advantages: Detachable, low assembly error

### 3. Transmission Systems
#### 1. Gear Systems
+ Involute gears:
    - Most common gear type
    - Feature: Single-point contact during meshing, smooth motion
    - Use gear generators for accurate tooth profiles
+ Other gear types:
    - Cycloidal gears: Easier to machine but less efficient
    - Helical gears: Smoother, quieter operation
    - Herringbone gears: Self-aligning, eliminate axial forces
+ Reduction systems:
    - Planetary gears: Compact reducers
    - Harmonic drives: High reduction ratio, used in robot joints

#### 2. Linear Transmission
+ Rack and pinion:
    - Converts rotary motion to linear motion
    - Can be custom-made to any length
+ Lead screw:
    - Ordinary lead screws have backlash
    - Anti-backlash nut: Spring preload reduces gap
    - Ball screw: Circulating balls reduce friction, smoother motion
+ Non-threaded linear drive:
    - Uses three heads on hardened rails for stable linear motion
+ Belt drive:
    - Reinforced timing belts are inextensible but flexible
    - Used to distribute force in machines
+ Capstan drive:
    - Uses wire or fishing line as the transmission element
    - Tensioning and winding increase friction
    - Advantages: Flexible shape, easy to customize

#### 3. Guide Systems
+ Guide shafts: Hardened steel rods for sliding guidance
+ Guide rails: Linear bearings move along rails
+ Slides: Simple guides for low-precision applications

#### 4. Bearings and Couplers
+ Bearing types:
    - Rotary bearing: Most common
    - Thrust bearing: For vertical loads
    - Linear bearing: For linear motion
    - Turntable bearing: For rotary loads
    - Sleeve bearing: For light loads
+ Bearing preload:
    - Apply slight axial force to bearings
    - Ensures good contact between balls and grooves, reduces noise and vibration
+ Couplers:
    - Connect motors to transmission components
    - Accommodate slight angular misalignment
    - Reduce vibration and noise

### 4. Mechanism Design
#### 1. Flexure Mechanisms
+ Flexures:
    - Use material elasticity for smooth motion
    - Suitable for precision applications with limited travel
    - Example: Fine focusing mechanism of open-source microscopes

#### 2. Series Elastic Transmission
+ Series elastic:
    - Add spring elements between motor and load
    - Control force instead of position for smoother motion
    - Similar to the way human muscles work

#### 3. Linkage Mechanisms
+ Various linkage combinations: Convert between different motion types
+ Pantograph: Amplifies or reduces motion

#### 4. Special Mechanisms
+ Delta robot: Three linear motions combine to create 3D movement
+ Hexapod: Six linear actuators enable full 6-DoF movement
+ CoreXY: Two fixed motors coordinate XY-plane motion
    - Advantage: Low moving mass, fast speed
    - Principle: Both motors rotate in the same direction for X, opposite for Y
+ Folding mechanisms (SARS): Use folding plates for linear motion
+ Hang printer: Uses cables for 3D spatial positioning
+ Art and mechanism design:
    - Chuck Hoberman's deployable structures
    - Theo Jansen's wind-powered Strandbeests

### 5. Machine Automation
#### 1. Drive and Control
+ Motor types:
    - Stepper motor
    - Servo motor
    - Geared motor
+ Control methods:
    - Open-loop control: No feedback, position estimated by motor steps
    - Closed-loop control: Encoder feedback for actual position

#### 2. Control Theory
+ Bang-bang control:
    - Simple on/off control, motion is not smooth
    - Sudden acceleration/deceleration, prone to overshoot
+ PID control:
    - Proportional: Error correction
    - Integral: Corrects accumulated error
    - Derivative: Suppresses rapid changes
+ Model predictive control:
    - Uses machine models to predict future behavior
    - Can use physical models or machine learning
    - Suitable for complex, high-precision systems

#### 3. Machine Networking
+ Centralized control: Single controller manages all motors and sensors
    - Advantage: Simple
    - Disadvantage: Poor scalability, hard to modify
+ Distributed control:
    - Each part of the machine is networked in real time
    - Each motor is independently controlled and coordinated via network
    - Advantage: Modular, easy to expand and modify

#### 4. Machine Instructions and Interfaces
+ G-code: Traditional CNC machine instructions
    - Long history, still widely used
    - Interpreters convert G-code to motor control signals
+ User interfaces:
    - Functions: Visualize G-code, edit instructions, real-time control, set origin
    - Open-source options: UGS, CNC.js, Chili Pepper, Candle, etc.
+ Path planning:
    - Converts design files to machine instructions
    - Includes edge detection, tool diameter compensation, direction control, etc.

### 6. Modular Machine Design
+ Modular concept:
    - Independent functional modules combine to form different machines
    - Interchangeable end effectors
+ Kinematic coupling for tool changing:
    - Use balls and grooves for precise tool changes
    - One machine can perform multiple functions (printing, cutting, milling, etc.)

### 7. Open-Source Machine Examples
+ Open-source machines in Fab Labs:
    - Rumbo: Simplified machine control, motors connect directly to USB
    - Modular Things: Modular system based on real-time networking
    - Jubilee: Modular machine with tool changing
    - Fellow Machines: Series of open-source machines
    - Open Lab Starter Kit: Complete open-source Fab Lab machine kit
+ Commercial spin-offs:
    - Shaper Tools, Ultimaker, Form Labs, etc.
    - Startups originating from Fab Labs

## Assignment Requirements
This week's assignment is a group project. Requirements:

1. Machine Design & Automation:
    - Design mechanical mechanisms and implement automation control
    - Test mechanisms manually before adding motor control
2. Teamwork:
    - Collaborate on mechanism design, motor control, end effectors, user interface, etc.
    - Each lab can build a machine, or collaborate across labs
3. Documentation:
    - Create a group page documenting the entire machine project
    - Personal pages document individual contributions
    - Include demo videos and detailed explanations
4. Project Presentation:
    - Prepare a short demo (~1 minute)
    - Plan for a global presentation in two weeks

## Learning Resources
### Suppliers & Materials
1. Mechanical parts suppliers:
    - McMaster-Carr: [https://www.mcmaster.com](https://www.mcmaster.com) (comprehensive docs)
    - Stock Drive Products: [https://sdp-si.com](https://sdp-si.com) (industrial supplier)
    - Amazon (various industrial suppliers)
    - Misumi: [https://us.misumi-ec.com](https://us.misumi-ec.com) (mechanical components)
    - Taobao (China)
2. Motors & Control:
    - Stepper and servo motor suppliers
    - Controllers: PLUS, Rumba, Modular Things, etc.

### Reference Projects
1. Mechanical references:
    - Kentucky Lab's clock project
    - Nadia and Jonathan's self-aligning joints
    - Yen's custom rack and pinion
    - Quentin's capstan drive
2. Open-source machine projects:
    - Reference: [https://mtm.cba.mit.edu/machines/](https://mtm.cba.mit.edu/machines/)
3. Control system references:
    - G-code interpreter: GRBL: [https://github.com/gnea/grbl](https://github.com/gnea/grbl)
    - User interfaces:
        * Universal Gcode Sender: [https://winder.github.io/ugs_website/](https://winder.github.io/ugs_website/)
        * CNC.js: [https://cnc.js.org](https://cnc.js.org)
        * Chili Pepper
        * Candle: [https://github.com/Denvi/Candle](https://github.com/Denvi/Candle)
    - Path planning: Mods: [https://mods.cba.mit.edu](https://mods.cba.mit.edu)
4. Creative references:
    - Joseph's "Bad Machines"
    - Simone Giertz's creative machines: [https://www.youtube.com/c/simonegiertz](https://www.youtube.com/c/simonegiertz)

### Further Learning Resources
+ Mechanisms:
    - 507 Mechanical Movements: [http://507movements.com](http://507movements.com)
    - KMODDL - Kinematic Models for Design Digital Library: [https://digital.library.cornell.edu/collections/kmoddl](https://digital.library.cornell.edu/collections/kmoddl)
+ Control systems:
    - PID Control Guide: [https://cllom.gitlab.io/mynotes/PID](https://cllom.gitlab.io/mynotes/PID)
    - Model Predictive Control (MPC): [https://ocw.mit.edu/courses/16-323-principles-of-optimal-control-spring-2008/resources/lec16/](https://ocw.mit.edu/courses/16-323-principles-of-optimal-control-spring-2008/resources/lec16/)
+ Fab Labs open-source machine projects:
    - MTM Snap: [https://mtm.cba.mit.edu/2011/2011_mtm-snap/mtm_snap-lock/index.html](https://mtm.cba.mit.edu/2011/2011_mtm-snap/mtm_snap-lock/index.html)
    - Jubilee: [https://jubilee3d.com](https://jubilee3d.com)
    - Modular Things: [https://github.com/modular-things/modular-things](https://github.com/modular-things/modular-things)
+ Industrial design references:
    - Open Source Ecology: [https://www.opensourceecology.org](https://www.opensourceecology.org)
    - RepRap Project: [https://reprap.org](https://reprap.org)

With these resources, students can design and build their own machines, from simple mechanisms to complex automated systems, realizing the Fab Labs' core goal of "making your own tools."