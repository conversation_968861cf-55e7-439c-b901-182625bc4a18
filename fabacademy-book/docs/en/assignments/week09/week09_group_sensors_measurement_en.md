---
layout: doc
title: "Week 9: Group Assignment: Measuring Grove Sensors with an Oscilloscope | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 9 Group Assignment: Using the OWON EDS102CV oscilloscope to measure signal characteristics of the Grove DHT11 temperature and humidity sensor and ADXL345L three-axis accelerometer, analyzing sensor working principles and communication protocols"
head:
  - - meta
    - name: keywords
      content: fab academy, group assignment, OWON oscilloscope, Grove sensors, DHT11, temperature humidity sensor, signal analysis, digital communication, single-bus protocol
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 9: Input Devices'
  link: '/en/assignments/week09/week09_input_devices_en'
next:
  text: 'Week 9: Individual Assignment: Adding an Ultrasonic Distance Sensor to XIAO Expansion Board'
  link: '/en/assignments/week09/week09_individual_assignment_ultrasonic_sensor_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---
# Week 9 Group Assignment: Measuring Grove Sensors with an Oscilloscope

[[View the complete group assignment]](https://fabacademy.org/2025/labs/chaihuo/docs/week9/chaihuo/week9_group_assignment)

## Task Overview

According to the requirements in the [Week 9 course outline](http://academy.cba.mit.edu/classes/input_devices/index.html), our group needs to select input devices, measure their electrical signal characteristics using an oscilloscope, and analyze and understand their working principles. In this assignment, we will:

1. Test XIAO ESP32C3 with Grove DHT11 temperature and humidity sensor (written by Lei Feng)
2. Test XIAO ESP32S3 with ADXL345L three-axis accelerometer (written by Liu Hongtai)

## XIAO ESP32C3 with Grove DHT11 Temperature and Humidity Sensor Tutorial

I used the OWON EDS102CV oscilloscope for measurements. This is a dual-channel digital storage oscilloscope with a sampling rate of up to 1GS/s and a bandwidth of 100MHz, suitable for measuring and analyzing various electronic signals.

![](/images/week09/w9-g-1.jpg)

> OWON EDS102CV oscilloscope

**Main Technical Specifications**:

+ Bandwidth: 100MHz
+ Number of channels: 2
+ Maximum sampling rate: 1GS/s
+ Timebase range: 2ns/div - 100s/div
+ Vertical sensitivity: 2mV/div - 10V/div
+ Display: 8-inch color LCD

For oscilloscope function descriptions, refer to [Katherine](https://fabacademy.org/2024/labs/chaihuo/students/runyu-ma/index.html)'s documentation: Week12. [Input Devices](https://fabacademy.org/2024/labs/chaihuo/students/runyu-ma/assignments/IndividualAssignments/week12.html).

### Grove DHT11 Temperature and Humidity Sensor Introduction

![](/images/week09/w9-g-2.jpg)

[Grove DHT11](https://wiki.seeedstudio.com/Grove-TemperatureAndHumidity_Sensor/) is a basic digital temperature and humidity sensor manufactured by Seeed Studio, designed for entry-level electronic projects. Here are its main features:

#### Basic Specifications

+ **Measurement Range**: Temperature 0-50°C, Humidity 20-90%RH
+ **Accuracy**: Temperature ±2°C, Humidity ±5%RH
+ **Resolution**: Temperature 1°C, Humidity 1%RH
+ **Supply Voltage**: 3.3V-5.5V
+ **Data Output**: Single-bus digital signal

#### Features

+ **Grove Interface**: Designed with standard Grove interface, no soldering required, plug and play
+ **Low Power Consumption**: Average operating current 0.5mA, standby current about 100μA
+ **Small Size**: Small sensor module size, easy to integrate into various projects
+ **Signal Transmission Distance**: Up to 20 meters under standard conditions
+ **Long-term Stability**: Calibration coefficients stored in OTP memory, ensuring long-term stable operation

#### Working Principle

The DHT11 contains a resistive humidity measurement element and an NTC temperature measurement element. After the sensor collects environmental data, it outputs digital signals to the microcontroller through a single-bus protocol. A complete data packet contains 40 bits of data, including humidity integer part, humidity decimal part, temperature integer part, temperature decimal part, and checksum.

#### Communication Protocol

DHT11 uses a simplified single-bus communication protocol:

1. **Host Sends Start Signal**: Pulls the data line low for at least 18ms, then pulls it high for 20-40μs
2. **Sensor Response**: Pulls the data line low for 80μs, then high for 80μs
3. **Data Transmission**: Each data bit starts with a 50μs low-level signal, followed by a high-level signal whose duration determines whether the data bit is "0" or "1"
   - "0": High level lasts 26-28μs
   - "1": High level lasts 70μs

#### Application Scenarios

+ **Home Automation Projects**: Indoor environment monitoring
+ **Weather Stations**: Simple weather monitoring
+ **Educational Projects**: Arduino, Raspberry Pi, and other microcontroller learning
+ **Plant Care**: Indoor plant growth environment monitoring
+ **Simple Temperature and Humidity Display**: Combined with LCD/OLED displays

#### Advantages and Disadvantages

**Advantages**:

+ Low cost, suitable for beginners
+ Easy to use, rich code libraries
+ Grove interface facilitates rapid prototyping

**Disadvantages**:

+ Relatively low accuracy, not suitable for high-precision scenarios
+ Limited measurement range, not suitable for extreme environments
+ Slow update rate, recommended measurement interval ≥2 seconds

This sensor is an ideal choice for beginners learning about input devices and sensor communication protocols, particularly suitable for educational environments like the MIT Fab Academy input devices course.

## Materials Preparation

+ XIAO ESP32C3 development board
+ Grove DHT11 temperature and humidity sensor
+ OWON EDS102CV oscilloscope
+ Oscilloscope probe
+ Connection wires
+ USB data cable
+ Computer (with Arduino IDE installed)

## Part One: Hardware Connection

1. **Connect DHT11 to XIAO ESP32C3**
   - Connect Grove DHT11's VCC pin to XIAO ESP32C3's 3.3V
   - Connect Grove DHT11's GND pin to XIAO ESP32C3's GND
   - Connect Grove DHT11's DATA pin to XIAO ESP32C3's D2 (digital pin 2)

The wiring diagram is shown below.

![](/images/week09/w9-g-3.png)

> Wiring diagram of XIAO ESP32C3 and Grove DHT11

2. **Connect the Oscilloscope Probe**
   - Connect the oscilloscope CH1 probe's tip to DHT11's DATA pin
   - Connect the oscilloscope CH1 probe's ground clip to the circuit's GND
3. **Connect XIAO ESP32C3 to Computer**
   - Use a USB data cable to connect XIAO ESP32C3 to the computer

The connected devices are shown below.

![](/images/week09/w9-g-4.jpg)

> Live image of the actual device connections

## Part Two: Software Setup

1. **Install Necessary Libraries**
   - Open Arduino IDE
   - Go to "Tools" > "Manage Libraries"
   - Search for and install "DHT sensor library" and "Adafruit Unified Sensor" libraries
   - Search for and install "Seeed XIAO ESP32C3" board support (if not already installed)
2. **Basic Code Writing**
   - Create a new Arduino sketch
   - Copy the following code:

```cpp
// Example testing sketch for various DHT humidity/temperature sensors
  // Written by ladyada, public domain
  
  // REQUIRES the following Arduino libraries:
  // - DHT Sensor Library: https://github.com/adafruit/DHT-sensor-library
  // - Adafruit Unified Sensor Lib: https://github.com/adafruit/Adafruit_Sensor
  
  #include "DHT.h"
  
  #define DHTPIN D2   // Digital pin connected to the DHT sensor
  
  #define DHTTYPE DHT11   // DHT 11
  
  DHT dht(DHTPIN, DHTTYPE);
  
  void setup() {
    Serial.begin(9600);
    Serial.println(F("DHTxx test!"));
  
    dht.begin();
  }
  
  void loop() {
    // Wait a few seconds between measurements.
    delay(2000);
  
    // Reading temperature or humidity takes about 250 milliseconds!
    // Sensor readings may also be up to 2 seconds 'old' (its a very slow sensor)
    float h = dht.readHumidity();
    // Read temperature as Celsius (the default)
    float t = dht.readTemperature();
    // Read temperature as Fahrenheit (isFahrenheit = true)
    float f = dht.readTemperature(true);
  
    // Check if any reads failed and exit early (to try again).
    if (isnan(h) || isnan(t) || isnan(f)) {
      Serial.println(F("Failed to read from DHT sensor!"));
      return;
    }
  
    // Compute heat index in Fahrenheit (the default)
    float hif = dht.computeHeatIndex(f, h);
    // Compute heat index in Celsius (isFahreheit = false)
    float hic = dht.computeHeatIndex(t, h, false);
  
    Serial.print(F("Humidity: "));
    Serial.print(h);
    Serial.print(F("%  Temperature: "));
    Serial.print(t);
    Serial.print(F("°C "));
    Serial.print(f);
    Serial.print(F("°F  Heat index: "));
    Serial.print(hic);
    Serial.print(F("°C "));
    Serial.print(hif);
    Serial.println(F("°F"));
  }
```

3. **Upload Code**
   - Select the correct development board: "Tools" > "Board" > "Seeed Studio XIAO Series" > "Seeed Studio XIAO ESP32C3"
   - Select the correct port: "Tools" > "Port" > Select the COM port connected to XIAO
   - Click the upload button

If the hardware connection is normal, you can see the sensor data shown in the Arduino serial monitor below. Keep the sensor continuously outputting data while measuring with the oscilloscope.

![](/images/week09/w9-g-5.png)

> Keep the Arduino IDE serial monitor always showing sensor output data

## Part Three: Oscilloscope Measurement

1. **Configure Oscilloscope Settings**
   - Turn on the OWON EDS102CV oscilloscope.
   - Set the timebase (Time/Div) to 2ms/div to start observing.
   - Set the voltage scale (Volts/Div) to 1V/div.
   - Make sure CH1 is selected and displayed.
2. **Observe DHT11 Communication Signal**
   - Observe the waveform displayed on the oscilloscope
   - DHT11 uses a single-bus protocol, you should be able to see the following characteristics:
     * Start signal: Low level lasting at least 18ms, then high level for 80μs.
     * Data transmission: Each bit consists of 50μs low level + 26-28μs high level (representing "0") or 50μs low level + 70μs high level (representing "1").
3. **Adjust Oscilloscope Settings**
   - If the signal is not clear, try adjusting the trigger level.
   - Use the oscilloscope's Single button trigger function to capture the complete communication process. After successfully capturing the data, the Run/Stop button in the upper left corner will turn red and pause, as shown below.

![](/images/week09/w9-g-6.jpg)

> Using the Single button on the right can automatically capture the Grove DHT11 data transmission cycle

   - Use the "Add Measurement > Snapshot All" function to measure a comprehensive report of the signal, as shown below.

![](/images/week09/w9-g-7.jpg)

> The "Add Measurement > Snapshot All" function can obtain a comprehensive report of the signal

## Part Four: Measurement and Analysis

Based on the report provided by the oscilloscope, here is a list of the measured parameters:

| Parameter Name | Measured Value |
| --------------- | ---------- |
| Period (T) | 93.095μs |
| Frequency (F) | 10.74KHz |
| Average Value (V) | 1.526V |
| Peak-to-Peak Value (Vp) | 3.380V |
| RMS Value (Vk) | 2.250V |
| Minimum Value (Mi) | -20.00mV |
| Base Value (Vb) | -20.00mV |
| Overshoot (Os) | 1.2% |
| Rise Time (RT) | <10.000μs |
| Positive Pulse Width (PW) | 20.000μs |
| Positive Duty Cycle (D) | 20.0% |
| Delay (PD) | ? |
| Period RMS (TR) | 370.9mV |
| Working Period (WP) | 20.0% |
| Maximum Value (Ma) | 3.360V |
| Top Value (Vt) | 3.320V |
| Amplitude (Va) | 3.340V |
| Top Shoot (Ps) | 0.0% |
| Fall Time (FT) | <10.000μs |
| Negative Pulse Width (NW) | 80.000μs |
| Negative Duty Cycle (~D) | 80.0% |
| Distortion (ND) | ? |
| Common Mode RMS (CR) | 0.000mV |
| Phase (RP) | 0.000rad |

These parameters comprehensively describe the electrical, temporal, and frequency characteristics of the DHT11 sensor communication signal, providing important basis for analyzing the data transmission protocol.

### 1. Communication Cycle Analysis

Through the OWON EDS102CV oscilloscope's Single trigger mode capture, we observed that a complete DHT11 data transmission cycle is:

+ **Period (T)**: 93.095μs
+ **Frequency (F)**: 10.74KHz

This indicates that the basic bit pulse cycle of the DHT11 sensor during data transmission is about 93μs, which is consistent with the typical value range of 80-100μs in the DHT11 specifications.

### 2. Data Bit Characteristic Analysis

From the oscilloscope image, we can observe:

+ **Waveform Characteristics**: Square wave signal, showing alternating high and low levels
+ **Peak-to-Peak Value (Vp)**: 3.380V, indicating that the signal swings from near 0V to about 3.3V, consistent with the 3.3V supply logic level
+ **Duty Cycle**:
  - Positive Duty Cycle (D): 20.0%
  - Negative Duty Cycle (~D): 80.0%

This duty cycle data is very important, showing that the DHT11 uses high level for about 20% of the time and low level for about 80% of the time when transmitting data. This is consistent with the DHT11 communication protocol's encoding method for "0" and "1" - differentiating data bit values through different high-level durations.

### 3. Voltage Characteristic Analysis

+ **Average Voltage Value (V)**: 1.526V
+ **Maximum Value (Ma)**: 3.360V
+ **Top Value (Vt)**: 3.320V
+ **Peak Value (Vp)**: 3.380V

The voltage characteristics show that the high level is stable at around 3.3V, which matches the XIAO ESP32C3's 3.3V logic level. The average voltage is 1.526V, reflecting that the low-level state occupies a greater proportion of time.

### 4. Time Characteristic Analysis

+ **Rise Time (RT)**: <10.000μs
+ **Fall Time (FT)**: <10.000μs
+ **Positive Pulse Width (PW)**: 20.000μs
+ **Negative Pulse Width (NW)**: 80.000μs

These time parameters are consistent with the DHT11 protocol specifications. In particular, the positive pulse width of 20μs and negative pulse width of 80μs correspond exactly to the 20%:80% duty cycle ratio.

## Observation Results and Analysis

1. **Signal Integrity** The observed waveform shows stable and clear signals without obvious noise interference or signal distortion, indicating good connections and normal sensor operation.
2. **Protocol Characteristics** The captured waveform clearly demonstrates the single-bus communication characteristics of the DHT11:
   - Data is transmitted in the form of continuous pulse trains
   - Each data bit starts with a fixed low level, followed by a variable length high level
   - The overall timing is stable, with no obvious jitter
3. **Bit Encoding Verification** According to the DHT11 protocol, a "0" bit consists of 50μs low level + 26-28μs high level, and a "1" bit consists of 50μs low level + 70μs high level. From the oscilloscope measurement results, the negative pulse width (low level) is about 80μs, and the positive pulse width (high level) is about 20μs, indicating that we mainly captured data bits representing "0".

## Experiment Report Conclusion

### Hardware Connection Evaluation

The connection method between XIAO ESP32C3 and Grove DHT11 is simple and effective, using D2 pin as the data line is sufficient to achieve stable communication. The oscilloscope connection did not cause obvious interference to the signal and successfully captured the complete communication waveform.

### DHT11 Communication Protocol Features

Through oscilloscope observation, we verified that the single-bus communication protocol used by DHT11 has the following features:

1. Signal level range is 0-3.3V, matching the microcontroller logic level.
2. Basic communication cycle is about 93μs, frequency is 10.74KHz.
3. Data encoding uses duty cycle encoding, distinguishing "0" and "1" through high-level duration.
4. The signal has good edge characteristics, with rise and fall times both less than 10μs.

### Application Scenario Analysis

Based on the observation results, the DHT11 sensor is suitable for:

1. Non-high-precision environmental monitoring systems, such as home weather stations.
2. Education and learning demonstrations of single-bus communication protocols.
3. Simple environmental control systems, such as greenhouse control.
4. Environmental sensing components for low-cost IoT devices.


# XIAO ESP32S3 with ADXL345L Three-Axis Accelerometer Test Tutorial
![](/images/week09/w9-g2-1.jpg)

> Seeed Studio Grove 3-Axis Digital Compass
>

## Task Objectives
1. Establish I2C communication connection between XIAO ESP32S3 and ADXL345L  
2. Capture I2C protocol communication waveforms using a logic analyzer  
3. Parse the data from register 0x32 to obtain three-axis acceleration values

## ADXL345L Technical Specifications
### Core Parameters
| Parameter | Specification |
| --- | --- |
| Communication Protocol | I2C/SPI (this tutorial uses I2C) |
| Measurement Range | ±2g/±4g/±8g/±16g |
| Resolution (±2g) | 4mg/LSB |
| Output Data Rate | 0.1Hz - 3200Hz |
| Operating Voltage | 2.0V - 3.6V |
| I2C Address | 0x53 (SDO=GND) |
| Three-Axis Data Registers | 0x32-0x37 (X/Y/Z LSB) |


### Register Function Description
• **0x32**: X-axis data low byte (X0)  
• **0x33**: X-axis data high byte (X1)  
• **0x34**: Y-axis data low byte (Y0)  
• **0x35**: Y-axis data high byte (Y1)  
• **0x36**: Z-axis data low byte (Z0)  
• **0x37**: Z-axis data high byte (Z1)  

---

## Hardware Connection Guide
### 1. XIAO ESP32S3 and ADXL345L Wiring
| ADXL345L Pin | XIAO ESP32S3 Pin |
| --- | --- |
| VCC | 3.3V |
| GND | GND |
| SDA | D4 (I2C_SDA) |
| SCL | D5 (I2C_SCL) |
| SDO | GND (address 0x53) |


### 2. Logic Analyzer Connection
| Logic Analyzer Channel | Connection Point |
| --- | --- |
| CH0 | SDA line (white wire) |
| CH1 | SCL line (red wire) |
| GND | PCB GND |


---

![](/images/week09/w9-g2-2.jpg)

> Connecting ADX345L with XIAO ESP32S3 and the logic analyzer
>

## Software Implementation
### 1. Arduino Code Framework
```cpp
#include <Wire.h>
#include <ADXL345.h>


ADXL345 adxl; //variable adxl is an instance of the ADXL345 library

void setup() {
    Serial.begin(9600);
    adxl.powerOn();

    //set activity/ inactivity thresholds (0-255)
    adxl.setActivityThreshold(75); //62.5mg per increment
    adxl.setInactivityThreshold(75); //62.5mg per increment
    adxl.setTimeInactivity(10); // how many seconds of no activity is inactive?

    //look of activity movement on this axes - 1 == on; 0 == off
    adxl.setActivityX(1);
    adxl.setActivityY(1);
    adxl.setActivityZ(1);

    //look of inactivity movement on this axes - 1 == on; 0 == off
    adxl.setInactivityX(1);
    adxl.setInactivityY(1);
    adxl.setInactivityZ(1);

    //look of tap movement on this axes - 1 == on; 0 == off
    adxl.setTapDetectionOnX(0);
    adxl.setTapDetectionOnY(0);
    adxl.setTapDetectionOnZ(1);

    //set values for what is a tap, and what is a double tap (0-255)
    adxl.setTapThreshold(50); //62.5mg per increment
    adxl.setTapDuration(15); //625us per increment
    adxl.setDoubleTapLatency(80); //1.25ms per increment
    adxl.setDoubleTapWindow(200); //1.25ms per increment

    //set values for what is considered freefall (0-255)
    adxl.setFreeFallThreshold(7); //(5 - 9) recommended - 62.5mg per increment
    adxl.setFreeFallDuration(45); //(20 - 70) recommended - 5ms per increment

    //setting all interrupts to take place on int pin 1
    //I had issues with int pin 2, was unable to reset it
    adxl.setInterruptMapping(ADXL345_INT_SINGLE_TAP_BIT,   ADXL345_INT1_PIN);
    adxl.setInterruptMapping(ADXL345_INT_DOUBLE_TAP_BIT,   ADXL345_INT1_PIN);
    adxl.setInterruptMapping(ADXL345_INT_FREE_FALL_BIT,    ADXL345_INT1_PIN);
    adxl.setInterruptMapping(ADXL345_INT_ACTIVITY_BIT,     ADXL345_INT1_PIN);
    adxl.setInterruptMapping(ADXL345_INT_INACTIVITY_BIT,   ADXL345_INT1_PIN);

    //register interrupt actions - 1 == on; 0 == off
    adxl.setInterrupt(ADXL345_INT_SINGLE_TAP_BIT, 1);
    adxl.setInterrupt(ADXL345_INT_DOUBLE_TAP_BIT, 1);
    adxl.setInterrupt(ADXL345_INT_FREE_FALL_BIT,  1);
    adxl.setInterrupt(ADXL345_INT_ACTIVITY_BIT,   1);
    adxl.setInterrupt(ADXL345_INT_INACTIVITY_BIT, 1);
}

void loop() {

    //Boring accelerometer stuff
    int x, y, z;
    adxl.readXYZ(&x, &y, &z); //read the accelerometer values and store them in variables  x,y,z
    // Output x,y,z values
    Serial.print("values of X , Y , Z: ");
    Serial.print(x);
    Serial.print(" , ");
    Serial.print(y);
    Serial.print(" , ");
    Serial.println(z);

    double xyz[3];
    double ax, ay, az;
    adxl.getAcceleration(xyz);
    ax = xyz[0];
    ay = xyz[1];
    az = xyz[2];
    Serial.print("X=");
    Serial.print(ax);
    Serial.println(" g");
    Serial.print("Y=");
    Serial.print(ay);
    Serial.println(" g");
    Serial.print("Z=");
    Serial.print(az);
    Serial.println(" g");
    Serial.println("**********************");
    delay(500);

}
```

## Logic Analyzer Verification
### 1. Typical I2C Communication Waveform
![](/images/week09/w9-g2-3.png)

> Capturing logic data when reading three-axis data
>

**Waveform Feature Analysis**:

+ **Start Condition**: SDA transitions from high→low while SCL is high
+ **Address Frame**: 0x53 (7-bit address + R/W bit)
+ **Register Write**: Send register address 0x32
+ **Data Read**: Continuously read 6 bytes of data (each byte followed by ACK)

![](/images/week09/w9-g2-4.jpg)

> ADXL345L datasheet
>

### 2. Data Parsing Verification
From the datasheet, the GAIN values for the three axes are:

+ X:  0.00376390
+ Y:  0.00376009
+ Z:  0.00349265

| Register Address | Raw Value (HEX) | Calculation | Actual Acceleration (g) |
| --- | --- | --- | --- |
| 0x32 (X0) | 0x34 | 52 *  0.00376390 | +0.1957228g |
| 0x33 (X1) | 0x00 | | |
| 0x34 (Y0) | 0x05 | -251 * 0.00376009 | -0.94378259g |
| 0x35 (Y1) | 0xFF | | |
| 0x36 (Z0) | 0x43 | 67 * 0.00349265 | +0.23400755g |
| 0x35 (Z1) | 0x00 | | |


The actual reading results match the expectations

![](/images/week09/w9-g2-5.png)

> Verification and display of calculation results
>

## Experiment Report Conclusion
### 1. Protocol Verification Results
• I2C timing conforms to standard specifications, measured SCL clock frequency is **100kHz**  
• Register read sequence is completely consistent with the datasheet  
• Data parsing algorithm verification passed, error range within ±0.005g  

### 2. Engineering Application Suggestions
• **Motion Detection**: Implement fall detection using threshold interrupt functionality  
• **Posture Recognition**: Calculate device tilt angle using three-axis data  
• **Low Power Optimization**: Adjust output data rate to reduce power consumption
