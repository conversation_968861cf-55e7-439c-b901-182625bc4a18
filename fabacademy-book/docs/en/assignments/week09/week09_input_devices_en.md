---
layout: doc
title: "Week 9: Input Devices | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week Nine: Learning the working principles and applications of various input devices and sensors, including buttons, temperature/humidity sensors, photosensitive elements, accelerometers, and other sensor technologies"
head:
  - - meta
    - name: keywords
      content: fab academy, input devices, sensors, buttons, temperature humidity sensors, accelerometers, step response, capacitive sensing, signal processing, oscilloscope
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 8: Individual Assignment: Electronic Circuit Board Production'
  link: '/en/assignments/week08/week08_individual_assignment_pcb_production_en'
next:
  text: 'Week 9: Group Assignment: Measuring Grove Sensors with an Oscilloscope'
  link: '/en/assignments/week09/week09_group_sensors_measurement_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Week 9: Input Devices

> This document was generated with AI assistance based on the [course outline](http://academy.cba.mit.edu/classes/input_devices/index.html) and subtitles from the video conference session that I provided to <PERSON> 3.7 Sonnet.
>

## Course Overview
This week's course focuses on input devices and sensor technologies, teaching how to connect various sensors to microcontrollers and read data from them. The course covers a wide range of input devices, including button switches, magnetic field sensors, step response measurements, temperature sensors, light sensors, motion sensors, distance sensors, real-time clocks, position sensors, accelerometers, sound sensors, force sensors, and cameras. Students will learn how to measure physical quantities using these sensors and input this information into digital systems for processing.

## Detailed Course Content
### 1. Course Introduction and Background
In the process of manufacturing electronic devices, input devices are key components for interacting with the external world. This week's course will explore various sensor technologies and their applications in depth. In the coming weeks, we will learn about output devices (such as motors and displays), communication between multiple processors, and how to build complete machine systems.

### 2. Microcontroller Input Basics
#### 2.1 Datasheets and Pin Functions
+ **Datasheet References**: Understanding the [AVR DB series microcontroller datasheets](http://inventory.fabcloud.io/Input%20Devices/inputs), with special attention to the analog interface section
+ **Basic Pin Types**: 
    - Digital input/output pins (ports)
    - Analog comparators
    - Analog-to-digital converters (A/D)
    - [I2C interface](https://www.nxp.com/docs/en/application-note/AN10216.pdf)

#### 2.2 Signal Processing Basics
+ **Analog Comparators**: Quick comparison of two voltage values
+ **Analog-to-Digital Converters**: Converting analog signals to digital values
+ **I2C Communication**: Increasingly more sensors use the I2C communication protocol

### 3. Buttons and Switches
#### 3.1 Button Principles
+ **Button Types**: [Momentary buttons](https://www.digikey.com/en/products/detail/omron-electronics-inc-emc-div/B3SN-3112P/27856), [slide switches](https://www.digikey.com/en/products/detail/c-k/AYZ0102AGRLC/1640108)
+ **Debouncing**: Buttons produce oscillating signals when pressed, requiring software or hardware debouncing

#### 3.2 Button Programming Examples
The following are examples based on ESP32-C3:

+ [Board design](https://claude.ai/embedded_programming/ESP32-C3/hello.button-blink.C3.png)
+ [Components](https://claude.ai/embedded_programming/ESP32-C3/hello.button-blink.C3.jpg)
+ [Arduino code](https://claude.ai/embedded_programming/ESP32-C3/hello.button-blink.C3.ino)
+ [MicroPython code](https://claude.ai/embedded_programming/ESP32-C3/hello.button-blink.C3.py)
+ [Demo video](https://claude.ai/embedded_programming/ESP32-C3/hello.button-blink.C3.mp4)

Other microcontroller examples:

+ SAM D11C: [Board design](https://claude.ai/chat/button/hello.button.D11C.png), [Code](https://claude.ai/chat/button/hello.button.D11C.ino)
+ ATtiny412: [Board design](https://claude.ai/chat/button/hello.button.t412.png), [Code](https://claude.ai/chat/button/hello.button.t412.ino)

### 4. Magnetic Field Sensors
#### 4.1 Hall Effect Sensors
+ **Principle**: [Hall effect sensors](https://www.digikey.com/en/products/detail/allegro-microsystems/A1324LLHLT-T/2639989) measure magnetic fields and output corresponding voltages
+ **Directionality**: Flipping the sensor can measure magnetic fields in different directions
+ **Sensitivity**: Sensitive enough to measure the Earth's magnetic field
+ **Applications**: Detecting lid closure, shaft proximity to endpoint
+ **Example**: [hello.mag.45](https://claude.ai/chat/mag/hello.mag.45), [Board design](https://claude.ai/chat/mag/hello.mag.45.png), [Code](https://claude.ai/chat/mag/hello.mag.45.c), [Video](https://claude.ai/chat/mag/hello.mag.45.mp4)

#### 4.2 Vector Magnetometers
+ **3-Axis Magnetic Field Measurement**: [Vector magnetometers](https://www.digikey.com/en/products/detail/infineon-technologies/TLE493DA2B6HTSA1/9808570) measure magnetic fields in X, Y, and Z directions
+ **Applications**: [Application cases](https://www.infineon.com/dgdl/Infineon-3D_Magnetic_Sensors-ProductBrief-v05_00-EN.pdf?fileId=5546d46261d5e6820161e7571b2b3dd0) include joysticks, shift lever position detection, wheel interfaces, etc.
+ **I2C Communication**: Data transmission via I2C protocol
+ **Example**: [hello.TLE493D.t412](https://claude.ai/chat/mag/TLE493D/hello.TLE493D.t412), [Board design](https://claude.ai/chat/mag/TLE493D/hello.TLE493D.t412.png), [Code](https://claude.ai/chat/mag/TLE493D/hello.TLE493D.t412.ino), [Video](https://claude.ai/chat/mag/TLE493D/hello.TLE493D.t412.mp4)
+ **Simulation Tool**: [3D magnetic sensor simulator](https://design.infineon.com/3dsim/)

### 5. Variable Resistors (Potentiometers)
+ **Principle**: [Potentiometers](https://www.digikey.com/en/products/detail/nidec-copal-electronics/ST4ETB103/738213) change resistance values through rotation or sliding
+ **Applications**: Adjusting settings, controlling motor current, etc.
+ **Example**: [Motor control application](http://academy.cba.mit.edu/classes/output_devices/DRV8428/hello.DRV8428-D11C-NEMA17.jpg)

### 6. Step Response Measurement
#### 6.1 Capacitive Sensing Principles
+ **Measurement Principle**: Based on measuring capacitance changes through charge/discharge time
+ **Application Range**: Measuring resistance, capacitance, inductance, position, pressure, tilt, acceleration, humidity, proximity, etc.
+ **Human Capacitance Measurement**: Utilizing human body conductivity to measure contact or proximity
+ **Animation Demo**: [Step response simulation](https://claude.ai/chat/step/sim/step.mp4)

#### 6.2 Self-Capacitance Measurement (Single Pin)
+ **Hardware Support**: 
    - [QTouch](https://developerhelp.microchip.com/xwiki/bin/view/products/mcu-mpu/32bit-mcu/sam/samd21-mcu-overview/peripherals/ptc-overview/)
    - [FreeTouch library](https://github.com/adafruit/Adafruit_FreeTouch)
    - [ESP32 touch functionality](https://docs.espressif.com/projects/esp-idf/en/stable/esp32/api-reference/peripherals/touch_pad.html)
+ **Examples**: 
    - SAMD21: [hello.touch.D21](https://claude.ai/chat/step/D21/hello.touch.D21), [Code](https://claude.ai/chat/step/D21/hello.touch.D21.ino), [Video](https://claude.ai/chat/step/D21/hello.touch.D21.mp4)
    - ESP32S3: [hello.touch.S3](https://claude.ai/chat/step/ESP32S3/hello.touch.S3), [Code](https://claude.ai/chat/step/ESP32S3/hello.touch.S3.py), [Video](https://claude.ai/chat/step/ESP32S3/hello.touch.S3.mp4)
+ **DIY Capacitance Measurement**: 
    - RP2040: [hello.steptime1.RP2040](https://claude.ai/chat/step/RP2040/hello.steptime1.RP2040), [Code](https://claude.ai/chat/step/RP2040/hello.steptime1.RP2040.py), [Video](https://claude.ai/chat/step/RP2040/hello.steptime1.RP2040.mp4)

#### 6.3 Remote Processor Measurement
+ **Principle**: Placing the processor close to electrodes to avoid line noise
+ **Example**: [hello.load.RP2040.t412](https://claude.ai/chat/step/t412/hello.load.RP2040.t412), [Code](https://claude.ai/chat/step/t412/hello.load.RP2040.py), [Video](https://claude.ai/chat/step/t412/hello.load.RP2040.t412.mp4)
+ **Applications**: [Capacitive caliper](https://claude.ai/chat/step/caliper.jpg), [CVDT](https://claude.ai/chat/step/steptime/CVDT.jpg), [Video](https://claude.ai/chat/step/steptime/CVDT.mp4)

#### 6.4 Mutual Capacitance Measurement (Dual Pin)
+ **Principle**: One electrode sends signals, another receives
+ **Advantages**: Not dependent on indoor ground, more stable and reliable
+ **Examples**: 
    - RP2040: [hello.txrx2.RP2040](https://claude.ai/chat/step/hello.txrx2.RP2040), [Code](https://claude.ai/chat/step/hello.txrx2.RP2040.ino), [Video](https://claude.ai/chat/step/hello.txrx2.RP2040.mp4)
    - ATtiny1624: [hello.txrx.t1624](https://claude.ai/chat/step/hello.txrx.t1624), [Code](https://claude.ai/chat/step/hello.txrx.t1624.ino), [Video](https://claude.ai/chat/step/hello.txrx.t1624.mp4)

#### 6.5 Remote Amplifier Measurement
+ **Principle**: Using [operational amplifiers](https://www.digikey.com/en/products/detail/texas-instruments/TLV365DBVR/17748355) to enhance signals
+ **Example**: [hello.txrx.RP2040.op-amp](https://claude.ai/chat/step/RP2040/hello.txrx.RP2040.op-amp), [Code](https://claude.ai/chat/step/RP2040/hello.txrx.RP2040.op-amp.ino), [Video](https://claude.ai/chat/step/RP2040/hello.txrx.RP2040.op-amp.mp4)

#### 6.6 Dielectric Spectroscopy Measurement
+ **High-Frequency Applications**: Can measure material properties at higher frequencies
+ **Material Identification**: Can differentiate between different liquids such as alcohol, martini, beer, wine, etc.
+ **References**: [Application Note](https://claude.ai/chat/DS.pdf), [Theory](https://claude.ai/chat/meas.pdf), [Measurement Methods](http://cba.mit.edu/docs/theses/17.06.VanWyk.pdf)

#### 6.7 Application Examples
+ [Touch Interfaces](http://fab.cba.mit.edu/classes/863.10/people/matt.blackshaw/week8.html)
+ [Multi-touch](http://fab.cba.mit.edu/classes/863.11/people/matthew.keeter/multitouch/index.html)
+ [Force Sensors](https://fabacademy.org/2020/labs/leon/students/adrian-torres/adrianino.html#step)
+ [Bend Measurement](https://dl.acm.org/doi/pdf/10.1145/3313831.3376269)

### 7. Temperature Sensors
#### 7.1 Thermistor Bridge Circuits
+ **Principle**: [NTC thermistors](https://www.digikey.com/en/products/detail/amphenol-thermometrics/NHQ103B375T10/374815) or [RTD thermistors](https://www.digikey.com/en/products/detail/vishay-beyschlag-draloric-bc-components/PTS120601B1K00P100/1666188) change resistance with temperature
+ **Example**: [hello.temp.45](https://claude.ai/chat/temp/hello.temp.45), [Code](https://claude.ai/chat/temp/hello.temp.45.c), [Video](https://claude.ai/chat/temp/hello.temp.45.mp4)

#### 7.2 Infrared Temperature Measurement
+ **Principle**: [Infrared temperature sensors](https://www.digikey.com/catalog/en/partgroup/mlx90614-15/20353) measure infrared radiation emitted by objects
+ **Applications**: Non-contact temperature measurement

### 8. Light Sensors
#### 8.1 Phototransistors
+ **Types**: [Infrared](http://www.digikey.com/product-detail/en/everlight-electronics-co-ltd/PT15-21B-TR8/1080-1379-1-ND) and [visible light](http://www.digikey.com/product-detail/en/everlight-electronics-co-ltd/PT15-21C-TR8/1080-1380-1-ND) phototransistors
+ **Example**: [hello.light.45](https://claude.ai/chat/light/hello.light.45), [Code](https://claude.ai/chat/light/hello.light.45.c), [Video](https://claude.ai/chat/light/hello.light.45.mp4)

#### 8.2 Synchronous Detection
+ **Principle**: [Synchronous detection technology](http://www.cambridge.org/us/knowledge/isbn/item6598594/The%20Physics%20of%20Information%20Technology/?site_locale=en_US) eliminates ambient light interference
+ **Example**: [hello.reflect.45](https://claude.ai/chat/light/hello.reflect.45), [Code](https://claude.ai/chat/light/hello.reflect.45.c), [Video](https://claude.ai/chat/light/hello.reflect.45.mp4)

#### 8.3 Color Sensors
+ **RGB Sensors**: [Color sensors](https://www.digikey.com/en/products/detail/vishay-semiconductor-opto-division/VEML6040A3OG/5168308) measure red, green, and blue colors
+ **Example**: [hello.VEML6040.t412](https://claude.ai/chat/color/hello.VEML6040.t412), [Code](https://claude.ai/chat/color/hello.VEML6040.ino), [Video](https://claude.ai/chat/color/hello.VEML6040.mp4)
+ **Gesture Sensors**: [APDS-9960](https://www.broadcom.com/products/optical-sensors/integrated-ambient-light-and-proximity-sensors/apds-9960) can detect gestures

### 9. Motion Sensors
#### 9.1 Doppler Radar
+ **Principle**: [Doppler radar modules](https://www.amazon.com/RCWL-0516-Detection-Microwave-Raspberry-Detector/dp/B07GCHY9K6) measure Doppler shift to detect motion
+ **Example**: [hello.RCWL-0516](https://claude.ai/chat/radar/hello.RCWL-0516), [Code](https://claude.ai/chat/radar/hello.RCWL-0516.c), [Video](https://claude.ai/chat/radar/hello.RCWL-0516.mp4)

#### 9.2 Pyroelectric Sensors
+ **Sensor**: [HC-SR501](https://www.amazon.com/DIYmall-HC-SR501-Motion-Infrared-Arduino/dp/B012ZZ4LPM)
+ **Example**: [hello.HC-SR501](https://claude.ai/chat/motion/hello.HC-SR501), [Code](https://claude.ai/chat/motion/hello.HC-SR501.c), [Video](https://claude.ai/chat/motion/hello.HC-SR501.mp4)

### 10. Distance Sensors
#### 10.1 Laser Time-of-Flight Measurement
+ **Sensors**: 
    - [VL53L0X](https://www.digikey.com/en/products/detail/stmicroelectronics/VL53L0CXV0DH-1/6023691), [Library](https://github.com/pololu/vl53l0x-arduino)
    - [VL53L1X](https://www.digikey.com/en/products/detail/stmicroelectronics/VL53L1CXV0FY-1/8258055), [Module](https://www.digikey.com/en/products/detail/pololu-corporation/3415/10451121)
    - [VL53L5CX](https://www.digikey.com/en/products/detail/stmicroelectronics/VL53L5CXV0GC-1/14552424) (8x8 array)
+ **Examples**: 
    - [hello.VL53L0X.D11C](https://claude.ai/chat/tof/hello.VL53L0X.D11C), [Code](https://claude.ai/chat/tof/hello.VL53L0X.D11C.ino), [Video](https://claude.ai/chat/tof/hello.VL53L0X.D11C.mp4)
    - [hello.VL53L1X.t1614](https://claude.ai/chat/tof/hello.VL53L1X.t1614), [Code](https://claude.ai/chat/tof/hello.VL53L1X.ino), [Video](https://claude.ai/chat/tof/hello.VL53L1X.mp4)

#### 10.2 Ultrasonic Sensors
+ **Sensor**: [HC-SR04](http://www.amazon.com/SunFounder-Ultrasonic-Distance-Mega2560-Duemilanove/dp/B00E0NXTJW)
+ **Example**: [hello.HC-SR04](https://claude.ai/chat/sonar/hello.HC-SR04), [Code](https://claude.ai/chat/sonar/hello.HC-SR04.c), [Video](https://claude.ai/chat/sonar/hello.HC-SR04.mp4)

### 11. Real-Time Clocks (RTC)
+ **Chip**: [PCF8523](https://www.digikey.com/en/products/detail/nxp-usa-inc/PCF8523T-1-118/2530422), [Module](https://www.adafruit.com/product/3295)
+ **Example**: [hello.PCF8523.RP2040](https://claude.ai/chat/RTC/hello.PCF8523.RP2040), [Code](https://claude.ai/chat/RTC/hello.PCF8523.ino), [Video](https://claude.ai/chat/RTC/hello.PCF8523.RP2040.mp4)

### 12. Position and Time Sensors (GPS)
+ **Systems**: [GNSS](https://www.gps.gov/systems/gnss), [NMEA Protocol](https://gpsd.gitlab.io/gpsd/NMEA.html)
+ **Modules**: [NEO-6](https://www.u-blox.com/sites/default/files/products/documents/NEO-6_DataSheet_(GPS.G6-HW-09005).pdf), [GT-U7](https://images-na.ssl-images-amazon.com/images/I/91tuvtrO2jL.pdf)
+ **Example**: [hello.GPS.t1614](https://claude.ai/chat/GPS/hello.GPS.t1614), [Code](https://claude.ai/chat/GPS/hello.GPS.t1614.ino), [Video](https://claude.ai/chat/GPS/hello.GPS.t1614.mp4)

### 13. Acceleration, Rotation, and Orientation Sensors
#### 13.1 Three-Axis Accelerometers
+ **Chip**: [ADXL343](https://www.digikey.com/en/products/detail/analog-devices-inc/ADXL343BCCZ/3542918)
+ **Example**: [hello.ADXL343](https://claude.ai/chat/accel/hello.ADXL343), [Code](https://claude.ai/chat/accel/hello.ADXL343.c), [Video](https://claude.ai/chat/accel/hello.ADXL343.mp4)

#### 13.2 Six-Axis IMU (Inertial Measurement Unit)
+ **Chips**: [MPU-6050](https://invensense.tdk.com/products/motion-tracking/6-axis/mpu-6050), [Module](https://www.amazon.com/HiLetgo-MPU-6050-Accelerometer-Gyroscope-Converter/dp/B00LP25V1A), [ICM-20609](https://invensense.tdk.com/products/motion-tracking/6-axis/icm-20609)
+ **Example**: [hello.MPU-6050.RP2040](https://claude.ai/chat/imu/6050/hello.MPU-6050.RP2040), [Code](https://claude.ai/chat/imu/6050/hello.MPU-6050.ino), [Video](https://claude.ai/chat/imu/6050/hello.MPU-6050.RP2040.mp4)

#### 13.3 Nine-Axis IMU
+ **Chips**: 
    - [BNO085](https://www.digikey.com/en/products/detail/ceva-technologies-inc/BNO085/9445940), [Module](https://www.adafruit.com/product/4754)
    - [BNO086](https://www.digikey.com/en/products/detail/ceva-technologies-inc/BNO086/14114190)
    - [MPU-9250](https://invensense.tdk.com/products/motion-tracking/9-axis/mpu-9250)
    - [ICM-20948](https://invensense.tdk.com/products/motion-tracking/9-axis/icm-20948/), [Module](https://www.adafruit.com/product/4554)
+ **Example**: [hello.4754.RP2040](https://claude.ai/chat/imu/hello.4754.RP2040), [Code](https://claude.ai/chat/imu/hello.4754.RP2040.py), [Video](https://claude.ai/chat/imu/hello.4754.RP2040.mp4)

### 14. Sound Sensors
#### 14.1 MEMS Microphones
+ **Digital Interface**:
    - [I2S Protocol](https://www.sparkfun.com/datasheets/BreakoutBoards/I2SBUS.pdf)
    - [Bottom Port](https://www.digikey.com/en/products/detail/tdk-invensense/ICS-43434/6140298), [Module](https://www.adafruit.com/product/6049)
    - [Top Port](https://www.digikey.com/en/products/detail/cui-devices/CMM-4030D-261-I2S-TR/13164051)
+ **Examples**:
    - [hello.ICS-43434.RP2040](https://claude.ai/chat/mic/I2S/hello.ICS-43434.RP2040), [Code](https://claude.ai/chat/mic/I2S/hello.ICS-43434.RP2040.ino), [Video](https://claude.ai/chat/mic/I2S/hello.ICS-43434.RP2040.mp4)
    - [hello.CMM-4030D-261-I2S-TR.t1614](https://claude.ai/chat/mic/hello.CMM-4030D-261-I2S-TR.t1614), [Code](https://claude.ai/chat/mic/hello.CMM-4030D-261-I2S-TR.ino), [Video](https://claude.ai/chat/mic/hello.CMM-4030D-261-I2S-TR.mp4)
+ **Analog Interface**:
    - [SPU0414HR5H](https://www.digikey.com/en/products/detail/knowles/SPU0414HR5H-SB-7/2420969)
    - **Example**: [hello.SPU0414HR5H](https://claude.ai/chat/mic/hello.SPU0414HR5H), [Code](https://claude.ai/chat/mic/hello.SPU0414HR5H.c), [Video](https://claude.ai/chat/mic/hello.SPU0414HR5H.mp4)

#### 14.2 Electret Microphones
+ **Amplifier**: [AD8615 Operational Amplifier](http://www.digikey.com/product-detail/en/AD8615AUJZ-REEL7/AD8615AUJZ-REEL7CT-ND)
+ **Example**: [hello.mic.45](https://claude.ai/chat/mic/hello.mic.45), [Code](https://claude.ai/chat/mic/hello.mic.45.c), [Video](https://claude.ai/chat/mic/hello.mic.45.mp4)

### 15. Other Sensor Introductions
+ **Vibration Sensors**: [Piezoelectric Sensors](http://www.jameco.com/webapp/wcs/stores/servlet/ProductDisplay?langId=-1&storeId=10001&catalogId=10001&productId=1956784)
+ **Force Sensors**: 
    - [Capacitive](http://www.designworldonline.com/capacitive-sensors-measure-low-forces)
    - [Resistive](https://github.com/IvDm/Z-probe-on-smd-resistors-2512)
    - [Force-Sensitive Resistors](http://www.interlinkelectronics.com/standard-products.php)
    - [Strain Gauges](http://www.omega.com/guides/straingages.html)
    - [Load Cells](http://www.omega.com/prodinfo/loadcells.html)
+ **Angle Sensors**: [Encoders](https://www.digikey.com/products/en/sensors-transducers/encoders/507)
+ **Pressure Sensors**: [DPS310](https://www.digikey.com/product-detail/en/infineon-technologies/DPS310XTSA1/DPS310XTSA1CT-ND/)
+ **Pulse Sensors**: [MAX30102](https://www.digikey.com/product-detail/en/maxim-integrated/MAX30102EFD/MAX30102EFD-ND/6166869)
+ **Air Pollution Sensors**: [Particulate Matter Sensors](https://www.digikey.com/en/products/filter/particle-dust-sensors/509)
+ **Gas Sensors**: [Various Gas Sensors](https://www.pololu.com/category/83/gas-sensors)

### 16. Image Sensors
#### 16.1 Camera Modules
+ **ESP32 Cameras**: 
    - [ESP32S3 XIAO Sense](https://www.seeedstudio.com/XIAO-ESP32S3-Sense-p-5639.html), [Module](https://claude.ai/chat/image/hello.ESP32-Sense.jpg), [Camera](https://www.seeedstudio.com/OV5640-Camera-for-XIAO-ESP32S3-Sense-With-Heat-Sink-p-5739.html)
    - [ESP32-CAM](https://www.amazon.com/s?k=ESP32-CAM)
+ **Examples**: 
    - [hello.ESP32-Sense.ino](https://claude.ai/chat/image/hello.ESP32-Sense.ino), [Video](https://claude.ai/chat/image/hello.ESP32-Sense.mp4)
    - [hello.ESP32-CAM](https://claude.ai/networking_communications/ESP32/hello.ESP32-CAM), [Code](https://claude.ai/networking_communications/ESP32/hello.ESP32-CAM.ino), [Video](https://claude.ai/networking_communications/ESP32/hello.ESP32-CAM.mp4)

#### 16.2 Artificial Intelligence Modules
+ [Grove Vision AI Module](https://wiki.seeedstudio.com/Grove-Vision-AI-Module/)

#### 16.3 Webcams
+ [Webcams](https://www.google.com/search?tbm=shop&q=webcam) with [Embedded Linux Boards](https://www.google.com/search?tbm=shop&q=embedded+linux+board)

#### 16.4 Image Processing Libraries
+ [OpenCV](http://opencv.org/), [OpenCV.js](https://github.com/ucisysarch/opencvjs), [SimpleCV](http://simplecv.org/)
+ [libuvc](https://int80k.com/libuvc/doc/), [guvcview](http://guvcview.sourceforge.net/)

#### 16.5 Web Image Processing
+ [WebRTC](https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API)
+ [Example](https://claude.ai/chat/video.html), [Video](https://claude.ai/chat/video.mp4)

## Assignment Requirements
### Group Assignment
Probe input devices for their analog levels and digital signals:

1. Select an input device (such as a button, sensor, etc.)
2. Use an oscilloscope or other tools to measure its analog levels
3. Observe and record the characteristics of digital signals
4. Analyze signal patterns and understand their working principles

### Individual Assignment
Measure something: Add a sensor to your designed microcontroller board and read the data

1. Design and build a circuit board with a microcontroller and sensor
2. Program it to read sensor data
3. Visualize the data (through Arduino/Thonny built-in plotting tools or Python code)
4. Document the process and write up the sensor principles, applications, and measurement results

**Reference Example**: [Adrian Torres' Input Devices Project](http://fabacademy.org/2020/labs/leon/students/adrian-torres/fabxiao.html#inputs)

**Suggestion**: If unsure which sensor to choose, try using step response technology, as it only requires processor pins and simple copper electrodes to create various interesting interfaces and sensors.

## Learning Resources
### References
1. [Input Devices Course Homepage](http://inventory.fabcloud.io/?purpose=Input%20Devices)
2. [AVR DB Series Datasheet](https://claude.ai/embedded_programming/DB/AVR128DB28-DS40002247A.pdf)
3. [I2C Communication Protocol Description](https://www.nxp.com/docs/en/application-note/AN10216.pdf)

### Programming Libraries and Tools
1. [Adafruit FreeTouch Library](https://github.com/adafruit/Adafruit_FreeTouch)
2. [ESP32 Touch Sensor API](https://docs.espressif.com/projects/esp-idf/en/stable/esp32/api-reference/peripherals/touch_pad.html)