---
layout: doc
title: "Week 6: First Attempt at PCB Design | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 6 Individual Assignment: Learning KiCad, designing an extension board for XIAO ESP32C3"
head:
  - - meta
    - name: keywords
      content: fab academy, PCB design, KiCad, XIAO ESP32C3, extension board, schematic design, LED control, electronics design
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 6: Electronics Design and Production'
  link: '/en/assignments/week06/week06_electronics_design_en'
next:
  text: 'Week 7: Computer-Controlled Machining'
  link: '/en/assignments/week07/week07_computer_controlled_machining_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Week 6 Group Assignment: Electronic Measurement Tools

[[View the complete group assignment]](https://fabacademy.org/2025/labs/chaihuo/docs/week6/week6_group_assignment)

This week's group assignment focuses on getting familiar with commonly used electronic measurement tools in the FabLab, including the multimeter, logic analyzer, and oscilloscope. Understanding how to use these tools effectively is crucial for debugging and testing electronic circuits.

We conducted the following experiments and measurements:

1. **Multimeter**:
   - Voltage measurement: Measuring the 5V pin output of the ESP32C3 microcontroller (actual 5.11V) and the digital signal changes in an LED blinking circuit (HIGH 3.25V, LOW 0V)
   - Continuity checking: Testing the correct connection of circuits on a breadboard
   - Resistance measurement: Measuring the actual resistance of a nominal 1K resistor (0.992kΩ)

2. **Logic Analyzer**:
   - Using DSLogicPlus to capture and analyze I2C communication signals
   - Running an Arduino I2C scanning demonstration to detect connected I2C devices
   - Finding that the SCL signal responded at address 0x80, with a measured I2C frequency of 44 kHz

3. **Oscilloscope**:
   - Capturing differential frequency signals and examining waveform characteristics
   - Using various oscilloscope models including RTM3004 (53M, 13M, and 26M)
   - Analyzing the I2C clock signal (SCL), also measured at 44 kHz

These electronic measurement tools are indispensable for circuit debugging and validation, making them crucial for any electronics-related project.

---

# Week 6 Individual Assignment: First Attempt at PCB Design


This week's assignment was quite challenging for me. Although I've previously written several introductory Arduino programming books, I haven't delved into electronics and circuit board design. So this week's course was almost like starting from scratch for me.

## Software Learning
For the electronics part, I started learning from Forrest M. Mims, III's [Getting Started in Electronics](https://archive.org/details/getting-started-in-electronics/mode/2up), which I found to be an introductory electronics book that I could truly understand. However, this learning process is slow, and I estimate it will take me several months to fully master this subject.

### LCEDA (EasyEDA)
I tried the Chinese PCB design tool [LCEDA](https://lceda.cn/) (free software), developed by JLCPCB. They expect users to use this free software to design PCB circuit boards and then use their PCB manufacturing service, thereby generating revenue. The software also supports importing projects from Altium, KiCad, etc. It also supports team collaboration.

This tool's documentation and operation are very user-friendly for Chinese users. The documentation introduces the entire process from PCB design to PCB ordering, as shown below.

![](/images/week06/w06-p-1.png)

> PCB design workflow from LCEDA documentation
>

I consulted with an electronics engineer from Seeed Studio, who recommended using KiCad, so I adjusted my learning goals.

### KiCad EDA
While starting to learn electronics, I also began trying the [KiCad EDA](https://www.kicad.org/) software (open source), with the help of Chinese quick-start video tutorials: [https://www.bilibili.com/video/BV12J411z7j7](https://www.bilibili.com/video/BV12J411z7j7)

I drew a schematic following the tutorial, which started with an existing project schematic as shown below.

![](/images/week06/w06-p-2.jpg)

> Reference schematic at the beginning of the video tutorial
>

Under the guidance of the tutorial author, I successfully drew my first schematic with KiCad 9, as shown below.

![](/images/week06/w06-p-3.png)

> Schematic drawn following the tutorial
>

Then, following the tutorial instructions, I edited the PCB based on the schematic and tried to draw the design shown below, by which time more than half a week had passed.

![](/images/week06/w06-p-4.png)

> PCB design drawn following the tutorial
>

At this stage, I had a basic concept of how to draw schematics and circuit boards. But I still felt a bit confused, as I hadn't developed enough electronics skills. Understanding what types of resistors, capacitors, and other components to add for my desired functionality made me feel that I was still quite far from being able to design PCBs independently.

## Using AI to Design a XIAO ESP32C3 Extension Board
Given my current knowledge, I lacked confidence in designing a PCB on my own, so I decided to try using AI for guidance to design and create an extension board for the XIAO ESP32C3. I chose [Claude](https://claude.ai/chat/) as my AI assistant, specifically using Claude 3.7 Sonnet.

### Prompt Preparation
I wrote a simple prompt and provided text content and pin diagrams from the XIAO ESP32C3 Wiki, as shown below.

![](/images/week06/w06-p-5.png)

> Providing Claude with the XIAO ESP32C3 Wiki text content and pin diagram, along with the prompt:  
I would like you to act as an electronics engineer and help me design a PCB. This PCB will be an extension board for the XIAO ESP32C3 (providing a slot for the XIAO ESP32C3), with 6 programmable LED lights and one programmable button on the board. Guide me to draw the schematic and PCB design using KiCad.
>

The following content enclosed by dividing lines is Claude's complete output.

---

# XIAO ESP32C3 Extension Board Design Guide
I'm happy to provide guidance for designing a XIAO ESP32C3 extension board. Based on your requirements, I'll detail how to use KiCad to design an extension board with 6 programmable LEDs and 1 button.

## I. Design Overview
Based on the information and images you provided, we need to design an extension board that includes:

+ XIAO ESP32C3 socket
+ 6 programmable LEDs
+ 1 programmable button
+ Necessary connectors and power management

## II. KiCad Schematic Design Steps
### 1. Create a New Project
1. Open KiCad and create a new project
2. Name it "XIAO_ESP32C3_Extension"
3. Click the schematic editor icon to open Eeschema

### 2. Add the XIAO ESP32C3 Symbol
Since KiCad may not have a standard library for XIAO ESP32C3, we need to create a custom symbol:

1. In Eeschema, click "Tools" → "Symbol Editor"
2. Create a new library, name it "XIAO_ESP32C3"
3. Create a new symbol, set up pins as follows (based on the pin arrangement in the image):

```plain
Left side pins:
GPIO2/D0 - Digital Pin
GPIO3/D1 - Digital Pin
GPIO4/D2 - Digital Pin
GPIO5/D3 - Digital Pin
GPIO6/D4 - Digital Pin
GPIO7/D5 - Digital Pin
GPIO21/D6 - Digital Pin

Right side pins:
5V - Power Input
GND - Power Ground
3V3 - Power Output
D10/GPIO10 - Digital Pin
D9/GPIO9 - Digital Pin
D8/GPIO8 - Digital Pin
D7/GPIO20 - Digital Pin

Top pins:
TX, RX, SCK, MISO, MOSI - Communication Interface
```

### 3. Add LED Circuits
Design circuits for 6 LEDs:

1. Add LED components (6) from the library
2. Add current-limiting resistors for each LED (recommend 220Ω-1kΩ, depending on LED type and brightness requirements)
3. Connect LED cathodes to GND
4. Connect LED anodes to different GPIO pins through current-limiting resistors (can use D0-D5)

### 4. Add Button Circuit
1. Add a button component from the library (e.g., SW_Push)
2. Add a 10kΩ pull-up resistor
3. Connect one end of the button to a GPIO pin (such as D6)
4. Connect the other end of the button to GND
5. Connect the pull-up resistor between the GPIO pin and 3.3V

### 5. Complete the Schematic
Here is the recommended connection scheme:

```plain
LED1: D0/GPIO2 → 220Ω resistor → LED → GND
LED2: D1/GPIO3 → 220Ω resistor → LED → GND
LED3: D2/GPIO4 → 220Ω resistor → LED → GND
LED4: D3/GPIO5 → 220Ω resistor → LED → GND
LED5: D4/GPIO6 → 220Ω resistor → LED → GND
LED6: D5/GPIO7 → 220Ω resistor → LED → GND
Button: D6/GPIO21 → 10kΩ resistor → 3.3V
                 D6/GPIO21 → Button → GND
```

## III. PCB Design Steps
### 1. Set Up PCB Layout
1. After completing the schematic, click "Tools" → "Update PCB from Schematic"
2. In the PCB editor, set the board size (recommended slightly larger than XIAO ESP32C3, about 30mm x 25mm)
3. Set the grid size to 0.5mm or 0.25mm for precise positioning

### 2. Design the Socket
There are two methods to create a socket for XIAO ESP32C3:

#### Method One: Using Pin Headers
1. Find 1x7 pin headers in the PCB editor library, place two rows
2. Arrange them according to XIAO ESP32C3 pin spacing (2.54mm)

#### Method Two: Design a Card-Edge Connector
1. Create a custom footprint for direct insertion of the board
2. Add 14 pads arranged according to XIAO ESP32C3 pin spacing

### 3. Place Components
1. Place all components on the PCB (LEDs, resistors, button)
2. It's recommended to arrange LEDs on one side of the board for easy viewing
3. Place the button in a position convenient for operation

### 4. Routing
1. Use a two-layer PCB design (top and bottom layers)
2. First connect power and ground lines (GND and 3.3V)
3. Then connect LED and button circuits
4. Use 0.25mm trace width for signal lines
5. Use 0.5mm or wider trace width for power lines
6. Add a ground plane, fill unused areas with copper

### 5. Design Check
After completing the design, run the following checks:

1. DRC (Design Rule Check) to ensure no manufacturing rules are violated
2. ERC (Electrical Rule Check) to ensure electrical connections are correct
3. 3D view to confirm reasonable component placement

## IV. Bill of Materials
You'll need to prepare the following components:

+ 1x Pin headers (2x7 pins, 2.54mm pitch) for XIAO ESP32C3 socket
+ 6x LEDs (recommended 3mm or 5mm diameter, different colors available)
+ 6x 220Ω-1kΩ resistors (depending on LED specifications)
+ 1x Tactile switch
+ 1x 10kΩ resistor (for button pull-up)

## V. Code Example
After completing the hardware design, you can use the following Arduino code to test your extension board:

```c
// XIAO ESP32C3 Extension Board Test Code
// Control 6 LEDs and 1 button

// Define pins
const int LED_PINS[6] = {D0, D1, D2, D3, D4, D5}; // Pins connected to LEDs
const int BUTTON_PIN = D6;                        // Pin connected to button

// Record button state
int lastButtonState = HIGH;  // Initial state is high (not pressed)
int currentPattern = 0;      // Current LED display mode

void setup() {
    Serial.begin(115200);
    Serial.println("XIAO ESP32C3 Extension Board Test");

    // Set LED pins as outputs
    for (int i = 0; i < 6; i++) {
        pinMode(LED_PINS[i], OUTPUT);
        digitalWrite(LED_PINS[i], LOW); // Initial state is off
    }

    // Set button pin as input, enable internal pull-up resistor
    pinMode(BUTTON_PIN, INPUT_PULLUP);
}

void loop() {
    // Read button state
    int buttonState = digitalRead(BUTTON_PIN);

    // Button state change detection (from not pressed to pressed)
    if (buttonState == LOW && lastButtonState == HIGH) {
        delay(50); // Simple debounce

        // Check again to confirm it's not noise
        if (digitalRead(BUTTON_PIN) == LOW) {
            // Switch to next display mode
            currentPattern = (currentPattern + 1) % 4;
            Serial.print("Switched to mode: ");
            Serial.println(currentPattern);
        }
    }

    // Update last button state
    lastButtonState = buttonState;

    // Display LED effects based on current mode
    switch (currentPattern) {
        case 0:
            // Mode 0: All LEDs off
            allOff();
            break;
        case 1:
            // Mode 1: All LEDs on
            allOn();
            break;
        case 2:
            // Mode 2: Sequential LED blinking
            chaseEffect();
            break;
        case 3:
            // Mode 3: LED breathing effect
            breatheEffect();
            break;
    }
}

// Turn off all LEDs
void allOff() {
    for (int i = 0; i < 6; i++) {
        digitalWrite(LED_PINS[i], LOW);
    }
}

// Turn on all LEDs
void allOn() {
    for (int i = 0; i < 6; i++) {
        digitalWrite(LED_PINS[i], HIGH);
    }
}

// Sequential LED blinking effect
void chaseEffect() {
    for (int i = 0; i < 6; i++) {
        allOff();
        digitalWrite(LED_PINS[i], HIGH);
        delay(100);
    }
}

// LED breathing effect (using PWM)
void breatheEffect() {
    // Gradually brighten
    for (int brightness = 0; brightness <= 255; brightness += 5) {
        for (int i = 0; i < 6; i++) {
            analogWrite(LED_PINS[i], brightness);
        }
        delay(30);
    }

    // Gradually dim
    for (int brightness = 255; brightness >= 0; brightness -= 5) {
        for (int i = 0; i < 6; i++) {
            analogWrite(LED_PINS[i], brightness);
        }
        delay(30);
    }
}
```

## VI. Practical Tips
1. **Design Considerations**:
    - Note that GPIO2, GPIO8, and GPIO9 are Strapping pins, as stated in the documentation, the levels of these pins may affect the board's boot mode. Keep this in mind during design.
    - Avoid using A3(GPIO5) for analog readings, as it is susceptible to false sampling signals.
2. **Power Management**:
    - If battery power is needed, consider adding a battery connector to the design.
    - XIAO ESP32C3 has built-in battery charging and discharging management chips, but you'll need to add the appropriate connector.
3. **PCB Manufacturing Considerations**:
    - Use standard PCB thickness (1.6mm) to ensure compatibility with pin headers.
    - Set appropriate hole diameters and pad sizes, recommended hole diameter 0.8-1.0mm.

I hope this guide helps you design an extension board for the XIAO ESP32C3. If you need further information about specific KiCad operations, or have other questions about the design, please feel free to ask.

---

# Designing a PCB Based on AI Output
Next, I followed Claude's suggestions and began drawing a schematic in KiCad.

## Creating a New Project
I created a new project file in KiCad: XIAO_ESP32C3_Extension, as shown below.

![](/images/week06/w06-p-6.png)

Then I clicked on the schematic editor to enter schematic editing, as shown below.

![](/images/week06/w06-p-7.png)

## Adding XIAO ESP32C3 Symbol
Claude guided me to manually add the XIAO ESP32C3 symbol, but I didn't follow this suggestion. In the Seeed Wiki documentation: Getting Started with Seeed Studio XIAO ESP32C3, the [Resources](https://wiki.seeedstudio.com/XIAO_ESP32C3_Getting_Started/#resources) list provides a [Seeed Studio XIAO ESP32C3 KiCad Libraries](https://files.seeedstudio.com/wiki/XIAO_WiFi/Resources/Seeeduino-XIAO-ESP32C3-KiCAD-Library.zip) ZIP package, as shown below.

![](/images/week06/w06-p-8.png)

After downloading and extracting, I obtained the following files:

![](/images/week06/w06-p-9.png)

+ `MOUDLE-SEEEDUINO-XIAO-ESP32C3.KiCad_sym` is the symbol file we need.
+ `MOUDLE14P-SMD-2.54-21X17.8MM.KiCad_mod` is the corresponding footprint file.

In KiCad, I created a library named `XIAO`, and then imported the library by using the File > Import > Symbol function in the symbol editor to import the `MOUDLE-SEEEDUINO-XIAO-ESP32C3.KiCad_sym` symbol file, so I could directly use the XIAO ESP32C3 symbol in the schematic design.

![](/images/week06/w06-p-10.png)

> Adding MOUDLE-SEEEDUINO-XIAO-ESP32C3.KiCad_sym to KiCad's symbol library
>

Now I can directly add the XIAO ESP32C3 symbol to the schematic.

![](/images/week06/w06-p-11.png)

In the same way, I imported the `MOUDLE14P-SMD-2.54-21X17.8MM.KiCad_mod` footprint in the footprint editor, as shown below.

![](/images/week06/w06-p-12.png)

> Importing the XIAO footprint
>

Since multiple XIAO models have the same footprint specifications, I also imported another footprint from the XIAO RP2040's provided [KiCad Libraries](https://files.seeedstudio.com/wiki/XIAO-RP2040/res/Seeeduino-xiao-rp2040-KiCAD-Library.zip), as shown below. This footprint is more suitable for a XIAO with pins.

![](/images/week06/w06-p-13.png)

> Importing the XIAO footprint with pin holes from XIAO RP2040
>

## Adding LED and Button Circuits
Next, following Claude's suggestions, I added 6 LEDs with corresponding current-limiting resistors, and a button to the editing area. I connected the symbols using network labels according to the AI-provided connection suggestions below.

```plain
LED1: D0/GPIO2 -> 220Ω resistor -> LED -> GND
LED2: D1/GPIO3 -> 220Ω resistor -> LED -> GND
LED3: D2/GPIO4 -> 220Ω resistor -> LED -> GND
LED4: D3/GPIO5 -> 220Ω resistor -> LED -> GND
LED5: D4/GPIO6 -> 220Ω resistor -> LED -> GND
LED6: D5/GPIO7 -> 220Ω resistor -> LED -> GND
Button: D6/GPIO21 -> 10kΩ resistor -> 3.3V
              D6/GPIO21 -> Button -> GND
```

![](/images/week06/w06-p-14.png)

> Symbols connected via network labels in the schematic
>

You can select a network label and press the tilde key (~) in the upper left of the keyboard to see the same label connections. This approach makes the schematic very clean.

![](/images/week06/w06-p-15.png)

> In KiCad schematic editor, selecting a network label (GND) and pressing the tilde key (~) in the upper left of the keyboard highlights all identical network labels
>

## Adding a Single 8-Pin Connector Extension Header
After completing the initial connections, I noticed that some pins on the right side of the XIAO were not being used, so I exported the symbol field table and took a screenshot of the current schematic, then continued with a new request to Claude:

> I want to select a `Conn_01x08` as a single connector for all unused pins.
>

The following content enclosed by dividing lines is Claude's complete output.

---

# Using a Single 8-Pin Connector for Extension Header Design
Using a single `Conn_01x08` connector is a great choice that will make the design more concise while providing sufficient expandability. Below I will detail how to implement this design in KiCAD.

## 8-Pin Connector Design Plan
### 1. Recommended Pin Assignment
For your `Conn_01x08` connector, I recommend the following pin assignment:

```plain
1. 3.3V       - Power
2. GND        - Ground
3. D7/RX      - Serial receive
4. D6/TX      - Serial transmit (if button uses another pin)
5. D8/SCK     - SPI clock (Strapping pin)
6. D9/MISO    - SPI master input (Strapping pin)
7. D10/MOSI   - SPI master output
8. RST        - Reset signal (optional) or other available pin
```

### 2. Steps to Add Connector in KiCAD
1. In the schematic editor, press the "A" key or click the "Place Symbol" button
2. Enter "Connector" in the search box
3. Select "Connector_Generic:Conn_01x08"
4. Place the connector in a suitable location on the schematic (recommended on the right side)
5. Press "E" to edit connector properties, set reference designator to "J1" and add description "Extension Interface"

![](/images/week06/w06-p-16.png)

> Claude even provided a text drawing language diagram of the extension interface, though it's not very readable
>

## 3. Detailed KiCAD Implementation Steps
### Step 1: Add 8-Pin Connector
```plain
1. Open the schematic editor
2. Press "A" to open the "Add Symbol" dialog
3. Search for "Conn_01x08"
4. Select "Connector_Generic:Conn_01x08"
5. Click to place on the right side of the schematic
6. Press "E" to edit properties:
   - Reference: J1
   - Value: Expansion_Header
   - Add description: "8-pin expansion header"
```

### Step 2: Connect ESP32C3 Pins to Extension Header
```plain
1. Use the wire tool ("W" key) to connect:
   - 3.3V to J1 pin 1
   - GND to J1 pin 2
   - D7/RX to J1 pin 3
   - D6/TX to J1 pin 4 (if available, otherwise mark as NC)
   - D8/SCK to J1 pin 5
   - D9/MISO to J1 pin 6
   - D10/MOSI to J1 pin 7
   - RST or other available pin to J1 pin 8
```

### Step 3: Add Pull-Up Resistors (Critical Step!)
```plain
1. Press "A", search for "R"
2. Select "Device:R" and place 2
3. Press "E" to set resistor values to "10K"
4. Connect R8 between D8 and 3.3V
5. Connect R9 between D9 and 3.3V
```

### Step 4: Add Appropriate Labels
```plain
1. Press "T" to add text labels
2. Near the connector add:
   "J1 - Extension Interface
    1: 3.3V Power
    2: GND Ground
    3: RX Serial Receive
    4: TX Serial Transmit
    5: SCK* SPI Clock (*Strapping)
    6: MISO* SPI Data Input (*Strapping)
    7: MOSI SPI Data Output
    8: RST Reset or Extension"
3. Add warning label: "Note: Pins marked with * are Strapping pins"
```

## 4. PCB Layout Recommendations
In the PCB design:

1. **Placement**:
    - Place J1 connector at the edge of the PCB for easy connection to external devices
    - Standard 2.54mm (0.1 inch) pitch header
2. **Silkscreen Marking**:
    - Clearly mark each pin function on the PCB: "3V3", "GND", "RX", "TX", "SCK", "MISO", "MOSI", "RST"
    - Specially mark Strapping pins: "SCK*", "MISO*"
3. **Routing Considerations**:
    - Keep traces for sensitive signals (such as SPI lines) as short as possible
    - Add decoupling capacitors near 3.3V and GND pins
4. **Pull-Up Resistor Layout**:
    - Place 10K pull-up resistors near D8 and D9
    - Keep them as close to the ESP32C3 chip as possible

## 5. Usage Tips
After designing, initialize these pins correctly in software:

```cpp
void setup() {
  // Initialize serial
  Serial.begin(115200);
  
  // Configure Strapping pins
  pinMode(D8, INPUT_PULLUP); // Or configure as needed
  pinMode(D9, INPUT_PULLUP);
  
  // Configure other extension pins
  pinMode(D10, INPUT); // Or configure as needed
  pinMode(D7, INPUT);
  
  Serial.println("Extension interface initialized");
}
```

## 6. Extension Interface Applications
With this 8-pin extension interface, you can connect:

1. Displays: SPI LCD/OLED screens
2. Sensors: SPI interface sensors such as thermometers, accelerometers, etc.
3. Storage: SPI Flash or SD cards
4. Communication modules: Serial Bluetooth modules, LoRa modules, etc.
5. Debug interface: Connect debuggers via UART

This single connector design both simplifies PCB layout and retains full functional expandability, making it a very practical solution.

I hope this detailed design guide is helpful! If you have any questions or need more detailed explanations, please feel free to ask.

---

## Completing the Schematic
Based on Claude's suggestions above, I adjusted the schematic, which now looks as shown below, and performed an Electrical Rule Check (ERC).

![](/images/week06/w06-p-17.png)

> Completed schematic, with Electrical Rule Check (ERC)
>

Then in the symbol fields table, I selected footprints for different types of symbols, as shown below.

![](/images/week06/w06-p-18.png)

> Selecting corresponding footprints for symbols
>

After completing these steps, I was ready to enter the PCB editor for PCB design.

## PCB Design
According to Claude's routing suggestions:

> 1. Use a two-layer PCB design (top and bottom layers)
> 2. First connect power and ground lines (GND and 3.3V)
> 3. Then connect LED and button circuits
> 4. Use 0.25mm trace width for signal lines
> 5. Use 0.5mm or wider trace width for power lines
> 6. Add a ground plane, fill unused areas with copper
>

I set up 3 trace widths: 0.25mm, 0.5mm, and 1mm, as shown below.

![](/images/week06/w06-p-19.png)

Then I chose to update the PCB from the schematic, and after a long editing process, I finally saw the first circuit board I had ever drawn in my life, and added a cutting boundary in Edge.Cuts.

![](/images/week06/w06-p-20.png)

> The first PCB I've ever drawn
>

Pressing Option+3 (Alt+3 on Windows) allows you to view the 3D view of the PCB, as shown below.

![](/images/week06/w06-p-21.jpg)

At a colleague's suggestion, I tried adding copper fill and teardrops.

### Copper Fill
> Below is DeepSeek's explanation of the purpose of copper fill.
>
> PCB copper fill is a critical process to optimize circuit performance by filling idle areas with copper layers (usually connected to ground or power layers). Its main functions can be summarized as follows:
>
> #### 1. Reduced Ground Impedance, Enhanced Interference Resistance
> Copper fill significantly reduces ground impedance through large-area copper foil connection to ground, thereby reducing voltage drops and noise interference in signal loops, especially effective in suppressing electromagnetic interference (EMI) in high-frequency circuits. For example, separate copper fill areas for digital and analog circuits can prevent signal crosstalk.
>
> #### 2. Improved Heat Dissipation
> Copper's high thermal conductivity makes the copper layer a heat dissipation channel, particularly suitable for high-power components (such as processors, power amplifiers), preventing localized overheating by dispersing heat, extending device life.
>
> #### 3. Enhanced Mechanical Stability
> Copper layers can increase the rigidity of PCB boards, reducing the risk of bending and deformation during assembly or use, especially important for multilayer boards or thin substrates.
>
> #### 4. Optimized Power Efficiency and Current Carrying Capacity
> Large-area power layer copper fill can reduce voltage drops, improve power distribution efficiency, and support large current transmission, suitable for motor drives or power modules.
>
> #### 5. Electromagnetic Shielding and Signal Integrity
> In high-frequency circuits, copper layers as ground planes can reduce crosstalk between signal lines and shield external electromagnetic interference, ensuring signal transmission stability.
>
> #### Supplementary: Choice of Copper Fill Methods
> + Large-area copper fill: Suitable for low-frequency high-current scenarios, requiring slots to prevent warping due to thermal expansion and contraction.  
> + Grid copper fill: Commonly used in high-frequency circuits, balancing heat dissipation and shielding, reducing copper foil weight and stress.
>
> Through reasonable design of copper fill strategies, the electrical performance, reliability, and service life of PCBs can be significantly improved.
>

Open the copper fill window, typically selecting the F.Cu layer (copper layer), and selecting /GND in the network. As shown below.

![](/images/week06/w06-p-22.png)

> Note to select /GND
>

Then use the draw fill zone tool to encapsulate the area that needs copper fill, as shown below.

![](/images/week06/w06-p-23.png)

> Drawing the area for copper fill
>

After closing the area, you'll see hatched lines appear on the border, as shown below.

![](/images/week06/w06-p-24.png)

> A hatched border appears around the area designated for copper fill
>

To see the copper fill effect, you need to go to the top menu Edit > Fill All Zones (or press the shortcut key `B`), as shown below.

![](/images/week06/w06-p-25.jpg)

> Menu Edit > Fill All Zones
>

Now you can see the effect after successful copper fill.

![](/images/week06/w06-p-26.png)

### Adding Teardrops
> Below is Claude's explanation of the purpose of teardrops.
>
> Adding teardrops to PCB design serves several important purposes:
>
> 1. **Increased Mechanical Strength**: Teardrops enhance the mechanical strength at the junction of pads and traces, making them better able to withstand stress from vibration and thermal expansion, reducing the risk of fractures due to physical impacts.
> 2. **Improved Signal Transmission**: Teardrops can smooth high-frequency signal transmission paths, reducing impedance discontinuities at corners, helping to improve signal integrity in high-speed circuits.
> 3. **Optimized Manufacturing Process**: 
>     - Increased etching tolerance, reducing over-etching risk
>     - Improved drilling accuracy, reducing pad damage from drill misalignment
>     - Improved reliability in PCB assembly process
> 4. **Extended Service Life**: By reducing stress concentration points, teardrops can extend PCB life in thermal cycling and mechanical shock environments.
> 5. **Improved Coating Coverage**: Teardrop designs help solder mask and other coatings cover corners more evenly, preventing exposure and corrosion.
>
> Teardrops are primarily applied in the following scenarios: pad-to-via connections, high-speed signal routes, frequently bent flexible circuit boards, and areas that need to withstand mechanical stress. Although they increase design time, they typically significantly improve PCB reliability and performance.

After completing the copper fill operation, I also tried adding teardrops. You need to go to the top menu Edit > Add Teardrops, in the popup "Teardrop Settings" window, press the "Apply" button to see the teardrop effect, as shown below.

![](/images/week06/w06-p-27.png)

> Circuit board after adding teardrops
>

The final version of the PCB 3D rendering is shown below.

![](/images/week06/w06-p-28.png)

> Special thanks to Qian Jiatao and Zhou Zhihuai for providing extensive guidance and help during my PCB design learning process.
>

# Design Improvement for CNC Milling PCB Manufacturing

Since we will be using CNC milling to manufacture circuit boards in Week 8, the current design uses through-hole packages, so I'm preparing to revise a version that will meet the requirements for future assignments.

### Creating a Project Copy
First, to preserve the original design, I created a project copy:

1. In the KiCad file manager, I selected the original project folder.
2. Copied and renamed it to "XIAO_ESP32C3_Extension_CNC2".
3. The new project files are shown in the image below.

![](/images/week06/w06-p3-1.png)

> Project copy: XIAO_ESP32C3_Extension_CNC2
>

### Downloading the Fab Component Library
I used the component library provided by Fab Academy, which is more suitable for CNC milling manufacturing:

Fab Electronics Library for KiCad: [https://gitlab.fabcloud.org/pub/libraries/electronics/kicad](https://gitlab.fabcloud.org/pub/libraries/electronics/kicad)

The repository homepage is shown below. There are multiple ways to obtain the library, downloading the zip file is one option, as shown below.

![](/images/week06/w06-p3-2.png)

> You can obtain the component library by downloading the zip package
>

You can also use git commands to download the Fab component library:

```plain
git clone https://gitlab.fabcloud.org/pub/libraries/electronics/kicad.git
```

After downloading, I placed the library in a directory where I could easily find it. I created a `/kicad/libraries/fab` directory to store the component library, as shown below.

![](/images/week06/w06-p3-3.png)

> The fab component library after downloading and unpacking
>

### Configuring Library Paths
+ In the main project interface, go to "Preferences" > "Configure Paths".
+ Add a new path, named `FAB`, set the path to the downloaded library folder: `/Users/<USER>/kicad/libraries/fab/`. As shown below.
+ This step is very important, otherwise the 3D view will not display correctly.

![](/images/week06/w06-p3-4.png)

> Configuring paths for the component library
>

### Adding Symbol Libraries in KiCad
+ Open "Preferences" > "Manage Symbol Libraries".
+ Click the "Browse" button and navigate to the downloaded library folder.
+ Add the `fab.kicad_sym` symbol library file, as shown below.

![](/images/week06/w06-p3-5.png)

> Adding the Fab symbol library
>

### Adding Footprint Libraries
+ Open "Preferences" > "Manage Footprint Libraries".
+ Click the "Browse" button and navigate to the downloaded library folder.
+ Add the `fab.pretty` footprint library folder.

![](/images/week06/w06-p3-6.png)

> Adding the fab.pretty footprint library
>

### Replacing Existing Component Symbols and Footprints with Fab Library
I modified the schematic to replace through-hole components with SMD components:

1. Open the schematic editor.

![](/images/week06/w06-p3-7.png)

> Preparing to replace components with Fab library components in the schematic editor
>

2. Select the component symbol you want to replace, right-click to open the context menu, select "Replace Symbol", then search for xiao and select Module_XIAO-ESP32C3 from the Fab directory. Note that this provides more pins than the symbol provided by Seeed because it includes the pins on the bottom of the XIAO ESP32C3, as shown below.

![](/images/week06/w06-p3-8.png)

> Replacing XIAO ESP32C3 with the component from the Fab library
>

After replacement, you'll notice that the network labels set for the original XIAO ESP32C3 don't match the pins of the new XIAO ESP32C3, as shown in the red box below. These need to be readjusted.

![](/images/week06/w06-p3-9.png)

> The network labels set for the original XIAO ESP32C3 don't match the pins of the new XIAO ESP32C3
>

Readjust the correspondence between network labels and pins, after adjustment it looks like this:

![](/images/week06/w06-p3-10.png)

> Readjusting the correspondence between network labels and pins
>

3. Find and continue to replace the following components, you can select multiple components of the same specification at once and perform operations similar to the previous step to batch replace components.
+ Batch replace all LEDs with "LED-1206" (using SMD LEDs from the Fab library).
+ Batch replace all resistors with "R-1206" (using SMD resistors from the Fab library).
+ Replace the button with "Switch_Tactile_Omron".
+ Note that in "Replace specified symbol", you need to select components from the Fab library in the "New library identifier" field, as shown below.

![](/images/week06/w06-p3-11.jpg)

> Replacing the button component with one from the Fab library
>

Now all components have been replaced with those from the Fab library, and all connections have been modified, as shown below.

![](/images/week06/w06-p3-12.png)

> All components have been replaced with those from the Fab library.
>

4. Click on the symbol fields table, in the `Footprint` column, replace the footprint for each component one by one. Note that you can turn on the 3D model display switch at the bottom, as shown below. Now you can see both the footprint and 3D effect of the selected component simultaneously.

![](/images/week06/w06-p3-13.jpg)

> Assigning corresponding footprints to the new components one by one, and checking if the 3D models display correctly
>

![](/images/week06/w06-p3-14.png)

> Symbol fields table after reassigning all footprints
>

Now you can export the Bill of Materials:

[XIAO_ESP32C3_Extension_CNC2.csv](https://www.yuque.com/attachments/yuque/0/2025/csv/2392200/1741997642191-9e204520-b562-4cec-a341-a6f1a27f9089.csv)

| Reference | Qty | Value | DNP | Exclude from BOM | Exclude from Board | Footprint |
| --- | --- | --- | --- | --- | --- | --- |
| D1,D2,D3,D4,D5,D6 | 6 | LED_1206 | | | | Fab:LED_1206 |
| J1 | 1 | PinHeader_01x08_P2.54mm_Horizontal_SMD | | | | Fab:PinHeader_01x08_P2.54mm_Horizontal_SMD |
| M1 | 1 | Module_XIAO-ESP32C3 | | | | Fab:SeeedStudio_XIAO_ESP32C3 |
| R1,R2,R3,R4,R5,R6,R7,R8,R9 | 9 | R_1206 | | | | Fab:R_1206 |
| SW1 | 1 | Switch_Tactile_Omron | | | | Fab:Button_Omron_B3SN_6.0x6.0mm |

### PCB Layout Redesign
Since the components and footprints have been changed, the PCB layout and routing need to be redesigned. I switched to the PCB editor, as shown below. The original design is no longer applicable, so I selected all and deleted.

![](/images/week06/w06-p3-15.png)

> The original PCB design is no longer applicable
>

1. Click "Update PCB from Schematic" (shortcut key F8) to place all components in the editing area, as shown below.

![](/images/week06/w06-p3-16.png)

> Updating and placing all components from the schematic
>

![](/images/week06/w06-p3-17.jpg)

> Press Option+3 (Alt+3 on Windows) to see all models in 3D mode
>

2. Adjust PCB design rules to accommodate CNC milling process limitations:
   - Set trace width to at least 0.4mm
   - Set clearance to at least 0.4mm
   - Avoid using vias in the design

The board configuration is shown below.

![](/images/week06/w06-p3-18.png)

> Board configuration panel settings
>

3. Adjust the component layout to minimize trace crossings.

![](/images/week06/w06-p3-19.png)

> Adjusting component layout to achieve an initial reasonable arrangement
>

4. Use the "Route" tool (shortcut key "x") with a 0.5mm trace width to reconnect all traces. After routing is complete, the component positions have changed because for a single-layer board, component positioning needs to consider avoiding trace crossing issues.

![](/images/week06/w06-p3-20.png)

> Completing initial routing, this process is quite time-consuming and requires many attempts
>

5. Ensure all traces are on the top layer (F.Cu) of the circuit board, do not use the bottom layer.
6. Add text or emoji to the board design (optional).
7. Switch to the `Edge.Cuts` layer and draw the circuit board boundary, as shown below.

![](/images/week06/w06-p3-21.png)

> Adding text and cutting boundary
>

8. In the `Edge.Cuts` layer, select the border, open the context menu, and set a 3mm radius rounded corner for the board boundary, as shown below.

![](/images/week06/w06-p3-22.png)

> Setting a 3mm radius rounded corner for the border
>

### Re-adding Teardrops
1. Select "Edit" > "Add Teardrops" from the top menu.
2. In the "Teardrop Settings" popup window, configure teardrop parameters suitable for CNC manufacturing.
3. Click the "Apply" button to see the effect.
4. Confirm that the teardrop design doesn't make traces too narrow.

![](/images/week06/w06-p3-23.png)

> Circuit board after adding teardrops
>

### Re-adding Copper Fill
1. Select the F.Cu layer, select the "Draw Fill Zone" function on the right, click in the editing area, in the copper zone properties window that appears, select `GND` in the network.
2. Draw the copper fill area, surrounding the entire circuit board design area.
3. Click menu "Edit" > "Fill All Zones" or press shortcut key "B".
4. Ensure the copper fill area maintains sufficient distance from signal lines.

The effect after completing the copper fill operation is shown below:

![](/images/week06/w06-p3-24.png)

> Circuit board after completing copper fill operation, GND parts are connected together
>

### Running DRC Check
Run a design rule check before exporting files:

1. Click "Inspect" > "Run DRC" from the top menu
2. Ensure there are no errors, though I have many warnings about text and copper connections being too narrow.
3. Specifically check that spacing and trace widths meet CNC manufacturing requirements.

There don't seem to be any major issues, though there are obvious problems with the position of many silkscreen texts, as shown below. These can be adjusted.

![](/images/week06/w06-p3-25.png)

> Run DRC check, make sure errors are 0
>

Adjust the position of some silkscreen text, as shown below.

![](/images/week06/w06-p3-26.png)

> Adjusted the position of some silkscreen text.
>

Finally, use the 3D viewer to see the rendering.

![](/images/week06/w06-p3-27.png)

> The final PCB design in the 3D viewer
>

### Exporting Gerber Files
1. Click "File" > "Export" > "Gerber", as shown below.

![](/images/week06/w06-p3-28.png)

> Preparing to export Gerber files for manufacturing
>

2. Make sure to select the following layers:
   - F.Cu (top copper)
   - Edge.Cuts (cutting edges)
3. Set appropriate export options:
   - Use 4.6 format
   - Set units to millimeters
   - Set export origin to coordinate origin

The "Plot" window is shown below.

![](/images/week06/w06-p3-29.png)

> In the Plot window, select F.Cu (top copper) and Edge.Cuts (cutting edge) layers
>

After clicking the "Plot" button in the "Plot" window, two exported `.gbr` files will appear in the project folder, as shown below.

![](/images/week06/w06-p3-30.png)

> Two exported `.gbr` files appear in the project folder
>

### Using Gerber2Png to Export PNGs for Cutting
To use MOD software for CNC cutting, we need to convert Gerber files to PNG format:

1. Visit the online tool [Gerber2Png](https://gerber2png.fablabkerala.in/)
2. Upload the generated Gerber files (F_Cu.gbr and Edge_Cut.gbr), you can select both files together for upload. After uploading, it looks like this:

![](/images/week06/w06-p3-31.png)

> Interface after uploading two .gbr files
>

3. Set export parameters:
+ In Quick Setup, first select `Top trace`, click the `Generate PNG` button, and you'll see a copper layer PNG on the right.
+ Then in Quick Setup, select `Top Cut`, click the `Generate PNG` button, and you'll see an edge cutting layer PNG added on the right, as shown below.

![](/images/week06/w06-p3-32.png)

> Gerber2Png export interface
>

4. Click the `DOWNLOAD ZIP` button on the right to get a zip file `gerber_files_2.zip`.
5. After extracting, you can see two generated PNG files, as shown below:
+ traces_top_layer_0.png (for copper layer milling)
+ outline_top_layer_1.png (for circuit board outline cutting)

![](/images/week06/w06-p3-33.png)

> After downloading and extracting, you can see 2 PNG files.
>

Here are the source files for the 2 files.

![](/images/week06/traces_top_layer_0.png)

![](/images/week06/outline_top_layer_1.png)

These PNG files will be used in Week 8, at which time G-code will be generated through MOD software for CNC machines to produce physical circuit boards. Using surface mount technology and single-sided PCB design ensures that the circuit board can be successfully manufactured using CNC milling methods.