---
layout: doc
title: "Week 6: Electronics Design and Production | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 6: Learn electronics design fundamentals, various electronic components and PCB design tools"
head:
  - - meta
    - name: keywords
      content: fab academy, electronics design, PCB, KiCad, electronic components, microcontrollers, schematic design, circuit board design
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 5: 3D Pen Holder Assignment'
  link: '/en/assignments/week05/week05_3d-pen-holder-assignment_en'
next:
  text: 'Week 6: Individual Assignment: First Attempt at PCB Design'
  link: '/en/assignments/week06/week06_individual_assignment_pcb_design_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

#  Week 6: Electronics Design
> This document was generated by AI synthesis after I provided Claude 3.7 Sonnet with the [course outline](http://academy.cba.mit.edu/classes/electronics_design/index.html) content and the transcript from this course's video conference session.
>

## Course Summary
This lesson introduces the fundamentals of electronics design and tools, highlighting common electronic components, circuit design software (EDA tools), and the use of testing equipment. The course is divided into two main parts: first, introducing common electronic components and their functions; second, detailing the process of designing circuit boards using EDA tools. This week's assignment is to design a microcontroller-based development board in preparation for the upcoming electronics production course.

## Detailed Course Content
### I. Course Introduction
Two weeks ago, we learned about embedded programming; this week, we'll learn about electronics design; and in two weeks, we'll proceed to electronics production. This course focuses on learning about electronic components and circuit design tools, with the ultimate goal of designing a development board around a microcontroller and producing it two weeks later.

### II. Design Inspiration
First, let's look at some excellent design examples:

1. [Adrián Torres' fab xiao](https://fabacademy.org/2020/labs/leon/students/adrian-torres/fabxiao.html): A feature-rich development board with multiple interface protocols that can connect to various devices.
2. [Ani Liu's artistic circuit design](https://fab.cba.mit.edu/classes/863.15/section.CBA/people/Liu/): Circuit board design can be not only functional but also artistic. For example, one student designed the instructor's portrait as a circuit board.
3. [Kai Zhang's programming-assisted design](https://fab.cba.mit.edu/classes/863.23/Harvard/people/Kai/index.html#week_6): A student with strong programming skills wrote his own software to aid in design, creating aesthetically pleasing circuits.

These examples demonstrate that circuit design can be both functional and creative/artistic.

### III. Introduction to Electronic Components
#### 1. Wires
+ Ribbon cables are more convenient, allowing multiple wires to be kept together while also being able to be separated for individual use
+ Wire specifications are expressed in AWG, with appropriate thickness chosen based on current requirements

#### 2. Connectors
+ Connectors are used for detachable connections, as opposed to fixed connections
+ IDC (Insulation Displacement Connectors) allow simultaneous connection of multiple wires without having to process each one individually
+ When using, place the ribbon cable in the connector and press to make the sharp edges penetrate the insulation and contact the wire

#### 3. Buttons & Switches
+ Buttons: Usually normally open, connecting two sides when pressed
+ Switches: Can be fixed in a particular position, used for on/off or configuration
+ These are surface-mount devices (SMD), not through-hole components

#### 4. Resistors
+ Ohm's Law: I = V/R
+ Analogous to a narrow channel in water flow, limiting the flow rate
+ Commonly used for current limiting, such as in LED circuits
+ Specifications include: 
    - Package size (e.g., 1206, which means 12/1000 × 6/1000 inches)
    - Power rating (e.g., 1/4 watt)
    - Resistance value and precision
+ The 1206 package is primarily used to allow routing traces underneath the resistor when using a 1/64-inch mill for circuit board fabrication

#### 5. Capacitors
+ Capacitance law: C = Q/V, current I = C·dV/dt
+ Analogous to a water reservoir
+ Functions: 
    - Used as filters
    - Setting response time
    - Large capacitors (supercapacitors) can be used for short-term energy storage, replacing batteries
    - Filtering unstable power supplies

#### 6. Crystals
+ Used for precise timing
+ Structure similar to capacitors with special materials
+ When voltage is applied, the material bends and produces voltage, can repeat very precisely
+ Required in complex processors, but some microcontrollers (like XIAO) have built-in crystals

#### 7. Inductors
+ Opposite to capacitors, voltage is proportional to the rate of change of current
+ Used as high-frequency filters, allowing only low frequencies to pass

#### 8. Diodes
+ Structure: Anode and Cathode
+ Current can only flow from anode to cathode
+ Uses: 
    - Protecting circuits from damage due to reverse battery connection
    - Rectification
+ Special types: 
    - Zener diodes (used for precise voltage references)
    - LEDs (Light Emitting Diodes, require current-limiting resistors)

#### 9. Transistors
+ Two types: 
    - Bipolar (generally not used)
    - MOSFET (primarily used)
+ MOSFET characteristics: 
    - Divided into N-type (current sink) and P-type (current source)
    - Have Gate, Source, and Drain
    - Can control resistance, used for switching loads
    - Lower RDS (drain-source resistance) is better, reducing power consumption and heat
    - Logic-level transistors are preferred for easier control

#### 10. Regulators
+ Convert high voltage (e.g., 9V battery) to stable low voltage (e.g., 3.3V)
+ Require output capacitors as buffers
+ Simple regulators are inefficient; DC-DC converters can be used for improved efficiency

#### 11. Amplifiers
+ Used for measuring sensitive signals or producing power signals
+ Applications: Microphone signal amplification, speaker driving
+ Modern microcontrollers and modules often integrate amplifier functions

#### 12. Microcontrollers
+ Include various functional pins: 
    - Power and ground
    - Analog pins (for reading voltage)
    - Digital pins (for outputting or reading logic signals)
    - Communication protocols: I2C, UART, SPI, etc.
+ Selecting an appropriate microcontroller: 
    - RP 2040: Powerful, customizable processor
    - ESP32: Suitable for wireless communication (WiFi, Bluetooth)
    - ARM: Supports USB communication
    - AVR: Small, low-cost processors

### IV. Circuit Design Fundamentals
#### 1. Basic Circuit Principles
+ Current (amperes) and voltage (volts)
+ Power: P = I²R = IV
+ Kirchhoff's Laws: 
    - Current Law: Sum of currents at a node is zero
    - Voltage Law: Sum of voltages around a loop is zero

#### 2. Electronic Design Automation (EDA) Tools
EDA tools have a steep learning curve but are essential for complex circuit design. The main workflow includes:

1. Sketching design on paper or tablet
2. Inputting schematic (connecting components)
3. Component placement (on PCB)
4. Routing (connecting wires)
5. Simulation (optional)
6. Manufacturing

Factors to consider:

+ Number of layers (single, double, etc.)
+ Power and ground planes
+ Component footprints
+ 3D models (convenient for mechanical design)
+ Design rules (trace width, spacing, etc.)

#### 3. Main EDA Tools Introduction
##### [KiCad](https://www.kicad.org/)
+ Free, open-source, cross-platform
+ Powerful, but with lower integration between modules
+ Features: 
    - Recently released KiCad 9, with significantly updated performance
    - Rich library resources
    - Supports push routing, assisted auto-routing

Demonstration workflow:

1. Creating a project
2. Designing schematics
3. Component placement
4. PCB routing
5. Setting boundaries
6. 3D viewing
7. Exporting manufacturing files

##### [Eagle](https://www.autodesk.com/products/eagle/overview) (integrated in Fusion 360)
+ Commercial software
+ Fully integrated with Fusion 360, allowing seamless switching between electronic and mechanical design
+ Suitable for users already using Fusion 360

##### Other Tools
+ [LibrePCB](https://librepcb.org/), [EasyEDA](https://easyeda.com/)
+ [OrCAD](https://www.ema-eda.com/products/cadence-orcad/why-orcad), [Cadence](https://www.cadence.com/en_US/home.html) (industrial-grade tools)
+ Integrated circuit design tools such as [Magic](http://opencircuitdesign.com/), [KLayout](https://www.klayout.de/), etc.

#### 4. Library Resources
+ Component libraries are an important part of EDA tools
+ Include: schematic symbols, PCB footprints, 3D models
+ Recommended resources: 
    - [Fab Library](https://gitlab.fabcloud.org/pub/libraries/electronics) (maintained by Chris, includes most components used in the course)
    - [Component Search Engine](https://componentsearchengine.com/)
    - [Ultra Librarian](https://www.ultralibrarian.com/)
    - [SnapEDA](https://www.snapeda.com/)

#### 5. Hardware Description Languages
+ Used for programmatic description of circuits
+ Essential for very complex circuits (such as billion-transistor processors)
+ Types: 
    - [Verilog](https://www.verilog.com/), [VHDL](http://valhalla.altium.com/Learning-Guides/TR0114%20VHDL%20Language%20Reference.pdf) (traditional HDLs)
    - [pcb.py](https://gitlab.cba.mit.edu/pub/libraries/-/tree/master/python), SVG-PCB, JSON-PCB (more modern tools)
+ Can describe relationships between components, rather than clicking and dragging

#### 6. Circuit Simulation
+ Verify design before actual production
+ Simulation types: 
    - Digital simulation (Wokwi)
    - Analog simulation (Falstad)
    - Mixed-signal simulation
+ SPICE framework: 
    - LTspice, ngspice
    - KiCad and Eagle both have SPICE interfaces

### V. Testing Equipment
Once a circuit is designed and produced, it typically needs testing and debugging. Common testing tools include:

1. Bench Power Supply
    - Adjustable voltage and current
    - Very useful for applications such as motor control
2. Multimeter
    - Measures voltage, current, resistance
    - First used to check if voltages in the circuit are normal
3. Oscilloscope
    - Displays signals as they change over time
    - View signal waveforms rather than just averages
4. Logic Analyzer
    - Not only displays signals but also interprets their meaning
    - Used for debugging communication protocols (such as I2C)
5. Mixed Signal Analyzer
    - Combines oscilloscope and logic analyzer functions
    - Can monitor multiple signals and decode protocols

### VI. Design Recommendations
1. Start with simple circuits to become familiar with the tools
2. Use existing library resources to avoid reinventing the wheel
3. When routing, view it as an interesting puzzle challenge
4. Use simulation to verify designs in complex projects
5. Learn to use testing equipment to debug circuit issues

## Assignment Requirements
### Group Assignment
Use the testing equipment in the lab to observe the operation of microcontroller circuit boards:

+ Use a digital multimeter to check voltages on the circuit board
+ Use an oscilloscope to view power supply noise
+ Use a logic analyzer to decode communication protocols (such as USB commands)

### Individual Assignment
Design a development board using EDA tools:

1. Choose a microcontroller (such as RP2040, ESP32, ARM, or AVR series)
2. Design a development board around this microcontroller
3. Consider interfaces for input/output devices you'll need to connect in the future
4. Design communication methods (such as USB, WiFi, etc., depending on the chosen microcontroller)

### Bonus Projects
1. Verify your design through simulation
2. Try different design workflows and compare them
3. Design a case for your development board (requires exporting the electronic design to a CAD environment)

## Learning Resources
1. Electronic Components
    - [Digikey Electronic Components Catalog](https://www.digikey.com/)
    - [Standard Resistor Values](https://www.electronics-notes.com/articles/electronic_components/resistors/standard-resistor-values-e-series-e3-e6-e12-e24-e48-e96.php)
    - [The Art of Electronics](https://artofelectronics.net/) - Classic electronics reference book
2. EDA Tools
    - [KiCad Official Website](http://kicad.org/)
    - [Eagle/Fusion 360](https://www.autodesk.com/products/eagle/overview)
    - [KiCad Design Rules](https://docs.oshpark.com/design-tools/kicad/kicad-design-rules/)
    - [KiCad Fab Library](https://gitlab.fabcloud.org/pub/libraries/electronics)
3. Simulation Tools
    - [Falstad Online Circuit Simulator](https://www.falstad.com/circuit/)
    - [Wokwi](https://wokwi.com/)
    - [LTspice](https://www.analog.com/en/design-center/design-tools-and-calculators/ltspice-simulator.html)
4. Reference Designs
    - [Adrian's Fab Shell](https://fabacademy.org/2020/labs/leon/students/adrian-torres/fabxiao.html)
    - [Component Search Engine](https://componentsearchengine.com/)
5. Testing Equipment
    - [Saleae Logic Analyzer](https://www.saleae.com/)
    - [SainSmart Test Equipment](https://www.sainsmart.com/products/)
