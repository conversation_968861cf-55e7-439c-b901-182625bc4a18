---
layout: doc
title: "Week 4: Grove Beginner Kit and XIAO Series Development Boards | Lei Feng Fab Academy 2025"
description: "Fab Academy 2025 Week 4 Guide to Using Grove Beginner Kit and XIAO Series Development Boards"
head:
  - - meta
    - name: keywords
      content: fab academy, Grove Kit, XIAO board, graphical programming, Arduino IDE, embedded development
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 4: Embedded Programming'
  link: '/en/assignments/week04/week04_embedded_programming_en'
next:
  text: 'Week 4: Embedded Development Practice with XIAO MG24'
  link: '/en/assignments/week04/week04_individual_assignment_xiao_mg24_sense_embedded_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Group Assignment: Grove Beginner Kit and XIAO Series Courses

This week's group assignment can be found at: [https://fabacademy.org/2025/labs/chaihuo/docs/week4/chaihuo/week4_group_assignment](https://fabacademy.org/2025/labs/chaihuo/docs/week4/chaihuo/week4_group_assignment)

The Chaihuo Makerspace group assignment focused on practical exploration of embedded programming. Group members compared different microcontroller architectures and development tools, with special attention to the application and performance comparison of XIAO series development boards. Hongtai Liu's work provided detailed comparisons of different models such as XIAO ESP32C3, XIAO ESP32S3, and XIAO ESP32C6, covering key parameters like processor performance, wireless connectivity capabilities, and power consumption characteristics, providing reference for selecting the appropriate development board.

As an employee of [Seeed Studio](https://www.seeedstudio.com/), I have written several introductory courses and books on embedded development, which I would like to introduce to beginners.

## Graphical Programming with Grove Beginner Kit For Arduino
For learners with no coding experience, you can start with graphical programming, such as this course: [Grove Beginner Kit For Arduino: Codecraft Graphical Programming Course](https://files.seeedstudio.com/wiki/Grove-Beginner-Kit-For-Arduino/res/Grove-Beginner-Kit-For-Arduino-Codecraft-Graphical-Programming-Course-web-v7.pdf).

![](/images/week04/w04-beginner-kit-course.png)

This course aims to help beginners master the [Grove Beginner Kit for Arduino](https://www.seeedstudio.com/Grove-Beginner-Kit-for-Arduino-p-4549.html) through the [Codecraft](https://ide.tinkergen.com/) graphical programming environment. The kit includes an Arduino-compatible board (using ATmega328P-MU) and 10 input and output modules, all connected through PCB design without requiring additional Grove cables. The course starts from basics and gradually guides learners through multiple projects, covering everything from simple LED control to complex sensor applications.

![](/images/week04/w04-beginner-kit-course-2.png)

> The 10 input and output modules of Grove Beginner Kit for Arduino
>

![Board](/images/week04/codecraft-1.jpg)

> Homepage of the Codecraft graphical programming tool
>

The advantage of graphical programming is that you don't need to write any code; the programming process is like building with blocks. Let's write a Blink program to make the LED module on the Grove Beginner Kit for Arduino flash.

As shown below, drag the setup (code that runs once when the board powers up) and loop (code that repeatedly runs after setup) blocks from the Start section to the programming area.

![](/images/week04/grove-beginner-kit-2.jpg)

> Dragging setup and loop blocks to the programming area
>

Then add 2 LED blocks, set the pin to D4, and set ON and OFF states, and add 2 Delay blocks, setting the delay time to 1 second, as shown in the program below.

![](/images/week04/codecraft-2.png)

> Blink program in Codecraft
>

After completion, simply connect the [Grove Beginner Kit for Arduino](https://www.seeedstudio.com/Grove-Beginner-Kit-for-Arduino-p-4549.html) to your computer, then click the Upload button. Once the program is uploaded, you'll see the LED start blinking, as shown below.

![](/images/week04/w04-beginner-kit-course-3.jpg)

> LED being lit up
>

Another advantage of Codecraft is that it can convert the program blocks into C code, as shown below. If you're just starting to learn code programming, you can first complete the programming using blocks, then convert it to code to understand how the program is written. This code can be directly copied into environments like Arduino IDE to run.

![Board](/images/week04/w04-beginner-kit-course-4.jpg)

> Converting Blink block program to C code in Codecraft
>

## Seeed Studio XIAO Series Courses Based on Arduino IDE
If you want to try different MCU development boards, check out the [Seeed Studio XIAO Series](https://www.seeedstudio.com/xiao-series-page). XIAO is an ultra-compact, high-performance development board, thumb-sized, designed specifically for IoT and AI applications. The series is Arduino-compatible, supports rapid prototyping, and is suitable for space-constrained projects. The XIAO series offers various models to meet different needs, including versions supporting Wi-Fi, Bluetooth, Zigbee, and other wireless connections, as well as versions with integrated sensors and AI capabilities. Additionally, the XIAO series provides rich expansion boards and accessories, making it convenient for developers to create various creative projects.

Hongtai Liu from Chaihuo Makerspace has provided a [comparison of different XIAO boards](https://fabacademy.org/2025/labs/chaihuo/students/hongtai-liu/docs/assignments/week04/00-group-assignment/) in his group assignment.

I have also written Arduino IDE-based courses for XIAO, such as this one co-authored with Professor [Marcelo Rovai](https://github.com/Mjrovai): [XIAO: Big Power, Small Board, Mastering Arduino and TinyML](https://mjrovai.github.io/XIAO_Big_Power_Small_Board-ebook/).

![](/images/week04/w04-xiao-book-1.jpg)

If you don't want to read such a long tutorial, you can check out my individual assignment section:

[Week 4 Individual Assignment: Embedded Development Practice with XIAO MG24 Sense](/en/assignments/week04/week04_individual_assignment_xiao_mg24_sense_embedded_en)