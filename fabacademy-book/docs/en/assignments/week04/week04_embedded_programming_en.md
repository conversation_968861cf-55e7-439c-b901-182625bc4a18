---
layout: doc
title: "Week 4: Embedded Programming | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 4 Guide to Embedded Programming Tools and Methods"
head:
  - - meta
    - name: keywords
      content: fab academy, embedded programming, microcontroller, Arduino, MCU, hardware development
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 3: Vinyl Cutting'
  link: '/en/assignments/week03/week03_vinyl_cutting_assignment_en'
next:
  text: 'Week 4: Grove Beginner Kit and XIAO Series Development Boards'
  link: '/en/assignments/week04/week04_group_assignment_grove_beginner_kit_xiao_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Week 4: Embedded Programming

## Course Overview
Welcome to the world of embedded programming! This course will lead you into a magical world of miniature computing. Imagine placing a chip the size of your fingernail on your fingertip, and within this tiny chip lies a complete computer system - this is the microcontroller we will explore.

This course forms the foundation of electronic design and manufacturing series, opening the door to embedded systems. Through this course, you will learn:

1. How to choose suitable microcontrollers
2. Understanding processor architecture and memory systems
3. Mastering basic programming skills
4. Learning debugging techniques
5. Hands-on project development

### Why Learn Embedded Programming?
Embedded systems are everywhere in our daily lives:

+ Processors controlling temperature and time in smart appliances
+ Chips monitoring health data in wearable devices
+ Automation controllers in industrial control systems
+ Even simple LED light controllers

Through this course, you will be able to design and develop these fascinating systems.

## Part One: Microcontroller Basics
### Understanding Microcontrollers
#### What is a Microcontroller?
A microcontroller is a complete computer system integrating processor core, memory, and input/output interfaces. Taking the ATtiny412 microcontroller, which costs only 50 cents, as an example, it includes:

+ 8-bit CPU core
+ Program memory (Flash)
+ Data memory (RAM)
+ Multiple input/output interfaces
+ Timers
+ Analog-to-digital converter

This tiny chip functions like a miniature factory, capable of sensing the environment, processing data, and controlling other devices.

### Processor Architecture
#### Von Neumann vs Harvard Architecture
These two architectures represent two fundamental design philosophies in computer systems:

**Von Neumann Architecture:**

+ Programs and data stored in the same memory space
+ Advantages: Simple structure, low hardware cost
+ Disadvantages: Potential program and data access conflicts
+ Applications: Mainly used in personal computers

**Harvard Architecture:**

+ Programs and data use separate storage spaces and buses
+ Advantages: Can access instructions and data simultaneously, higher efficiency
+ Applications: Used in most microcontrollers
+ History: Originated from early computer design at Harvard University

#### RISC vs CISC
The processor's instruction set architecture determines how it executes commands:

**RISC (Reduced Instruction Set Computing):**

+ Few instructions, simple and direct
+ Most instructions complete in one clock cycle
+ Low power consumption, fast execution
+ Representatives: ARM processors, RISC-V architecture

**CISC (Complex Instruction Set Computing):**

+ Many instructions, single instructions can complete complex operations
+ High code density, flexible programming
+ Relatively high power consumption
+ Representatives: x86 processors

## Part Two: Major Processor Series
### 1. AVR Series - Best Choice for Beginners
Like choosing an entry-level car, the AVR series offers reliable and easy-to-use features:

**Core Features:**

+ 8-bit processor, simple to use
+ Affordable (about 50 cents)
+ Wide operating voltage range (1.8-5.5V)
+ Considerable running speed (20MHz)

**Advantages:**

+ Single-pin programming, simple circuit design
+ Rich resources, low entry barrier
+ Low power consumption, suitable for battery power
+ High reliability, industrial-grade quality

**Common Models:**

+ ATtiny412: Compact and elegant, suitable for space-limited scenarios
+ ATtiny44: More pins, richer functionality
+ ATtiny1614: Enhanced analog capabilities

### 2. ARM Series - Performance Representative
If AVR is an entry-level car, ARM is like a performance vehicle, offering more powerful features:

**Core Features:**

+ 32-bit processor architecture
+ High clock frequency (48MHz+)
+ Rich peripheral interfaces
+ Supports more complex functions

**Advantages:**

+ Powerful computing capability
+ Supports advanced interfaces like USB
+ Larger memory capacity
+ Mature ecosystem

**Common Models:**

+ SAMD11: Small but comprehensive
+ SAMD21: More powerful, suitable for complex applications
+ SAM51: High-end model, supports high-speed computing

### 3. RP2040 (Raspberry Pi) - Innovative Choice
This is a distinctive processor offering unique features:

**Core Features:**

+ Dual-core 32-bit processor
+ Base clock frequency 133MHz
+ Can be overclocked to 250MHz+
+ Innovative programmable IO system

**Advantages:**

+ Affordable (about $1)
+ Unique PIO functionality
+ Strong development ecosystem
+ Excellent price-performance ratio

### 4. ESP32/ESP8266 - Wireless Communication Expert
Suitable for projects requiring network connectivity:

**Core Features:**

+ Integrated WiFi and Bluetooth functionality
+ 32-bit processor
+ Rich peripheral interfaces
+ Multi-core design (ESP32)

**Advantages:**

+ Strong wireless communication capabilities
+ High integration
+ Excellent price-performance ratio
+ Rich development ecosystem

## Part Three: Development Environment and Tools
### Programming Language Selection
#### 1. C/C++
The most basic and common embedded development language:

**Advantages:**

+ High execution efficiency
+ Small memory footprint
+ Direct hardware control
+ Comprehensive compiler support

**Suitable Scenarios:**

+ High-performance projects
+ Resource-constrained devices
+ Scenarios requiring direct hardware control

#### 2. Python (MicroPython/CircuitPython)
Suitable for rapid development and learning:

**Advantages:**

+ Clean and readable code
+ Fast development speed
+ Easy debugging
+ Rich library support

**Considerations:**

+ Slower execution speed (about 1/100 of C)
+ Requires more system resources
+ Not suitable for real-time control

### Recommended Development Environments
#### 1. Arduino IDE
The most popular entry-level development environment:

**Features:**

+ Simple installation
+ Built-in common libraries
+ One-click compile and upload
+ Serial monitor

**Advantages:**

+ Gentle learning curve
+ Strong community support
+ Rich examples
+ Suitable for education

#### 2. VS Code + PlatformIO
Professional-level development environment combination:

**Features:**

+ Intelligent code completion
+ Syntax checking
+ Code refactoring
+ Version control

**Advantages:**

+ Comprehensive functionality
+ Rich extensions
+ Supports team collaboration
+ Cross-platform

## Part Four: Debugging Methods and Techniques
### Basic Debugging Methods Arduino (C/C++)
#### LED Status Indication
The most basic and intuitive debugging method:

```c
// Example code
void debugBlink(int times) {
    for(int i = 0; i < times; i++) {
        digitalWrite(LED_PIN, HIGH);
        delay(100);
        digitalWrite(LED_PIN, LOW);
        delay(100);
    }
}
```

#### Serial Print Debugging
Can output detailed debug information:

```c
void debugPrint(const char* msg, int value) {
    Serial.print("Debug: ");
    Serial.print(msg);
    Serial.print(" = ");
    Serial.println(value);
}
```

### Common Problems and Solutions
#### Program Upload Fails
Possible causes:

+ Wiring errors
+ Chip damage
+ Incorrect fuse bit settings
+ Power supply issues

Solution steps:

1. Check wiring
2. Verify power supply voltage
3. Try reducing programming speed
4. Check chip model

## Practical Assignments
### Group Assignment
1. Compare different embedded architectures
2. Test different processor development environments
3. Record and share comparison results

### Individual Assignment
1. Choose a microcontroller and read its datasheet
2. Write simple programs to implement:
    - LED control
    - Button input
    - Serial communication
3. Verify program functionality using simulator
4. Optional: Test program on actual hardware

## Learning Resources
### Official Documentation
+ Processor datasheets
+ Programming guides
+ Example code

### Online Resources
+ Course outline: [Embedded Programming](http://academy.cba.mit.edu/classes/embedded_programming/index.html)
+ [Arduino Official Documentation](https://www.arduino.cc/reference/en/)
+ [MicroPython Documentation](https://docs.micropython.org/)
+ [ESP32 Documentation](https://docs.espressif.com/)
+ [Raspberry Pi Pico Documentation](https://www.raspberrypi.com/documentation/microcontrollers/)

## Important Notes
### Safety Tips
1. Always match voltage and current correctly
2. LEDs must use current-limiting resistors
3. Avoid circuit shorts

### Development Tips
1. Start with simple examples
2. Gradually add functionality
3. Save and backup code regularly
4. Establish good debugging habits

### Documentation Organization
1. Record development process promptly
2. Save important code snippets
3. Document problems and solutions
