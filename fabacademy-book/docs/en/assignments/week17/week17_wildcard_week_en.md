---
layout: doc
title: "Week 17: Wildcard Week | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 17 Wildcard Week course, where students can choose their own digital fabrication techniques to explore, focusing on digital manufacturing technologies not covered in previous courses."
head:
  - - meta
    - name: keywords
      content: fab academy, wildcard week, digital fabrication, multi-axis machining, composites, individual assignment
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 16 Assignment: System Integration Design'
  link: '/en/assignments/week16/week16_system_integration_design_en'
next:
  text: 'Week 17 Assignment: Wildcard Week Project'
  link: '/en/assignments/week17/week17_wildcard_week_project_en'
lastUpdated: true
editLink: true
---

# Week 17: Wildcard Week
## Course Overview
Wildcard Week is a unique component of the Fab Academy curriculum that allows students to explore digital fabrication technologies not covered in previous courses. This week is characterized by students' freedom to choose which digital fabrication techniques they want to learn, departing from the pattern of previous weeks where all students learned the same skills. The chosen technique must meet the following conditions: it must involve a digital design and manufacturing process, be documented in detail for others to replicate, and must be a skill not covered in other assignments. The flexibility of this week enables students to utilize special equipment available in their lab or introduce new manufacturing capabilities.

The course is divided into two parts: the first half introduces various optional digital fabrication technologies, while the second half delves deeply into composite materials technology (which was previously a separate course week). When choosing projects, students need to balance the allocation of time and attention, as the final project deadline is approaching.

Check out the [Wildcard Week inventory](http://inventory.fabcloud.io/?purpose=Wildcard%20Week) for more information.

## Detailed Course Content Introduction
### Part One: Wildcard Week Optional Technologies Overview
#### 1. Multi-axis Machining
+ **Three-axis Machining**: Although previous courses introduced 2.5D machining (XY plane movement plus Z-axis stepping), full three-axis synchronous machining (simultaneous control of XYZ axes) is an optional new skill
+ **Four-axis/Five-axis Machining**: If conditions permit, more advanced multi-axis machining can be learned, where the workbench can rotate (four-axis), or a combination of workbench rotation and tool head pivoting (five-axis)
+ **Specific Example**: [5-axis Machining Center](https://cba.mit.edu/tools/#Hurco_VMX42SRTi_5_axis_machining_center)

#### 2. Special Cutting Technologies
+ **Electrical Discharge Machining (EDM)**: Uses plasma around electrodes for metal cutting - [Sodick SL-400G Wire EDM](http://cba.mit.edu/tools/index.html#Sodick_SL-400G_Wire_EDM)
+ **Waterjet Cutting**: Uses supersonic water jets mixed with abrasive garnet for cutting, can process almost any material including glass and stone - [Omax 5555 Waterjet Cutter](http://cba.mit.edu/tools/index.html#Omax_5555_waterjet)
+ **Plasma Cutting**: Uses plasma generated by a torch to cut metal, requires appropriate safety environment - [Forest Scientific Plasma Cutter](http://forestscientific.com/hs-cnc-plasma-cutter/)
+ **Fiber Laser Cutting**: 1000-watt level high-power laser capable of cutting metal - [Fablight 3000 Laser Metal Cutter](http://cba.mit.edu/tools/index.html#Fablight_3000_Laser_Cutter)
+ **Micromachining Laser**: Ultra-high precision laser for creating micron-level parts - [Oxford Alpha532 Micromachining Laser](http://cba.mit.edu/tools/index.html#Oxford_Alpha532_Laser)
+ **Large Format Flatbed Cutter**: Similar to vinyl cutters but with work areas up to 4×8 feet, suitable for large cutting projects - [Zund G3 Digital Cutting System](http://cba.mit.edu/tools/index.html#Zund_G3_L-2500_Digital_Cutter)

#### 3. Welding
+ Welding alone does not meet the requirements for digital manufacturing, but can be combined with other digital tools
+ Parts can be designed by computer, cut using CNC tools, and then connected using MIG, TIG, or spot welding techniques
+ **Technical Reference**: [Welding Technology Fundamentals](http://fab.cba.mit.edu/classes/863.03/topics/assembly/forming/welding.pdf)

#### 4. Special Molding and Casting Techniques
+ **Vacuum Forming**: Heats plastic sheets and uses vacuum to make them conform to mold shapes, commonly used in packaging manufacturing - [Brent's Vacuum Forming Project](http://fab.academany.org/2019/labs/lccc/students/brent-richardson/assignments/week18)
+ **Rotational Casting**: Forms hollow structures by rotating molds so that material evenly coats the interior surface - [Saverio's Rotational Casting Project](http://fabacademy.org/archives/2015/eu/students/silli.saverio/project07.html)

#### 5. Folding Techniques
+ **Curved Folding**: Using laser cutters or vinyl cutters to create foldable structures
+ **Mathematical Origami**: Designing curved crease structures based on mathematical algorithms by Eric Demaine and others - [Erik Demaine's Origami Mathematics Research](http://erikdemaine.org)
+ **3D to 2D Conversion**: Designing 2D cutting patterns to fill 3D shapes - [Calisch's Research on Shape and Function](http://cba.mit.edu/docs/theses/19.09.calisch.pdf)
+ **Kirigami**: An origami variant combining cut lines and crease lines, applicable to materials like metal - [MIT Kirigami Research](https://news.mit.edu/2023/using-kirigami-ultrastrong-lightweight-structures-0822)

#### 6. Robotics Technology
+ **Universal Robot Arms**: Such as Elephant Robotics' low-cost robot arms, programmable for various tasks - [UR10 Robot Arm](http://cba.mit.edu/tools/index.html#Universal_Robotics_UR10_Robot_Arm)
+ **Architectural Robots**: Mobile robots that construct by placing components - [Gramazio Kohler Robotic Architecture](https://www.google.com/search?q=gramazio+kohler+robotic&tbm=isch)
+ **Soft Robotics**: Soft grippers and manipulators cast from molds and actuated by pneumatic pressure - [Adriana's Soft Robotics Project](https://adrianacabrera.github.io/SoftRobotics) and [Harvard Soft Actuator Research](https://gmwgroup.harvard.edu/soft-robotics)
+ **Pneumatic Systems**: Open-source portable pneumatic generators for soft robotics - [Soft Robotics Platform](https://www.softrobotics.io)
+ **Inflatable Structures**: Creating flexible motion structures through selective inflation - [Otherlab Inflatable Structures](https://www.google.com/search?q=otherlab+inflatable&tbm=vid)

#### 7. Electronic Manufacturing
+ **Automated Pick and Place Machines**: Using open-source pick and place machines to automatically position electronic components - [Mechatronika M10V Pick and Place](http://cba.mit.edu/tools/index.html#Mechatronika_M10V_Pick_and_Place)
+ **Programmable Logic**: Creating reconfigurable integrated circuits using FPGAs (Field Programmable Gate Arrays) - [Programmable Logic in Embedded Programming](http://academy.cba.mit.edu/classes/embedded_programming/index.html)

#### 8. Special Applications for Embedded Programming
+ **Embedded AI**: Implementing machine learning on microcontrollers using platforms like TensorFlow Lite, TinyML, ESP-DL, or Edge Impulse - [Embedded AI Resources](http://academy.cba.mit.edu/classes/embedded_programming/index.html#AI)
+ **Machine Vision**: Using OpenCV or AI cameras for facial recognition, tracking moving objects, etc. - [Image Processing in Input Devices](http://academy.cba.mit.edu/classes/input_devices/index.html#image)

#### 9. Food Manufacturing
+ **Food Printing**: Designing and manufacturing digital systems for food extrusion, molding, or processing - [Columbia Food Printing Research](https://magazine.columbia.edu/article/all-food-thats-fit-print)
+ **Digital Gastronomy**: Using digital technology to manipulate food - [Digital Gastronomy Platform](http://digitalgastronomy.co)

#### 10. Materials Science
+ **Material Synthesis**: Creating open-source material libraries using readily available materials like coffee grounds, eggshells, algae, etc. - [Materiom Open Source Materials Library](https://materiom.org)
+ **Material Testing**: Designing and building open-source material testing equipment - [Displacement Testing Exercise](https://gitlab.cba.mit.edu/jakeread/displacementexercise)
+ **Material Property Analysis**: Creating 3D printers that can measure material properties - [MIT Rheology 3D Printing Research](https://news.mit.edu/2024/3d-printer-can-print-with-unknown-material-0408)

#### 11. Biotechnology
+ **DIY Biology**: Combining Fab Lab and biology laboratory techniques - [DIYbio Organization](https://diybio.org)
+ **Synthetic Biology**: Referencing the iGEM competition's system programming approach - [iGEM International Genetically Engineered Machine Competition](https://igem.org)
+ **Biological Cultivation**: Concepts from the "How to Grow Almost Anything" course - [HTGAA Course](http://fab.cba.mit.edu/classes/S61.20/index.html) and [Bio Academy Platform](http://bio.academany.org)

#### 12. Textile Technologies
+ **Fiber Processing**: Different techniques including weaving, knitting, crocheting, felting, braiding, etc. - [Introduction to Fiber Processing Techniques](http://fab.cba.mit.edu/classes/865.18/fiber/index.html)
+ **Computerized Knitting Machines**: Learning to program knitting machines to create custom textiles - [Machine Knitting Guide](https://akaspar.pages.cba.mit.edu/machine-knitting/)
+ **Embroidery Machines**: Using programmable hybrid sewing/embroidery machines - [Embroidery Technology Research](http://cba.mit.edu/docs/theses/99.02.post.pdf)
+ **Open-source Embroidery Software**:
    - [PEmbroider](https://github.com/CreativeInquiry/PEmbroider)
    - [Ink/Stitch](https://inkstitch.org)
+ **Fabricademy**: A sister course focusing on textile technologies - [Textile Academy](http://textile-academy.org)

### Composite Materials Composition
1. **Fiber Material Selection**:
    - Chopped Fibers: Easy to use but lower performance, as they cannot effectively transfer forces
    - Filaments: Can be used for wrapping
    - Tape: Can be laid up
    - Fabrics: Most commonly used in Fab Labs, can be fiberglass, carbon fiber, or natural fibers (cotton, silk, bamboo, wood, flax, jute)
2. **Matrix Material Selection**:
    - Epoxy Resin: Commonly used but requires safety precautions
    - Phenolic Resin: Used for FR1 circuit boards, etc.
    - Cement: Used for construction
    - Natural Resins: More environmentally friendly plant-based resins
    - **Supplier Resources**: [Entropy Resins (Bio-based Epoxy)](https://entropyresins.com/), [McMaster-Carr](http://www.mcmaster.com/#garolite), [West System](http://www.westsystem.com/)
3. **Lamination Materials**:
    - Wood Lamination: For example, custom skateboards using multiple layers of wood pressed and bonded in molds
    - **Reference Example**: [Custom Skateboard Projects](https://www.google.com/search?q=fab+lab+skateboard&source=lnms&tbm=isch)
    - **Related Resources**: [Cardboard Composite Techniques](http://fabacademy.org/2018/labs/fablabbcn/students/santi-fuentemilla/weeks/week15.html)

### Composite Materials Design Principles
+ **Bending Stiffness Principle**: Outer layer fibers bear tensile forces, inner layers contribute less to the structure
+ **Core Material Usage**: Using honeycomb structures or cardboard as core material in the middle section to reduce weight
+ **Sandwich Structure**: Combination of bottom composite layer, core material, and top composite layer
+ **Digital Design Methods**: Using computers to design the shape and performance of composite structures - [Digital Composite Materials Research](http://cba.mit.edu/docs/papers/13.09.Science.pdf)

### Composite Materials Manufacturing Process
1. **Mold Making**:
    - Machined Molds - [Machined Mold Example Image](http://academy.cba.mit.edu/classes/composites/machine.jpg)
    - Folded Molds - [Folded Mold Example Image](http://academy.cba.mit.edu/classes/composites/fold.png)
    - Cardboard Frames as Internal Forms - [West Kust Kayak Skin Technique](http://westkustsurf.nl)
2. **Layup Process**:
    - Wet Layup: Layering fabrics and applying resin progressively - [Wet Layup Example](http://fab.cba.mit.edu/classes/863.12/people/calisch/10/week10.html)
    - Prepreg: Fabrics pre-impregnated with resin
    - RTM (Resin Transfer Molding): Injecting resin under pressure
    - Material Ratio Control: Testing fiber-to-resin ratios under different compression conditions - [Composite Coupon Testing](http://fab.cba.mit.edu/classes/863.16/doc/tutorials/composites/coupon-testing.html)
3. **Compression Methods**:
    - Vacuum Bag Compression: Using vacuum bags, breather layers, and release layers, compressed by atmospheric pressure - [Detailed Guide to Vacuum Bag Techniques](http://www.westsystem.com/ss/assets/HowTo-Publications/Vacuum-Bagging-Techniques.pdf)
    - Press Molding: Using mechanical clamping with two-sided molds to squeeze out excess resin
    - Water Bag Pressure: A simple method using garbage bags filled with water to provide weight - [Vacuum Storage Bag Example](https://www.amazon.com/Space-Saver-Vacuum-Storage-Jumbo/dp/B004X98B5A)
4. **Post-processing**:
    - Surface Treatment: Final composite materials often require varnish or other post-processing for a smooth, glossy surface

### Safety Considerations
+ **Fiber Hazards**: Carbon fiber and fiberglass produce short fibers when cut, drilled, or sanded, which can cause lung disease if inhaled
+ **Protective Measures**: Full protective equipment is needed when working with carbon fiber (respiratory, eye, and skin protection)
+ **Resin Safety**: Common epoxy resins are highly volatile, requiring good ventilation; low-volatility bio-based resins (like Entropy Resins) are recommended
+ **Thermal Reaction**: Large composite materials may generate significant heat during curing, requiring cooling measures

### Recommendations and Best Practices
+ **For Beginners**: Avoid fiberglass and carbon fiber; use natural fibers and low-volatility resins
+ **Material Ratio**: Beginners might use a 50:50 fiber-to-resin ratio, while advanced composites can reach 90% fiber and 10% resin
+ **Test First**: Make small samples ("coupons") to test the process before making large components - [Composite Coupons Collection](https://pub.pages.cba.mit.edu/compositecoupons)
+ **Combine with Other Skills**: Composite material techniques combined with "Making Large Items" and "Molding and Casting" week skills can greatly expand a lab's manufacturing capabilities

## Assignment Requirements
The assignment for this week requires students to define and complete their own project:

1. **Design and Manufacture**: Create an item using a digital manufacturing process (including computer-aided design and manufacturing) - [Reference Example Project](http://fab.cba.mit.edu/classes/863.12/people/calisch/10/skin.html)
2. **Uniqueness Requirement**: The chosen technology must not be covered in other assignments
3. **Documentation**:
    - Clearly state which requirements your assignment meets
    - Document all necessary steps in detail so others can replicate your project
    - Explain your chosen technology and process
4. **Digital Process**: The project must involve computer design and manufacturing, not just manual skills
5. **Time Management**: Balance this new project with work on the final project, allocating time wisely

### Official Fab Academy Resources
+ [Wildcard Week Inventory](http://inventory.fabcloud.io/?purpose=Wildcard%20Week)
+ [Composites Course](http://academy.cba.mit.edu/classes/composites/index.html)
+ [Previous Years' Wildcard Week Projects](http://fabacademy.org/archive/courses/index.html)
