---
layout: doc
title: "Week 16: System Integration | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 16 System Integration course, introducing how to combine various independent components (such as mechanical parts, electronic components, software, etc.) into a coordinated working system."
head:
  - - meta
    - name: keywords
      content: fab academy, system integration, project management, time planning, component integration, testing verification, personal assignment
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 15 Personal Assignment: Interface and Application Programming'
  link: '/en/assignments/week15/week15_interface_application_programming_personal_en'
next:
  text: 'Week 16 Personal Assignment: System Integration Mechanism Design'
  link: '/en/assignments/week16/week16_system_integration_mechanism_design_en'
lastUpdated: true
editLink: true
---

# Fab Academy 2025 - System Integration Course Notes
## Course Overview
This week's course will delve into the important concept of system integration, which is a key step in designing and manufacturing complete functional prototypes. System integration involves combining independent components (such as mechanical parts, electronic components, software, etc.) into a coordinated working system. At this stage, we will learn how to integrate the skills learned in previous weeks to ensure all components work together smoothly. This course is particularly important as it directly relates to the successful implementation of the final project and prepares for the final project presentation.

## Detailed Course Content Introduction
### 1. Course Progress and Important Date Reminders
#### Course Timeline Review
+ **Current Date**: May 7
+ **Remaining Four Weeks**: All weekly assignments must be completed and submitted within four weeks (June 2-6)
+ **Five Weeks Later**: Final project presentations (June 9-13)
+ **Seven Weeks Later**: Fab25 Conference (July 4-11, Czech Republic)

#### Important Notes
+ Registration for final project presentations will open on May 14
+ This week's system integration assignment is an important indicator of whether you are ready for the final project presentation
+ If your assignments are 75% complete and you have demonstrated your final project, but still have some work to complete, you may qualify for conditional graduation

### 2. Project Management and Time Planning
In the remaining four weeks of tight schedule, completing all assignments requires efficient time management:

+ **Supply and Demand Management**: Cannot rely solely on hope to complete the project, must schedule time scientifically
+ **Schedule Planning**: Establish a detailed schedule listing specific tasks to be completed each day
+ **Work According to Plan**: Strictly execute according to the established plan, one of the most important skills in Fab Academy
+ **Triage Rule**: Differentiate task priorities to ensure core functions can be completed

### 3. System Design and Integration Concepts
#### Design Methodology
+ Differences between **conceptual design, preliminary design, and detailed design**
+ **User Experience (UX) and Usability** considerations
+ **Design for Manufacturing (DFM)**: How to design products that can be practically manufactured

#### Component Selection and Optimization
+ Benefits of using standard components
+ Near-net-shape technology applications
+ Flexible structures vs fasteners vs adhesives choices
+ Design for self-alignment features
+ How to minimize the number of components

#### Documentation
+ Organization and management of design files
+ Creating Bill of Materials (BOM)
+ Production documentation and operating instructions
+ Packaging considerations

### 4. Key Aspects of System Integration
#### Mechanical Integration
+ PCB mounting methods
+ Cable routing techniques
+ Mechanism alignment methods
+ Surface treatment processes

#### Testing and Quality Control
+ Differences between Quality Assurance (QA) and Quality Control (QC)
+ Common testing methods: vibration testing, aging testing, cycle testing, environmental testing
+ Application of fuzzy testing in system stability verification

### 5. Common Failure Modes and Prevention
#### Mechanical Failures
+ Loads exceeding elastic limits
+ Cracking issues in stress concentration areas (using rounded transitions)
+ Fastener loosening, vibration, and thread-locking solutions
+ Thread stripping problems
+ Misalignment, jamming, and flexible structure design
+ Dynamic instability

#### Cable and Connection Failures
+ PCB trace damage
+ Connector detachment issues
+ Strain relief design
+ Application of polarized connectors
+ Insulation wear, dielectric breakdown, and short circuit prevention
+ Cable routing and harness management
+ Connector corrosion issues

#### Electronic Component Failures
+ MOSFET overvoltage, overcurrent, overtemperature protection
+ Inductive kickback and reverse protection diodes
+ Voltage regulator reverse polarity issues
+ Processor overvoltage and GPIO current source/sink issues
+ Current limiting design
+ Transient protection design
+ EMI shielding techniques

#### Power Supply Issues
+ Power budget and power supply matching
+ Battery life considerations
+ Power supply voltage matching issues (e.g., 48V supply to 24V input)
+ Transient noise and processor failures
+ Cable resistance and inductance
+ Application of bypass capacitors
+ Ground loop issues

#### Software Failures
+ Memory leaks
+ Buffer overflows
+ Race conditions
+ Variable scope issues
+ Object interface design
+ Dependency management
+ Over-complication issues

### 6. Scaling and Manufacturing Considerations
#### Scalability
+ Big O notation for complexity growth
+ Phase transition phenomena in complexity

#### Manufacturing-Related Issues
+ Supply chain management
+ Capacity planning

#### Collaborative Development
+ Data exchange standards
+ Fail fast strategies
+ Feed-forward vs feedback development models

### 7. Maintenance and Lifecycle
#### Design for Maintenance
+ Drop and vibration resistant design
+ Importance of modular design
+ Widlarize principle (simplifying design)

#### Lifecycle Considerations
+ Right to repair design
+ Design for disassembly, reuse, and recycling
+ End-of-life management

## Assignment Requirements
This week's assignment requires designing and documenting the system integration plan for your final project. Specifically including:

1. **System Design Documentation**: Create detailed system design diagrams that clearly show the connections and interactions between components
2. **Integration Plan**: Develop specific integration steps and testing plans
3. **Potential Failure Analysis**: Identify possible failure points and propose preventive measures
4. **Maintenance and Lifecycle Considerations**: Explain how to perform maintenance and final disposal

Special Note: Completing this week's assignment is a prerequisite for being scheduled for the final project presentation. The quality of the assignment will directly affect whether you can present your project during the final presentation week in June.

## Learning Resources
The following are recommended resources for in-depth learning about system integration:

### Online Resources
+ [Design for Manufacturing (DFM) Guide](https://www.disher.com/blog/design-for-manufacturing/)
+ [Self-Alignment Feature Design](https://mtm.cba.mit.edu/2011/2011_mtm-snap/mtm_snap-lock/index.html)
+ [Bill of Materials (BOM) Tools](https://github.com/openscopeproject/InteractiveHtmlBom)
+ [Production Automation Documentation](https://wiki.freecad.org/OSH_Automated_Documentation)
+ [Mechanism Alignment Guide](https://help.prusa3d.com/guide/4-z-axis-assembly_67704)

### Case Studies
+ [Tesla Gigacasting Technology](https://www.theverge.com/2023/9/14/23873345/tesla-gigapress-gigacasting-manufacturing-breakthrough) - Revolutionary case of component minimization
+ [NASA Mars Climate Orbiter Metric Unit Error](https://www.simscale.com/blog/nasa-mars-climate-orbiter-metric/) - Importance of data exchange standardization
+ [SpaceX Starship Development](https://www.space.com/every-spacex-starship-explosion-lessons-learned) - Successful case of feedback development model

### Recommended Books and Articles
+ "The Design of Everyday Things" by Don Norman - On user experience design
+ "Making Things Move" by Dustyn Roberts - Mechanical design and integration
+ [CBA Paper: Stress Concentration and Fillet Design](https://cba.mit.edu/docs/papers/24.01.MIPS.pdf)

### Reference Project Examples
+ [System Integration Excellence Case](https://archive.fabacademy.org/archives/2016/fablabtorino/students/440/project/P02/B2/electronics.html)