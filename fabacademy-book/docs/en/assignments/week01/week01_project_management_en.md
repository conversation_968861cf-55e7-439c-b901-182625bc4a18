---
layout: doc
title: "Week 1: Project Management | Lei <PERSON> Fab Academy 2025"
description: Documentation of Week 1 learning journey in Fab Academy 2025 - Project Management Fundamentals
head:
  - [meta, { name: keywords, content: "fab academy, project management, git, version control, documentation" }]
  - [meta, { name: author, content: "<PERSON><PERSON>" }]
aside: true
outline: true
prev:
  text: "About Me"
  link: "/en/about/about_en"
next:
  text: 'Week 1: Final Project Ideas'
  link: '/en/assignments/week01/week01_final_project_ideas_en'
lastUpdated: true
editLink:
  pattern: "https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path"
  text: "Edit this page on GitLab"
---
# Week 1: Overview

As the opening week of the Fab Academy 2025 course, Week 1 focuses on the fundamental yet crucial topic of project management. This week's content not only lays the groundwork for our 24-week learning journey but also helps us establish the core workflows necessary for developing digital fabrication projects.

At the beginning of the course, Professor <PERSON> emphasized the importance of version control systems in modern manufacturing. In the field of digital fabrication, project management is not just about time planning but also about effectively tracking and documenting the entire manufacturing process. By using a distributed version control system like [Git](https://git-scm.com/), we can precisely record every design change, facilitate team collaboration, and easily revert to any historical version when needed.

The course also provided a detailed introduction on how to set up a personal project documentation website. This website serves not only as a platform for submitting assignments but also as a digital archive showcasing our learning journey. Through learning the basics of [HTML](https://developer.mozilla.org/en-US/docs/Web/HTML) and [CSS](https://developer.mozilla.org/en-US/docs/Web/CSS), we will be able to build both aesthetically pleasing and functional project documentation, preparing us for the development records in the upcoming weeks.

Particularly noteworthy is the course's emphasis on the spirit of open source and knowledge sharing. Each student is required to fully document and publicly share their project processes. This not only facilitates peer learning but also contributes valuable experience to the global maker community. This open and sharing philosophy is a key driving force behind the continuous development of digital fabrication technologies.

For me, the biggest takeaway from this lesson is understanding the central role of project management in digital fabrication. It is not just a tool or methodology but a crucial guarantee for the orderly progression of projects and the realization of creative ideas. In the upcoming weeks, these foundational project management skills will be consistently applied, helping us better complete weekly assignments and ultimately achieve the goals of our final projects.

Link to the learning documentation: [http://academy.cba.mit.edu/classes/project_management/index.html](http://academy.cba.mit.edu/classes/project_management/index.html)