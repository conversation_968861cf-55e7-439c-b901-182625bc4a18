---
layout: doc
title: "Week 1: Final Project Ideas | Lei Feng Fab Academy 2025"
description: "Complete documentation of final project ideation and design process for Week 1 of Fab Academy 2025"
head:
  - [meta, { name: "keywords", content: "fab academy, final project, digital fabrication, revolving lantern, innovative design" }]
  - [meta, { name: "author", content: "<PERSON><PERSON>" }]
aside: true
outline: deep
prev:
  text: "Week 1: Project Management"
  link: "/en/assignments/week01/week01_project_management_en"
next:
  text: 'Week 1: Git and GitLab Environment Setup and Usage Guide'
  link: '/en/assignments/week01/week01_git_gitlab_setup_en'
lastUpdated: true
editLink:
  pattern: "https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path"
  text: "Edit this page on GitLab"
---
# Week 1:Final Project Ideas

Before introducing specific project ideas, I believe it's necessary to clarify the specific requirements for the final project in Fab Academy. These requirements are not only evaluation criteria but also an important reference framework guiding our project ideation.

## Project Requirements Analysis

Fab Academy expects each student to demonstrate comprehensive digital fabrication capabilities in their final project. Specifically, the project needs to integrate the following core skills:

First is design capability. We need to proficiently use 2D and 3D modeling tools, developing from conceptual sketches to precise engineering drawings. Second is manufacturing technology. The project must combine additive manufacturing (such as 3D printing) and subtractive manufacturing (such as laser cutting, CNC milling) processes. In electronics, we need to independently design and fabricate printed circuit boards with necessary input/output devices. Finally, in programming, we need to integrate these hardware components into an intelligent system through embedded programming.

Particularly noteworthy is the emphasis on the "self-made" concept. This means we should minimize the use of ready-made components and instead complete the design and fabrication of key components through digital fabrication technologies.

## Evolution of Project Ideas

Based on these requirements, I proposed four initial ideas, each with its unique challenges and opportunities:

+ The first idea is a kite with remotely controllable colored lights. This concept stems from my interest in aerial photography, attempting to combine traditional kites with modern lighting technology. However, after analysis, I found this project might not fully demonstrate the multiple skills required by the course.
+ The second concept is a smart desktop system. This is an ambitious project where I wanted to create an intelligent transformation of my home workspace, involving complex mechanical structures and intelligent control. But considering time constraints and personal capabilities, the scale of this project might be too large.
+ The third proposal is a group positioning and anti-lost device for teachers. This idea came from my experience in the education field, observing the chaotic situations during teacher-led field trips. However, further consideration revealed that the complexity of its electronic system might exceed my current capabilities.

## Final Choice: Smart Revolving Lantern

After careful consideration, I chose the Smart Revolving Lantern as my final project. This choice focuses on the perfect integration of traditional Chinese cultural art with modern digital fabrication technology, creating a work that both inherits tradition and embodies innovation.

## Fusion of Tradition and Innovation

Traditional revolving lanterns are exquisite folk art pieces, typically using the thermal airflow from candles to drive the rotation of the top blades, causing the hollowed-out lantern shade to rotate and project continuous animation-like light effects on surrounding walls. This ancient design embodies the wisdom of our ancestors but also faces limitations such as open flame safety and poor stability.

| ![](/images/week01/week01_final_project_ideas1.jpg) | ![](/images/week01/revolving_lantern.gif) |
| -------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------- |

Building on the elegant mechanism of traditional revolving lanterns, I plan to innovate them through digital fabrication technology:

First is the modernization of the power system. I will use a Seeed Studio ESP32C3 development board as the main controller, replacing the traditional thermal airflow driving principle, and integrate a motor control system as the main power source. Compared to the originally planned dual-drive mechanism, this design is more controllable and ensures system stability, while greatly reducing implementation complexity.

Second is the intelligent upgrade of the light source. Traditional candles will be replaced by programmable LED arrays, not only improving safety but also bringing rich visual possibilities to the work. Through carefully designed control circuits, LEDs can achieve varied effects such as color changes and brightness adjustments.

The interaction method will also have breakthrough innovations. I plan to integrate an ultra-mini APDS-9960 gesture sensor to implement intuitive gesture control functions:

+ Left/right gestures: Control the revolving lantern's forward/reverse rotation
+ Up/down gestures: Adjust light brightness high/low

![](/images/week01/w01-fp-1.png)

> Left/right gestures: Control the revolving lantern's forward/reverse rotation, up/down gestures: Adjust light brightness high/low
>

This sensor chip features integrated IR LEDs and drivers, as well as four directional photodiodes that can sense the amount of IR light reflected by the LEDs, enabling high-precision measurement of the distance between objects and the front of the sensor. It connects to microcontrollers via I2C interface, making it convenient to use and reliably stable.

At the same time, I will utilize the ESP32C3's WiFi functionality to develop a simple mobile application, enabling remote control of the lantern's power, rotation direction, and brightness, adding modern technological convenience to traditional craftsmanship.

In terms of structural design, I will reinterpret traditional aesthetics using digital fabrication processes. The lantern shade will be created using laser cutting technology, making traditional patterns more delicate and refined. The base will be 3D printed, incorporating modern design language while ensuring structural stability.

## Technical Challenges and Innovation Points
Although moderate in scale, this project contains multiple challenging technical aspects:

1. **Mechanical Design and Manufacturing**: Need to design a precise motor drive mechanism to ensure smooth and reliable rotation of the lantern shade. The base requires a rational arrangement of component layout, balancing structural strength and heat dissipation requirements. This will be accomplished through a combination of laser cutting and 3D printing techniques.
2. **Electronic System**: Design control circuits around the ESP32C3 development board, integrating the APDS-9960 gesture sensor, LED drivers, and motor control modules. Need to design custom PCBs and solve technical issues of multiple modules working collaboratively.
3. **Interaction Design**: Implement intuitive gesture control experiences and develop a simple, user-friendly mobile control interface. Need to implement stable gesture recognition algorithms in the firmware, as well as smooth wireless communication mechanisms.
4. **Energy Management**: Consider using rechargeable lithium batteries to power the entire system, requiring efficient power management circuit design to balance performance and battery life.

Through this project, I hope to not only demonstrate the application capabilities of digital fabrication technology but also interpret the elegant fusion of traditional craftsmanship and modern technology. This is what I understand as the way of digital fabrication: mastering advanced technology while cherishing traditional wisdom, innovating through inheritance, and inheriting through innovation.