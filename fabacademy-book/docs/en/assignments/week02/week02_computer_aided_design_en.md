---
layout: doc
title: "Week 2: Computer-Aided Design | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 2 Learning Guide for Computer-Aided Design (CAD) Tools and Methodologies"
head:
  - - meta
    - name: keywords
      content: fab academy, CAD, FreeCAD, Blender, Inkscape, parametric design, 3D modeling
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 1: VitePress Website Development and Deployment Guide'
  link: '/en/assignments/week01/week01_web_en'
next:
  text: 'Week 2: Converting Raster to Vector Graphics'
  link: '/en/assignments/week02/week02-raster-vector-en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---
# Week 2: Computer-Aided Design

## Overview

As the second week of the Fab Academy 2025 course, the core focus is on Computer-Aided Design (CAD). <PERSON>'s lecture aims to help students master digital design tools and methods from basic to advanced levels, laying the technical foundation for subsequent physical manufacturing and project development. The course emphasizes the diversity of design tools, parametric thinking, and the importance of cross-platform collaboration, while upholding the core principles of open-source spirit and knowledge sharing.

---

### Course Highlights

1. 2D Design Tools and Workflows
   - Raster Image Processing: Learn to use tools like [GIMP](https://www.gimp.org/), [ImageMagick](https://imagemagick.org/index.php) for image editing, batch compression, and format conversion, understanding the basic principles of raster graphics.
   - Vector Graphics Design: Master Boolean operations, cloning, and constraints in [Inkscape](https://inkscape.org/), and explore tools like [Potrace](https://potrace.sourceforge.net/) for converting raster images to vector paths.
   - Introduction to Parametric Design: Practice constraint-driven sketch design in [FreeCAD](https://www.freecad.org/), ensuring designs can automatically adjust with parameters (such as material thickness).
2. 3D Modeling and Advanced Techniques
   - Modeling Paradigms: Study geometric modeling methods including Boundary Representation (BRep), Function Representation (FRep), Voxel Representation (VRep), understanding their applications and limitations.
   - Core Operations: Practice modeling techniques through FreeCAD, Fusion 360, including extrude, revolve, loft, sweep, and master Boolean operations (CSG), symmetry, and assembly design.
   - Parametric Design and Programming: Implement parametric design using spreadsheet variables and Python scripts, and explore code-based modeling tools like [OpenSCAD](https://openscad.org/).
3. Rendering, Animation, and Simulation
   - Visual Presentation: Use [Blender](https://www.blender.org/features/) for high-quality rendering and animation creation, combining real-time rendering engines (like Eevee) to generate dynamic demonstrations.
   - Physical Simulation: Simulate mechanical motion, fluid, and soft body behavior through Blender's physics engine to support design validation.
4. File Formats and Collaboration
   - Format Selection: Prioritize universal formats like STEP and SVG for cross-tool collaboration, avoiding formats like DXF and STL that easily lose design information.
   - Open-Source Resources: Utilize platforms like [McMaster-Carr](https://www.mcmaster.com/) and [FreeCAD Library](https://github.com/FreeCAD/FreeCAD-library) to obtain standardized component models, improving design efficiency.
5. AI and Future Tools
   - Initial exploration of AI-assisted design (such as text-to-CAD), understanding its potential and current limitations (such as insufficient physical feasibility).

---

### Core Tools

+ Open-Source Tools: FreeCAD (parametric modeling), Blender (rendering/animation), Inkscape (vector editing), GIMP (image processing).
+ Commercial Tools: Fusion 360 (full workflow integration), SolidWorks (industrial-grade design), Onshape (cloud collaboration).
+ Collaboration Platforms: Git (version control), GrabCAD (model sharing), OBS Studio (screen recording and streaming).

---

### Assignment Requirements

Complete the following tasks this week:

1. Multi-dimensional Modeling: Design a potential final project using at least 3 tools (such as raster, vector, 3D CAD), covering 2D sketches, 3D models, rendered images, and animation demonstrations.
2. File Optimization: Compress images and videos using tools like ImageMagick and ffmpeg, ensuring they meet web publishing standards (H.264 encoding recommended).
3. Documentation Submission: Publish design documentation on the course page, including original design files (such as .FCStd, .blend), embodying the open-source sharing spirit.

---

### Open Source and Collaboration

The course continues Fab Academy's open culture, encouraging students to use open-source tools like FreeCAD and Blender while openly sharing design files and processes. Through participation in the global maker community's collaboration, we promote the accumulation and innovation of technical experience.

---

### Learning Resources

+ Learning Documentation Index Link: [http://academy.cba.mit.edu/classes/computer_design/index.html](http://academy.cba.mit.edu/classes/computer_design/index.html)
