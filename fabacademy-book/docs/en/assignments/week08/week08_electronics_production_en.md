---
layout: doc
title: "Week 8: Electronics Production | Lei <PERSON> Fab Academy 2025"
description: "Fab Academy 2025 Week 8: Learning electronic product manufacturing techniques, including PCB milling, component soldering and testing, mastering the complete process from design to physical prototype"
head:
  - - meta
    - name: keywords
      content: fab academy, electronics production, PCB milling, SMT soldering, circuit boards, CNC, component soldering, testing debugging
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Week 7: Individual Project: Monitor Stand Design and Cutting'
  link: '/en/assignments/week07/week07_individual_assignment_monitor_stand_design_cutting'
next:
  text: 'Week 8: Group Assignment: PCB Design Rules Characterization and Manufacturing'
  link: '/en/assignments/week08/week08_group_assignment_pcb_rules'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Week 8: Electronics Production
> This document content was refined by AI from the [course outline](http://academy.cba.mit.edu/classes/electronics_production/index.html) and the subtitles of the video conference lecture that I provided to Claude 3.7 Sonnet.
>

## Course Overview
This week's course is about the practical aspects of electronics production. In previous courses, we have learned about programming and simulation, and have designed electronic products. Now, we will take the next step to learn how to actually produce circuit boards, which is the foundation for moving into the next stage of learning (sensors and output devices, etc.). This course will introduce different methods of circuit board production, with a focus on CNC milling technology, and will cover key skills such as component soldering, debugging, and testing.

## Detailed Course Content
### 1. Introduction to Circuit Board Production Methods
#### 1.1 Non-recommended Method: Dead Bug Circuit
"Dead Bug" is a simple but unstable circuit production method, which involves directly connecting wires between components. Although this method may occasionally be used as a temporary solution, it is not recommended in professional environments due to its unstable and unreliable structure.

#### 1.2 Etching Method
Etching is a method of creating circuits by using chemicals to corrode the copper layer:

+ **Advantages**: Can be batch processed, can achieve very fine resolution
+ **Disadvantages**: 
    - Requires making masks (can be achieved through photolithography, toner transfer, or printing)
    - Uses hazardous chemicals (such as ferric chloride, copper chloride, ammonium/sodium persulfate, etc.)
    - Produces a large amount of chemical waste, which is not environmentally friendly
    - High water consumption (1 square meter of PCB can consume 1 cubic meter of water)

Due to these environmental issues, Fab Lab generally does not recommend the etching process, unless extremely high precision or mass production is required.

#### 1.3 CNC Milling Method - Recommended Method
CNC milling is the main production method recommended by Fab Academy:

+ **Advantages**:
    - No need to set up complex processes, designs can go directly into the machine
    - Almost no waste (only a small amount of dust that can be easily handled)
    - Milling can be completed within 10-15 minutes after the design is finished
    - Results are highly consistent and predictable
+ **Tools**:
    - Uses small, high-precision milling machines
    - Commonly used tools: 
        * 0.010 inch (0.25 mm) end mill: for extremely small features, but very fragile
        * 1/64 inch (about 0.4 mm) end mill: commonly used for cutting traces
        * 1/32 inch (about 0.8 mm) end mill: used for cutting boards and drilling holes
        * V-shaped bit: lower cost, suitable for trace isolation, but not suitable for removing large areas of copper
        * Tapered bit: combines the advantages of V-shaped bits and regular end mills
+ **Fixation and Support**:
    - The board material must be flat and fixed, any bending can cause the tool to break
    - Use tape or special fixtures for fixation
    - Need underlay material as support, which gradually wears out with use and needs to be replaced regularly
+ **Zeroing**:
    - Critical step, the tool must be precisely positioned on the copper layer surface
    - Common methods: 
        * Manual lowering and fixing (note that tightening may cause the tool to move up slightly)
        * Paper method: place paper and gradually lower the tool, when the paper cannot be moved, lower the tool by the thickness of the paper
        * Probe method: use electrical conductivity to automatically measure the zero position
+ **Tool Lifespan**:
    - New tools are very sharp, with fine edge features
    - Slightly dulled tools actually work better
    - With use, tools gradually become dull and need to be replaced promptly
+ **Post-processing**:
    - Deburring: use sandpaper, files, or straight-edge tools to remove small burrs
    - Cleaning: clean the circuit board after processing to avoid fingerprint oil causing copper layer oxidation

#### 1.4 Other Production Methods
##### 1.4.1 Vinyl Cutting Method
+ Faster than milling because it only needs to move the tool path rather than the entire area
+ Requires a "weeding" process, which requires some skill
+ Traces can be placed on any surface, high flexibility
+ Steps: 
    - Use underlay support
    - Transfer copper to epoxy film or polycarbonate film
    - Cut (ensure appropriate depth)
    - Remove unwanted parts
    - Recommend final encapsulation to enhance adhesion, especially for connector parts

##### 1.4.2 Laser Cutting Method
+ Ordinary CO2 laser cutters cannot be used for PCB production
+ Fiber lasers (approximately 1 micron wavelength) can effectively remove copper layers
+ New equipment such as XTool F1 Ultra (approximately $4000) combines diode laser and fiber laser
+ Features: 
    - Not limited by tool diameter, only limited by beam diameter
    - Very sensitive to laser focus
    - May require multiple passes (about 10) to remove copper layer
    - Can handle very small features
    - Can process large areas and small features simultaneously

##### 1.4.3 Other Methods
+ Laser-induced graphene (LIG): Using lasers to directly write conductive graphene material on Kapton
+ Conductive ink printing: Creating circuits by printing conductive ink
+ Electroplating technology: Used for commercial low-cost RFID tags, etc.
+ Conductive thread sewing: Using conductive thread to create low-resolution circuits on fabric

### 2. PCB Materials
#### 2.1 Rigid Circuit Board Materials
+ **FR4** (epoxy glass fiber):
    - Most commonly used commercially
    - Not recommended for milling in Fab Lab because: 
        * Glass fiber will wear down tools
        * Glass particles are hazardous waste, not suitable for inhalation
+ **FR1** (phenolic paper-based board):
    - Recommended by Fab Academy
    - Easy to mill, no harmful dust
    - Slightly lower temperature resistance than FR4, but sufficient for hand soldering
+ **Garolite**: Similar to FR1 but without copper layer

#### 2.2 Flexible Circuit Board Materials
+ **Kapton/Pyralux**: Common material for commercial flexible circuits
+ **Epoxy film and copper tape**: Can be used for DIY flexible circuits

#### 2.3 Copper Layer Thickness
+ 0.5 oz: 17.5 microns
+ 1.0 oz: 35 microns (standard)
+ 2.0 oz: 70 microns

### 3. Commercial PCB Manufacturing
#### 3.1 Common PCB Manufacturers
+ JLCPCB, PCBWay, OSH Park, Aisler, etc.
+ Features: Low cost (multiple boards for a few dollars)
+ Shipping is usually the main cost

#### 3.2 Design Rules
+ Trace width/spacing: Commercial typically 5 mil (0.127 mm)
+ Homemade PCBs usually require wider traces and spacing (about 15 mil/0.38 mm)

#### 3.3 Number of Layers
+ Single-sided board: All traces on one side
+ 1.5-sided board: Using zero-ohm resistors as cross-connections
+ Double-sided board: Traces on both sides, requires flip alignment
+ Multi-layer board: Commercially common 4 layers (top layer, bottom layer, power layer, ground layer)

#### 3.4 Vias
+ Through-hole connection methods: 
    - Wire: Suitable for a small number of connections
    - Rivets: Good choice for handmade multi-layer boards
    - Electroplating: Standard method for commercial PCBs
+ Special via types (commercial production only): 
    - Blind vias: Connect from the surface to an internal layer
    - Buried vias: Connect only between internal layers

### 4. Electronic Components
#### 4.1 Component Types
+ Through-hole components: Less used, mainly for special parts that need reinforcement
+ Surface-mount components: Recommended
+ Chip-scale packages: Extremely small components, require special soldering techniques

#### 4.2 Why Not Use Breadboards
+ Issues: 
    - Requires components with leads (incompatible with PCB design)
    - No documentation, difficult to transfer to PCB design
    - Poor electrical performance (noise, frequency characteristics)
    - Poor mechanical performance (connections may loosen when moved)
+ Recommendation: Learn to make circuit boards directly, which are more reliable, documented, and can transition directly from prototype to production

### 5. Soldering Techniques
#### 5.1 Soldering Equipment
+ Soldering iron/station: Control temperature
+ Fume extractor: Provide good ventilation
+ Safety precautions: Avoid burns

#### 5.2 Solder Types
+ Lead-free solder (recommended): Environmentally friendly, complies with ROHS standards, requires slightly higher temperature
+ Leaded solder: Lower soldering temperature, but not environmentally friendly
+ Low-temperature solder: For special applications

#### 5.3 Forms:
+ Solder wire: Commonly used for hand soldering
+ Solder paste: Used for reflow soldering

#### 5.4 Hand Soldering Techniques
1. Keep the soldering iron tip clean and shiny
2. Apply a thin layer of solder to the iron tip to aid heat transfer
3. Heat both the component lead and PCB pad simultaneously (not just one)
4. First add a small amount of solder to help heat transfer
5. Patiently wait for the solder to flow naturally (about 10-15 seconds)
6. Continue heating for a while after the solder flows
7. Remove the soldering iron

Good solder joint: Smooth and shiny; Bad solder joint: Grainy, dull

#### 5.5 Component Fixing Techniques
1. First add a small amount of solder to one pad
2. Place the component, temporarily fix it with that pad
3. Solder the other leads
4. Finally, return to the first joint, add an appropriate amount of solder to complete a good connection

#### 5.6 Reflow Soldering
+ Suitable for extremely small components or mass production
+ Steps: 
    1. Make a template (can be milled from polycarbonate material)
    2. Apply solder paste through the template
    3. Place components
    4. Heat to melt the solder paste (can use a heat gun, hot plate, or reflow oven)
+ Tip: Don't approach too quickly with the heat gun to avoid blowing away components

#### 5.7 Desoldering Techniques
+ Solder wick: First add a small ball of solder to "prime" it, then the wick will absorb the solder
+ Hot air gravity method: Heat all leads with a heat gun, use gravity to make the component detach

#### 5.8 Repair Techniques
+ Trace cutting and jumper wires: When there are design errors, can cut traces and add jumper wires
+ Component repositioning: Use desoldering techniques to remove incorrectly placed components

### 6. CAM and File Formats
#### 6.1 Common Formats
+ Gerber/RS-274X: Industry standard, used for commercial production
+ PNG: Easy to view and edit, suitable for homemade PCBs (no compression artifacts)

#### 6.2 CAM Software
+ FlatCAM, pcb2gcode: Convert PCB files to milling G-code
+ gerber2img, gerber2png: Convert Gerber files to images
+ MODS: Workflow automation tool

#### 6.3 Design Considerations
+ Choose trace width according to production tools
+ Pad design considerations: May need to adjust standard pads to accommodate milling tools
+ Standard pad spacing: 
    - 50 mil: Easy to achieve
    - 0.65 mm: Relatively easy
    - 0.5 mm or smaller: Very challenging for homemade PCBs

### 7. Debugging Techniques
When the circuit board doesn't work, debug systematically following these steps:

1. Check solder joints: Ensure they are smooth and shiny, with no solder bridges
2. Check component orientation and values: Confirm correct component orientation and values
3. Review datasheets: Confirm pin definitions are correct
4. Confirm connector orientation
5. Measure supply voltage: Ensure correct voltage at various points
6. Probe I/O signals: Use tools like oscilloscopes

## Assignment Requirements
### Group Assignment:
1. Characterize your lab's in-house PCB production process design rules 
    - Run test patterns to determine the minimum achievable trace width and spacing
    - Ensure the workflow runs smoothly, results should be highly consistent
2. Submit PCB designs to a PCB manufacturer 
    - Complete the design upload and price estimation process
    - Actual ordering is optional

### Individual Assignment:
1. Make and test your designed microcontroller development board 
    - Mill the circuit board
    - Solder components
    - Run your written program
    - Verify that the functionality is normal
2. Extra credit: Use another production technique 
    - Start with milling (simplest and most predictable)
    - Try other methods such as vinyl cutting or laser milling

## Time Management Suggestions
+ Update designs to comply with design rules: 1 day
+ Learn to make circuit boards: 1 day
+ Make circuit boards: 1 day
+ Soldering: 1 day
+ Load code: 1 day
+ Allow ample time for debugging (at least 2 days)

## Learning Resources
### Circuit Board Production Methods
+ [Dead Bug Circuit Examples](https://spectrum.ieee.org/with-the-dead-bug-method-hobbyists-can-break-through-the-highfrequency-barrier)
+ [PCB Etching Guide](http://pub.fabcloud.io/helloworld/uncharted/acid_etch.html)
+ [PCB Milling Video Tutorial](http://www.youtube.com/watch?v=XdamEhs2RIk&list=PL-xEsC0ZUCUM42QNHaOOdoOwYg0j251dU&index=1)

### Soldering Resources
+ [ROHS Standard Information](http://ec.europa.eu/environment/waste/rohs_eee/index_en.htm)
+ [Cold Solder Joint Identification Guide](https://www.google.com/search?q=cold+solder+joint&source=lnms&tbm=isch)

### CAM Tools
+ [FlatCAM](http://flatcam.org/)
+ [pcb2gcode](https://github.com/pcb2gcode/pcb2gcode)
+ [MODS](https://modsproject.org/?program=programs/machines/G-code/mill%202D%20PCB)

### PCB Manufacturers
+ [JLCPCB](https://jlcpcb.com/)
+ [PCBWay](https://www.pcbway.com/)
+ [OSH Park](https://oshpark.com/)
+ [Aisler](https://aisler.net/)

### Debugging Guides
+ [PCB Debugging Video](https://vimeo.com/518231668)

### Excellent Student Assignment Examples
+ [Quentin's PCB Production Example](https://fabacademy.org/2020/labs/ulb/students/quentin-bolsee/assignments/week05/)
+ [Isaak Freeman's Week 6 Assignment](https://fab.cba.mit.edu/classes/863.24/people/IsaakFreeman/week6/week6.html)

## Task Schedule
- [ ]  Friday, March 14: Determine the assignment circuit board and prepare components (may need to modify the circuit board based on the components found).
- [ ]  Saturday, March 15: Learn to make circuit boards.
- [ ]  Sunday, March 16: Cut circuit boards and test soldering.
- [ ]  Monday, March 17: Code and documentation.
- [ ]  Tuesday, March 18: Website upload and assignment completion.
