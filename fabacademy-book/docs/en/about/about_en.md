---
layout: doc
title: About Me | <PERSON><PERSON> Fab Academy 2025
description: Introduction and background of <PERSON><PERSON>'s journey in Fab Academy 2025
head:
  - - meta
    - name: keywords
      content: fab academy, digital fabrication, maker culture, seeed studio, chaihuo makerspace
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
prev:
  text: 'Home'
  link: '/en/'
next:
  text: 'Week 1: Project Management'
  link: '/en/assignments/week01/week01_project_management_en'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# About Me

Hi, I'm <PERSON><PERSON>. In this age dominated by code and circuits, I have worked at Seeed Studio in Shenzhen for six years, where I lead the user experience team. User experience, in essence, is about finding the most natural pathways through our digital world.

## The Journey to Fab Academy

[Fab Academy](http://fabacademy.org/) holds a prestigious position at Seeed Studio. As a manufacturer of open-source hardware, the open-source spirit is deeply embedded in the company's foundation. Its subsidiary, [Chaihuo Makerspace](https://chaihuo.org/) - my chosen Fab Lab node for this course - has been instrumental in spreading maker culture across China.

In 2021, I worked with Chaihuo Makerspace to create "A Guide to Makerspace Setup and Operation" ("创客空间搭建及运营指南"). This open-source guide emerged from our desire to help Chinese schools and organizations understand maker culture's origins and establish their own creative spaces. During the research process, I delved deep into the history, tracing back to [Dale Dougherty's](https://en.wikipedia.org/wiki/Dale_Dougherty) first issue of [Make:](https://makezine.com/) magazine in 2005. Interestingly, just a few pages in, I discovered "Welcome to the Fab Lab >>", featuring Neil's passionate face filling an entire page. This was my introduction to Fab Lab and Neil, and my initial curiosity gradually grew into deep respect.

![](/images/about/makerspace-setup-operation-guide-cover.png)

> A Guide to Makerspace Setup and Operation, organized by Chaihuo Makerspace

![](/images/about/make-volume-01.jpg)

> The inaugural cover of Make: Magazine
> Source: [https://www.makershed.com/products/make-volume-01-pdf](https://www.makershed.com/products/make-volume-01-pdf)

![](/images/about/welcome-to-the-fab-lab.jpg)

> The opening page of "Welcome to the Fab Lab >>" in Make: Magazine's first issue

Several colleagues at Seeed Studio have graduated from [Fab Academy](http://fabacademy.org/). Their stories always combine admiration with memories of late-night work sessions. By the end of 2024, these stories finally gave me the courage to enroll and embrace this learning challenge. Through this course, I hope to explore new possibilities in maker education.

My first impression of the course was moving - though Neil's hair has turned white and we can only watch him through video, his expression remains as sharp and determined as it appeared in Make: magazine two decades ago...

## Professional Background

My career path has been quite diverse: starting as a website and magazine editor, then moving through roles as an interaction designer, internet product manager, and game planner, before joining Seeed Studio in 2018 to design courses for Chaihuo Maker Education. This position introduced me to technical writing, where I discovered unexpected enjoyment. In February 2021, my first book "Learning Game Development with Microsoft MakeCode Arcade" ("做游戏 玩编程——零基础开发微软 Arcade 掌机游戏") was published by Tsinghua University Press. Since then, I've maintained a steady rhythm of publishing one book per year, like a farmer's annual harvest.

Each book represents a journey of learning. When facing new technology, I first work to understand it thoroughly, then try to explain it in ways that others can easily grasp. My background in web design and game development has given me familiarity with different tools for expressing knowledge. To create better-looking books, I even taught myself Adobe InDesign for layout design. Here are my published works:

| ![](/images/about/book-arcade.webp)  | "Learning Game Development with Microsoft MakeCode Arcade" (《做游戏 玩编程——零基础开发微软 Arcade 掌机游戏》), published by Tsinghua University Press, 2021                                                                                                                                                                                       |
| ------------------------------------ | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| ![](/images/about/book-arduino.webp) | "Easy Learning Arduino Graphical Programming" (《Arduino 图形化编程轻松学》), co-authored with Dmitry Maslov, published by Tsinghua University Press, 2022                                                                                                                                                                                           |
| ![](/images/about/book-iot.webp)     | "IoT In-Depth: Complete Project-Based Practice" (《深入浅出 IoT：完整项目通关实战》), Chinese translation of Microsoft's open-source course [IoT for Beginners](https://github.com/microsoft/IoT-For-Beginners), responsible for translation and layout, published by Tsinghua University Press, 2023                                                    |
| ![](/images/about/book-xiao.webp)    | "Arduino Miniaturization and TinyML Applications - From Beginner to Master" (《Arduino 小型化与TinyML 应用——从入门到精通》), collaborated with Marcelo Rovai on the English version [XIAO: Big Power, Small Board](https://mjrovai.github.io/XIAO_Big_Power_Small_Board-ebook/) which is now open source, published by Tsinghua University Press, 2023 |

## Working with AI

In 2024, I began collaborating with AI tools like ChatGPT and Claude. To be honest, the experience has been remarkable - it's like suddenly having several tireless assistants at my disposal. The integration of AI gave me the confidence to venture into territories I previously wouldn't have dared to explore. During this year, I completed two challenging translation projects: The first was Microsoft's [Artificial Intelligence for Beginners - A Curriculum](https://github.com/microsoft/ai-for-beginners). Initially, this course seemed impenetrable to me, filled with complex formulas and programs. However, with AI assistance, I was able to understand and execute almost all the example programs (with only a few exceptions where support libraries had been discontinued).

The second project was [Industrial Automation from Scratch](https://www.amazon.com/Industrial-Automation-Scratch-hands-industrial/dp/1800569386), published by [Packt](https://www.packtpub.com/en-us/product/industrial-automation-from-scratch-9781800569386?type=print). While this represented entirely new territory for me, the translation process progressed surprisingly smoothly with AI support. To properly understand the PLC programming content, I even acquired a second-hand Siemens PLC and HMI system, allowing me to complete hands-on programming exercises for the key chapters. Both books are scheduled for publication in Chinese by Tsinghua University Press.

![](/images/about/Industrial-Automation-from-Scratch.webp)

> [Industrial Automation from Scratch](https://www.amazon.com/Industrial-Automation-Scratch-hands-industrial/dp/1800569386) (Industrial Automation from Scratch: Hands-on Programming Exercises for PLCs and HMI Systems)

The rapid development of AI and my hands-on experience with it have completely changed how I think about learning and creation. While researching the [Fab Academy](http://fabacademy.org/) program, I came across the 2024 report [How to Make (Almost) Anything (AImost) without Making Anything](https://vimeo.com/912688847). This inspired me to include AI as a partner in this adventure and document our journey together.

## Personal Interests

Outside of work (usually on weekends or holidays), I love exploring with my wife Zhou Huimei in our BYD electric car. Sometimes when we only have a two-day weekend, we'll leave on Friday evening and drive through the night just to catch the sunrise from a special location. Covering nearly a thousand kilometers in a weekend is normal for us. The electric car is incredibly economical (using only $5-10 worth of electricity for a full day's driving), though range remains a challenge for long-distance travelers like us, requiring a charging stop every 200 kilometers. Fortunately, Chinese counties are typically less than 100 kilometers apart, and charging infrastructure is growing rapidly.

![](/images/about/about-wanfenglin.jpg)

> Photographed at sunrise in October 2023 at Wanfeng Lake Town, Anlong County, Qianxinan Buyi and Miao Autonomous Prefecture, Guizhou Province, China
[https://maps.app.goo.gl/NdsY4MyLenk7WuyAA](https://maps.app.goo.gl/NdsY4MyLenk7WuyAA)

![](/images/about/about-baidumap.jpg)

> This is the navigation record generated by the mobile map. We are like slime molds, constantly expanding our footprint outward with Shenzhen as the center.

![](/images/about/about-yuedan.jpg)

> Taken in November 2023 on Yuandan Road in Shaoguan, Guangdong. That small white dot on the mountain road is our car - it's incredibly enjoyable to drive on these remote mountain roads.

Each trip adds 30-100GB to our hard drives. Besides drone videos, most of this is DNG photos taken with bracketed exposure (when photographing the sky, you need a wide dynamic range, so multiple exposures are needed to capture both bright and dark details, which are then merged into HDR images. DJI drones make this convenient - one press of the shutter automatically captures 5 photos at different exposures). I use Adobe Lightroom Classic to merge these DNGs into HDR, adjust them to match what I remember seeing, then export both SDR and HDR versions.

| ![](/images/about/abou-luokeng-sdr.jpg) | ![](/images/about/abou-luokeng-hdr.avif) |
| --------------------------------------- | --------------------------------------- |

> Taken in the early morning of Luokeng Grassland, Guangdong in July 2024, in SDR and HDR versions (requires a monitor that supports HDR to see the effect)

Over the years, I've collected nearly ten thousand carefully edited landscape photos. They randomly appear on my computer desktop, reminding me of the world's natural beauty while I work in Shenzhen's concrete jungle.

Speaking of the [Fab Academy](http://fabacademy.org/) course, time management is my biggest concern. Looking at the schedule, I expect I won't be able to travel for about half a year! But this learning journey is similar to our travels - both are explorations of unknown territory.