---
layout: doc
title: "Final Project | Lei Feng Fab Academy 2025"
description: Fab Academy 2025 Final Project Documentation
head:
  - - meta
    - name: keywords
      content: fab academy, final project, magical lantern
  - - meta
    - name: author
      content: <PERSON><PERSON>
aside: true
outline: deep
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: 'Edit this page on GitLab'
---

# Magical Revolving Lantern

My Fab Academy 2025 final project involves developing a Magical Lantern that combines traditional Chinese lantern aesthetics with modern digital manufacturing technology. You can learn about the initial concept and development process through the following links:

![](/images/final_project/w01-fn.jpg)

**Core Functions**:

+ Using a motor to drive the lampshade rotation, replacing traditional hot air flow drive
+ LED array replacing traditional candles, providing programmable lighting effects
+ Intuitive human-machine interaction through gesture sensors
+ Remote control functionality via WiFi connection

## Project Progress
| Week | Date | Planned Content | Status | Documentation Link |
| --- | --- | --- | --- | --- |
| Week 1 | Jan 22 | Final Project Conceptualization and Requirements Analysis | ✅ Completed | [Project Concept Details](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week01/week01_final_project_ideas_cn) |
| Week 2 | Jan 29 | Lantern Shell 3D Modeling and Design | ✅ Completed | [3D Modeling Process](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week02/week02_3d_modeling_cn) |
| Week 3 | Feb 5 | Lantern Shell Laser Cutting and Assembly | ✅ Completed | [Laser Cutting Implementation](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week03/week03_laser_cutting_lantern_shell_cn) |
| Week 6 | Feb 26 | Control Circuit PCB Design | ✅ Completed | [Circuit Design Process](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week06/week06_individual_assignment_pcb_design_cn) |
| Week 8 | Mar 12 | PCB Circuit Board Production | ✅ Completed | [Circuit Board Production](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week08/week08_individual_assignment_pcb_production_cn) |
| Week 9 | Mar 19 | Gesture Sensor Integration | ✅ Completed | [Gesture Control Implementation](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week09/week09_individual_gesture_sensor_cn) |
| Week 10 | Mar 26 | Output Device Control (Fan Motor and LED) | ✅ Completed | [Output Control System](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week10/week10_individual_gesture_fan_led_cn) |
| Week 11 | Apr 2 | Dual-Node Gesture Control | ✅ Completed | [Dual-Node Gesture Control](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week11/week11_individual_gesture_network_system_cn) |
| Week 15 | May 6 | Web Control Interface Design and Implementation | ✅ Completed | [Web Control Interface Design and Implementation](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week15/week15_interface_application_programming_personal_cn) |
| Weeks 13-15 | May 10 | Rotating Structure Mechanical Design | ✅ Completed | [Motion Mechanism Design](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week16/week16_system_integration_mechanism_design_cn) |
| Week 16 | May 7-14 | System Integration | ✅ Completed | [System Integration Design](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week16/week16_system_integration_design_cn) |
| Week 17 | May 15-21 | Final Solution PCB Design and Program Debugging | 📅 Planned | - |
| Week 18 | May 22-27 | Final Solution Structure Refinement and Testing | 📅 Planned |  |
| Week 19 | May 28-Jun 4 | Documentation Organization and Presentation Preparation | 📅 Planned | - |
| Jun 9-13 | - | Final Project Presentation | 📅 Planned | - |


---

[Week 1: Final Project Concept](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week01/week01_final_project_ideas_cn)

![](/images/final_project/w01-2-fn.jpg)

Completed the preliminary concept of the Magical Lantern project, determining the core functions and technical approach.

---

[Week 2: 3D Modeling of the Magical Lantern](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week02/week02_3d_modeling_cn)

![](/images/final_project/w02-fn.jpg)

Used Fusion 360 to complete the 3D modeling design of the lantern shell, preparing for subsequent laser cutting.

---

[Week 3: Laser Cutting the Lantern Shell](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week03/week03_laser_cutting_lantern_shell_cn)

![](/images/final_project/w03-fn.jpg)

Successfully laser cut and assembled the hexagonal wooden lantern shell, verifying the feasibility of the design.

---

[Week 6: Control Circuit PCB Design](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week06/week06_individual_assignment_pcb_design_cn)

![](/images/final_project/w06-fn.jpg)

Designed an ESP32C3-based control circuit PCB, integrating gesture sensors and output control interfaces.

---

[Week 8: Circuit Board Production](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week08/week08_individual_assignment_pcb_production_cn)

![](/images/final_project/w08-fn.jpg)

Successfully produced the control circuit PCB using a CNC mill, and completed component soldering and basic testing.

---

[Week 9: Gesture Control Implementation](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week09/week09_individual_gesture_sensor_cn)

![](/images/final_project/w09-fn.jpg)

Implemented the integration of the APDS-9960 gesture sensor, successfully recognizing gestures in four directions: up, down, left, and right.

---

[Week 10: Output Control System](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week10/week10_individual_gesture_fan_led_cn)

![](/images/final_project/w10-fn.jpg)

Completed the drive control of the motor and LED light strips, achieving interactive effects between gestures and output devices.

---

[Week 11: Dual-Node Gesture Control](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week11/week11_individual_gesture_network_system_cn)

![](/images/final_project/w11-fn.jpg)

Developed a dual-node communication system based on the ESP-NOW protocol, enabling gesture information transmission and synchronized control between multiple devices.

---

[Week 15: Web Control Interface Design and Implementation](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week15/week15_interface_application_programming_personal_cn)

![](/images/final_project/w15-fn.jpg)

Designed and implemented a responsive web control interface, supporting remote adjustment of lighting effects and motor switches.

---

[Week 15: Motion Mechanism Design](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week16/week16_system_integration_mechanism_design_cn)

![](/images/final_project/w16-1-fn.jpg)

Designed and 3D printed the rotating cage mechanism and gear system, solving transmission and stability issues.

---

[Week 16: System Integration Design](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week16/week16_system_integration_design_cn)

![](/images/final_project/w16-2-fn.jpg)

I'll translate that short text for you.

---

[Week 17: Magical Lantern Carousel Ring PCB Design and Manufacturing](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/en/assignments/week17/week17_wildcard_week_project)

![](/images/final_project/w17-fn.jpg)

Redesigned the system architecture, integrating mechanical, electronic, and software subsystems, laying the foundation for the final product.


## Solved and Unsolved Problems
### Solved Problems
+ **Lantern Shell Design and Fabrication**: Successfully designed and laser-cut the hexagonal wooden shell in Week 3
+ **Rotating Cage Mechanism Design**: Designed and 3D printed a reliable rotating cage structure with proper tolerances
+ **Gear System Implementation**: Calculated appropriate gear module (1.11mm) and designed a functional gear system with steel shafts
+ **Motor Selection and Integration**: Selected appropriate N20 dual-shaft worm gear motor (130rpm) with sufficient torque for the rotating mechanism
+ **System Architecture Redesign**: Completely revised the system architecture to address limitations in the original design
+ **Component Selection and Sourcing**: Identified and acquired all necessary electronic components for the improved design

### Unsolved Problems
+ **Circular PCB Design and Fabrication**: Need to design and produce the new circular PCB that supports all required interfaces
+ **Multi-directional Gesture Recognition**: Implementation of the three parallel APDS-9960 sensors to achieve omnidirectional gesture control
+ **RGB Light Strip Integration**: Installation and programming of the dual RGB light strips for 360° visual effects
+ **Motor Control System**: Integration of the Grove Mini Fan driver with the XIAO ESP32C3 for precise motor speed control
+ **WiFi and MQTT Communication**: Development of the wireless communication system for multi-lantern synchronization
+ **Final Assembly and Testing**: Integration of all subsystems into a cohesive product and conducting thorough testing
+ **Power Management System**: Implementation of the rechargeable battery system with efficient power management (optional, will attempt if time permits)

## Next Steps
1. **PCB Design and Manufacturing (5-7 days)**: 
    - Complete the circular PCB schematic design
    - Design PCB layout and routing
    - Manufacture the PCB and assemble components
    - Test all interfaces and connections
2. **Component Integration (2-3 days)**: 
    - Connect and test the gesture sensors
    - Install and program the RGB light strips
    - Integrate the motor system
    - Connect and test the battery system
3. **Software Development (2-3 days)**: 
    - Develop firmware for multi-sensor processing
    - Implement RGB and motor control logic
    - Set up WiFi and MQTT communication
    - Create gesture control and device synchronization features
4. **Mechanical Integration (5-7 days)**: 
    - Finalize the transmission mechanism
    - Adapt the lantern shell for PCB and battery housing
    - Complete final assembly and balance adjustments
    - Conduct comprehensive testing according to the test plan

## Project Documentation Plan
The final project documentation will include the following sections:

1. **Project Overview**
    - Project background and sources of inspiration
    - Function introduction and technical features
    - Application scenarios and value
2. **Design Process**
    - Mechanical structure design
    - Electronic system design
    - Software system design
    - Design iterations and improvements
3. **Production Process**
    - Materials and components list
    - Detailed production steps
    - Encountered problems and solutions
4. **Results Presentation**
    - Final product showcase
    - Function demonstration videos
    - User guide
5. **Reflection and Summary**
    - Project outcome evaluation
    - Learning gains and experiences
    - Future improvement directions
    - Related source files


## References and Resources
+ [Fab Academy Syllabus](http://academany.fabcloud.io/fabacademy/2025/schedule.html)
+ [Chinese Traditional Revolving Lantern Information](https://www.sohu.com/a/449472236_120214181)
+ [XIAO ESP32C3 Documentation](https://wiki.seeedstudio.com/XIAO_ESP32C3_Getting_Started/)
+ [ESP32C3 Development Resources](https://docs.espressif.com/projects/esp-idf/en/latest/esp32c3/index.html)
+ [APDS-9960 Gesture Sensor Documentation](https://www.broadcom.com/products/optical-sensors/integrated-ambient-light-and-proximity-sensors/apds-9960)