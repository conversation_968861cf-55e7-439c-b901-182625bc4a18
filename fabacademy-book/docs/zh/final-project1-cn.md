---
layout: doc
title: "最终项目 | 冯磊 Fab Academy 2025"
description: Fab Academy 2025 最终项目文档
head:
  - - meta
    - name: keywords
      content: fab academy, 最终项目, 智幻灯笼
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 智幻走马灯

我的 Fab Academy 2025 最终项目是开发一个结合中国传统灯笼美学与现代数字制造技术的智幻灯笼。您可以通过以下链接了解项目的初始构想和发展过程：

![](/images/final_project/w01-fn.jpg)

**核心功能**:

+ 使用电机驱动灯罩旋转，替代传统热气流驱动
+ LED阵列替代传统蜡烛，提供可编程的灯光效果
+ 通过手势传感器实现直观的人机交互
+ 通过WiFi连接实现远程控制功能

## 项目进度
| 周次 | 日期 | 计划内容 | 状态 | 文档链接 |
| --- | --- | --- | --- | --- |
| 第1周 | 1月22日 | 最终项目构思与需求分析 | ✅ 已完成 | [项目构思详情](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week01/week01_final_project_ideas_cn) |
| 第2周 | 1月29日 | 灯笼外壳3D建模与设计 | ✅ 已完成 | [3D建模过程](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week02/week02_3d_modeling_cn) |
| 第3周 | 2月5日 | 灯笼外壳激光切割与组装 | ✅ 已完成 | [激光切割实现](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week03/week03_laser_cutting_lantern_shell_cn) |
| 第6周 | 2月26日 | 控制电路PCB设计 | ✅ 已完成 | [电路设计过程](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week06/week06_individual_assignment_pcb_design_cn) |
| 第8周 | 3月12日 | PCB电路板制作 | ✅ 已完成 | [电路板制作](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week08/week08_individual_assignment_pcb_production_cn) |
| 第9周 | 3月19日 | 手势传感器集成 | ✅ 已完成 | [手势控制实现](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week09/week09_individual_gesture_sensor_cn) |
| 第10周 | 3月26日 | 输出设备控制（风扇电机与LED） | ✅ 已完成 | [输出控制系统](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week10/week10_individual_gesture_fan_led_cn) |
| 第 11 周 | 4 月 2 日 | 双节点手势控制 | ✅ 已完成 | [双节点手势控制](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week11/week11_individual_gesture_network_system_cn) |
| 第 15 周 | 5 月 6 日 | Web控制界面设计与实现 | ✅ 已完成 | [Web控制界面设计与实现](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week15/week15_interface_application_programming_personal_cn) |
| 第 13-15 周 | 5月 10 日 | 旋转结构机械设计 | ✅ 已完成 | [运动机构设计](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week16/week16_system_integration_mechanism_design_cn) |
| 第16周 | 5 月 7 日-5月 14 日 | 系统集成 | ✅ 已完成 | [系统集成设计](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week16/week16_system_integration_design_cn) |
| 第17周 | 5月15-21日 | 最终方案 PCB 设计与程序调试 | 📅 规划中 | - |
| 第18周 | 5月 22-27 日 | 最终方案结构完善与测试 | 📅 规划中 |  |
| 第19周 | 5月28日-6月4日 | 文档整理与展示准备 | 📅 规划中 | - |
| 6月9-13日 | - | 最终项目展示 | 📅 规划中 | - |


---

[第1周：最终项目构思](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week01/week01_final_project_ideas_cn)

![](/images/final_project/w01-2-fn.jpg)

完成了智幻灯笼项目的初步构思，确定了核心功能和技术路线。

---

[第2周：3D 建模智幻灯笼](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week02/week02_3d_modeling_cn)

![](/images/final_project/w02-fn.jpg)

使用Fusion 360完成了灯笼外壳的3D建模设计，为后续激光切割做准备。

---

[第3周：激光切割灯笼外壳](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week03/week03_laser_cutting_lantern_shell_cn)

![](/images/final_project/w03-fn.jpg)

成功激光切割并组装了六边形木质灯笼外壳，验证了设计的可行性。

---

[第 6 周：控制电路PCB设计](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week06/week06_individual_assignment_pcb_design_cn)

![](/images/final_project/w06-fn.jpg)

设计了基于ESP32C3的控制电路PCB，集成了手势传感器和输出控制接口。

---

[第 8 周：电路板制作](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week08/week08_individual_assignment_pcb_production_cn)

![](/images/final_project/w08-fn.jpg)

使用数控铣床成功制作了控制电路PCB，并完成了元器件焊接与基础测试。

---

[第 9 周：手势控制实现](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week09/week09_individual_gesture_sensor_cn)

![](/images/final_project/w09-fn.jpg)

实现了APDS-9960手势传感器的集成，成功识别上下左右四个方向的手势输入。

---

[第 10 周：输出控制系统](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week10/week10_individual_gesture_fan_led_cn)

![](/images/final_project/w10-fn.jpg)

完成了电机和LED灯带的驱动控制，实现了手势与输出设备的联动效果。

---

[第 11 周：双节点手势控制](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week11/week11_individual_gesture_network_system_cn)

![](/images/final_project/w11-fn.jpg)

开发了基于ESP-NOW协议的双节点通信系统，实现了多设备间的手势信息传输与同步控制。

---

[第 15 周 Web控制界面设计与实现](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week15/week15_interface_application_programming_personal_cn)

![](/images/final_project/w15-fn.jpg)

设计并实现了响应式Web控制界面，支持远程调节灯光效果和电机开关。

---

[第15周：运动机构设计](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week16/week16_system_integration_mechanism_design_cn)

![](/images/final_project/w16-1-fn.jpg)

设计并3D打印了旋转笼机构和齿轮系统，解决了传动和稳定性问题。

---

[第16周：系统集成设计](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week16/week16_system_integration_design_cn)

![](/images/final_project/w16-2-fn.jpg)

---

[第17周：智幻走马灯圆环 PCB 设计与制造](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week17/week17_wildcard_week_project_cn)

![](/images/final_project/w17-fn.jpg)

重新设计了系统架构，整合了机械、电子和软件子系统，为最终成品奠定基础。


## 已解决与待解决的问题
### 已解决的问题
+ **灯笼外壳设计与制作**：在第3周成功设计并激光切割了六边形木质外壳
+ **旋转笼机构设计**：设计并3D打印了具有适当公差的可靠旋转笼结构
+ **齿轮系统实现**：计算了合适的齿轮模数（1.11mm）并设计了使用钢轴的功能性齿轮系统
+ **电机选择与集成**：选择了适当的N20双轴蜗轮蜗杆电机（130rpm），为旋转机构提供足够扭矩
+ **系统架构重新设计**：完全修订了系统架构，以解决原始设计中的局限性
+ **组件选择与采购**：为改进后的设计确定并获取了所有必要的电子组件

### 待解决的问题
+ **圆形PCB设计与制作**：需要设计并生产支持所有所需接口的新圆形PCB
+ **多方向手势识别**：实现三个并联的APDS-9960传感器，实现全方位手势控制
+ **RGB灯带集成**：安装并编程双RGB灯带，实现360°视觉效果
+ **电机控制系统**：将Grove Mini Fan驱动器与XIAO ESP32C3集成，实现精确的电机速度控制
+ **WiFi和MQTT通信**：开发无线通信系统，实现多灯笼同步
+ **最终组装与测试**：将所有子系统整合为一个完整产品并进行全面测试
+ **电源管理系统**：实现可充电电池系统与高效电源管理（备选，如果有时间会尝试一下）

## 下一步工作计划
1. **PCB设计与制作（5-7天）**: 
    - 完成圆形PCB原理图设计
    - 设计PCB布局和走线
    - 制造PCB并组装元器件
    - 测试所有接口和连接
2. **组件集成（2-3天）**: 
    - 连接并测试手势传感器
    - 安装并编程RGB灯带
    - 集成电机系统
    - 连接并测试电池系统
3. **软件开发（2-3天）**: 
    - 开发多传感器处理固件
    - 实现RGB和电机控制逻辑
    - 设置WiFi和MQTT通信
    - 创建手势控制和设备同步功能
4. **机械集成（5-7天）**: 
    - 完善传动机构
    - 调整灯笼外壳以容纳PCB和电池
    - 完成最终组装和平衡调整
    - 根据测试计划进行全面测试

## 项目文档计划
最终项目文档将包含以下部分：

1. **项目概述**
    - 项目背景与灵感来源
    - 功能介绍与技术特点
    - 应用场景与价值
2. **设计过程**
    - 机械结构设计
    - 电子系统设计
    - 软件系统设计
    - 设计迭代与改进
3. **制作过程**
    - 材料与组件清单
    - 制作步骤详解
    - 遇到的问题与解决方案
4. **成果展示**
    - 最终成品展示
    - 功能演示视频
    - 使用指南
5. **反思与总结**
    - 项目成果评估
    - 学习收获与体会
    - 未来改进方向
    - 相关源文件


## 参考资料与资源
+ [Fab Academy课程大纲](http://academany.fabcloud.io/fabacademy/2025/schedule.html)
+ [中国传统走马灯资料](https://www.sohu.com/a/449472236_120214181)
+ + [XIAO ESP32C3 文档](https://wiki.seeedstudio.com/XIAO_ESP32C3_Getting_Started/)
+ [ESP32C3开发资源](https://docs.espressif.com/projects/esp-idf/en/latest/esp32c3/index.html)
+ [APDS-9960手势传感器文档](https://www.broadcom.com/products/optical-sensors/integrated-ambient-light-and-proximity-sensors/apds-9960)
