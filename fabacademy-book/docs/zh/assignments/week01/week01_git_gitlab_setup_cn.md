---
layout: doc
title: 第1周：Git 及 GitLab 环境配置 | 冯磊 Fab Academy 2025
description: Fab Academy 2025 第1周 Git 及 GitLab 环境配置与使用指南
head:
  - - meta
    - name: keywords
      content: fab academy, Git, GitLab, 版本控制, 环境配置
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第1周：最终项目构思'
  link: '/zh/assignments/week01/week01_final_project_ideas_cn'
next:
  text: '第1周 VitePress 网站开发与部署指南'
  link: '/zh/assignments/week01/week01_web_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 第1周：Git 及 GitLab 环境配置与使用指南

作为 Fab Academy 的学员，掌握版本控制工具是我们的第一个技术挑战。这不仅关系到日常作业的提交，更是现代数字制造项目管理的基础技能。让我分享一下我的配置过程和经验总结。

## 环境配置
我是一个 macOS 用户，配置 Git 环境的第一步是安装 Git 本身。虽然 macOS 通常预装了 Git，但为了确保使用最新版本，我建议通过 Homebrew 重新安装。

首先，如果尚未安装 Homebrew，我们需要打开终端并执行以下命令：

```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

安装完成后，使用 Homebrew 安装 Git：

```bash
brew install git
```

安装完成后，我们可以通过以下命令验证安装并查看 Git 版本：

```bash
git --version
```

接下来是配置全局用户信息。这一步极其重要，因为这些信息将出现在我们的每次代码提交记录中：

```bash
git config --global user.name "你的名字"
git config --global user.email "你的邮箱"
```

为了确保配置正确，我们可以通过以下命令查看当前的配置信息：

```bash
git config --list
```

这些信息将出现在每次代码提交的记录中，是我们在项目协作中的电子签名。

其次是配置 SSH 密钥。这是一个容易被忽视但极其重要的步骤。通过命令：

```bash
ssh-keygen -t rsa -C "你的邮箱"
```

生成密钥后，我们需要将公钥添加到 GitLab 账户的 SSH Keys 设置中。这样做不仅提高了安全性，也让后续的代码推送变得更加便捷。

## 仓库初始化与管理
### 理解项目仓库
在 Fab Academy 课程中，每位学员都会获得一个专属的 Git 仓库（通过邮件方式告知）。这个仓库不仅仅是存放代码的地方，更是我们记录学习历程、展示项目成果的数字档案馆。通过这个仓库，我们的文档会自动发布到个人网站上，供评估师审阅并与其他学员分享。

![画板](/images/week01/w01-gitlab-01.webp)

> 左图是我的个人仓库的样子，不过这里的内容只有我个人账户可见。手指处的链接是这个仓库里存储的我个人网站的公开链接：[https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/)，点击后可以在右侧看到自己个人网站的样子。现在这个网站还只是一个通用模板，接下来我们将着手重建它
>

### 工作目录设置
在开始克隆仓库之前，我们需要选择一个合适的工作目录。建议在本地创建一个专门的文件夹来存放 Fab Academy 的所有内容。例如：

```bash
# 创建工作目录
mkdir ~/Documents/FabAcademy2025
cd ~/Documents/FabAcademy2025

# 克隆仓库
<NAME_EMAIL>:academany/fabacademy/2025/labs/chaihuo/students/lei-feng.git
```

### 目录结构解析
克隆完成后，我们会得到一个完整的项目模板。这个模板的目录结构经过精心设计，每个文件夹都有其特定用途：

```plain
lei-feng/                    # 项目根目录
├── public/                  # 网站发布目录
│   ├── 404.html            # 404错误页面
│   ├── about.html          # 个人介绍页面
│   ├── assignments/        # 作业文档目录
│   │   ├── week01.html     # 第一周作业
│   │   └── final-project.html  # 最终项目文档
│   ├── images/             # 图片资源目录
│   │   ├── avatar-photo.jpg    # 个人头像
│   │   └── sample-photo.jpg    # 示例图片
│   ├── index.html          # 网站首页
│   └── style.css           # 样式表文件
├── .gitlab-ci.yml          # GitLab CI/CD配置文件
└── README.md               # 项目说明文档
```

其中，`.gitlab-ci.yml` 文件在项目中扮演着关键角色。这个配置文件定义了持续集成和持续部署(CI/CD)的流程，它告诉 GitLab 如何将我们的文档自动构建并发布到网站上。每当我们推送新的更改到仓库时，GitLab 会自动执行这个文件中定义的任务，将 public 目录中的内容发布到我们的个人网站上。这个自动化过程使我们能够专注于内容创作，而不必担心部署细节。

README.md 通常用于说明项目的基本信息和使用说明。

网站内容主要存放在 public 目录中。`index.html` 作为网站的入口页面，是访问者首先看到的内容。`assignments` 目录用于存放每周的作业文档，我们将在这里详细记录学习过程和项目进展。`images` 目录集中管理所有图片资源，确保了文档结构的整洁性。`style.css` 则定义了整个网站的视觉风格，保证了界面的统一性和专业性。

这个结构不仅符合课程要求，更为后续的文档管理提供了清晰的框架。随着课程的推进，我们会逐步充实每个部分的内容，最终形成一个完整的项目档案。

## 日常工作流程
完成配置后，日常常用的工作流程如下：

第一步是确保本地代码与远程仓库同步：

```bash
git pull origin master
```

然后在进行文档编辑或代码修改时，我会定期执行：

```bash
git add .
git commit -m "更新说明"
```

这里特别要注意提交信息的规范性。一个好的提交信息应该简明扼要地说明本次更改的内容，这对于日后的版本回溯非常重要。

最后是推送更新到远程仓库：

```bash
git push origin master
```

## 问题排查与解决
在使用过程中，我遇到过一些常见问题，这里分享解决方案：

如果推送时遇到权限问题，首先检查 SSH 密钥是否正确配置。可以通过以下命令测试连接：

```bash
ssh -T ***********************
```

如果遇到合并冲突，不要慌张。首先通过：

```bash
git status
```

查看具体冲突文件，然后手动解决冲突，最后重新提交。

## VSCode 工作流程设置与建议
对于刚接触版本控制的新手开发者来说，命令行操作往往需要记忆大量命令，这可能会带来一定的学习门槛。特别是在需要频繁进行代码提交、分支管理等日常操作时，使用 [Visual Studio Code](https://code.visualstudio.com/) （简称 VSCode）这类现代化 IDE 的图形化界面，能够显著降低操作复杂度，提高开发效率。

VSCode 提供了优秀的 Git 集成支持，能够让我们以图形化方式管理代码版本。下面介绍如何搭建一个适合 Fab Academy 课程使用的 VSCode 开发环境。

### 打开项目仓库
在 VSCode 官网下载并安装 VSCode 并启动 VSCode 后，我们有两种方式打开项目仓库：

1. 通过菜单：选择 "File > Open Folder"，导航到我们之前创建的 FabAcademy2025 目录，选择克隆下来的 `lei-feng` 文件夹。
2. 通过命令行：在项目目录下执行：

```bash
code .
```

第一次打开时，VSCode 可能会询问是否信任此文件夹的作者，选择"是"即可。此时，我们应该能在左侧资源管理器中看到完整的项目文件结构。

![](/images/week01/w01-gitlab-vscode-01.png)

> 打开项目文件后的 VSCode 界面，左侧资源管理器可以看到仓库的文件夹内容。
>

### 必要扩展安装
为了更好地支持我们的开发工作，我们需要安装一些扩展。点击左侧活动栏的"扩展"图标（快捷键 Ctrl+Shift+X），然后搜索并安装以下扩展：

1. GitLens 
    - 搜索 "GitLens"
    - 这个扩展让我们能直观地查看代码的修改历史
    - 安装后会在编辑器中显示每行代码的最后修改信息
2. Live Server
    - 用于预览我们的网站效果
    - 安装后，在 index.html 文件上右键，选择 "Open with Live Server"，即可在浏览器中实时预览网站
3. HTML CSS Support
    - 提供 HTML 和 CSS 的智能提示
    - 帮助我们更高效地编辑网站代码
4. Live Preview
    - 用于在 VSCode 中直接预览 HTML 文件

![](/images/week01/w01-gitlab-vscode-02.png)

> 成功安装 Live Preview 扩展后的状态
>

现在，我们点击左侧的文件资源管理器图标，在左侧资源列表中，点击 `about.html`，可以看到一个 show Preview 的图标，尝试点击它。

![画板](/images/week01/w01-gitlab-vscode-03.png)

> 安装 Live Preview 扩展后，访问.html 文件会增加一个预览图标
>

下载，我们能够马上看到这个 html 页面实际显示的效果。

![](/images/week01/w01-gitlab-vscode-04.png)

> 开启 html 预览后，我们的 html 文件呈现出了我们最终发布时的样子，而不是一堆难以理解的 HTML 代码了。
>

在代码编辑区找到“I am Your name.”这句，改成自己的名字，预览区可以马上看到变化。然后保存修改后的文件。

![](/images/week01/w01-gitlab-vscode-05.png)

> 修改 about.html 文件中的名字并保存
>

### 使用 VSCode 管理代码版本
VSCode 的源代码管理功能让 Git 操作变得更加直观。点击左侧活动栏的源代码管理图标（快捷键 Ctrl+Shift+G），我们就能看到所有的代码变更。

刚才我们编辑并保存了 `about.html` 这个文件，并如要提交对 `about.html`的修改，我们可以：

1. 在源代码管理面板中，找到修改过的文件
2. 将鼠标悬停在文件上，点击 "+" 号暂存更改
3. 在上方的文本框中输入描述性的提交信息“**Update about.html with personal name**”
4. 点击窗口顶部的"提交"按钮（✓）完成提交

![画板](/images/week01/w01-gitlab-vscode-06.png)

5. 使用“同步”将提交推送到远程仓库，要完成同步操作，你可以：
    1. 点击图中显示的"源代码管理"最右侧的"..."（更多选项）
    2. 在弹出的菜单中选择"拉取，推送"
    3. 选择"同步"将您的更改同步到远程仓库

![](/images/week01/w01-gitlab-vscode-07.png)

> 在源代码管理界面进行提交
>

完成推送后，我们通过浏览器访问远程仓库，打开 `about.html` 文件，可以看到名字部分已被修改。

![](/images/week01/w01-gitlab-vscode-08.png)

> 推送后的远程仓库的 `about.html` 文件也获得了更新，姓名的部分被修改了
>

### 实用功能说明
在日常开发中，这些 VSCode 功能会特别有用：

1. 预览 HTML 文件
    - 安装 Live Server 后，右键点击 HTML 文件
    - 选择 "Open with Live Server"
    - 网站会在浏览器中打开，并随着文件修改自动刷新
2. 文件对比
    - 在源代码管理面板中点击修改过的文件
    - VSCode 会显示并排的差异对比视图
    - 帮助我们准确了解做出的修改
3. 集成终端
    - 使用快捷键 Ctrl+` 打开内置终端
    - 可以直接执行 Git 命令
    - 无需切换到外部终端窗口

通过这些工具的配合使用，我们可以构建一个高效的开发环境，专注于课程内容的创作和项目的推进。随着对这些工具的熟悉，我们会发现版本控制和文档管理变得越来越得心应手。

### 工作建议
基于我的使用经验，我想分享几点建议：

第一，养成定期提交的习惯。不要等到完成大量工作后才一次性提交，这样既增加了出错风险，也让版本控制失去了意义。

第二，重视提交信息的质量。一个好的提交信息应该能让其他人（包括未来的自己）快速理解这次修改的内容和原因。

第三，在进行重要修改前创建分支。这样可以在保证主分支稳定的同时，安全地进行新功能的开发和测试。

掌握这些工具和流程或许需要一些时间，但这些投入是值得的。因为在接下来的课程中，这些将成为我们日常工作的重要组成部分。

有了这些知识，我们就可以开始着手搭建个人网站了。