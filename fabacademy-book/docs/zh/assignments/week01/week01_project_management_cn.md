---
layout: doc
title: 第1周：项目管理 | 冯磊 Fab Academy 2025
description: 冯磊参与 Fab Academy 2025 第一周项目管理的学习记录与总结
head:
  - - meta
    - name: keywords
      content: fab academy, 项目管理, git, 版本控制, 文档编写
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '关于我'
  link: '/zh/about/about_cn'
next:
  text: '第1周：最终项目构思'
  link: '/zh/assignments/week01/week01_final_project_ideas_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 第1周：项目管理

## 概述

作为 Fab Academy 2025 课程的开篇，第一周的内容聚焦于项目管理这一基础却至关重要的主题。这一周的课程不仅为我们接下来为期24周的学习旅程奠定基础，更帮助我们建立起数字制造项目开发所需的核心工作流程。

课程伊始，<PERSON> G<PERSON>henfeld 教授强调了版本控制系统在现代制造业中的重要性。在数字制造领域，项目管理不仅仅是关于时间规划，更是关于如何有效地追踪和记录整个制造过程。通过使用 [Git](https://git-scm.com/) 这样的分布式版本控制系统，我们能够精确地记录每一个设计改动，实现团队协作，并在需要时轻松回溯到任何历史版本。

课程还详细介绍了如何建立个人项目文档网站。这个网站不仅是作业提交的平台，更是展示我们学习历程的数字档案馆。通过 [HTML](https://developer.mozilla.org/en-US/docs/Web/HTML) 和 [CSS](https://developer.mozilla.org/en-US/docs/Web/CSS) 的基础知识学习，我们将能够构建既美观又实用的项目文档，为之后的课程开发记录做好准备。

特别值得一提的是，课程强调了开源精神和知识共享的重要性。每位学员都需要将自己的项目过程完整记录并公开分享，这不仅有助于同学之间的互相学习，也为全球的创客社区贡献经验。这种开放和共享的理念，正是推动数字制造技术不断发展的重要动力。

对我而言，这节课最大的收获是理解到项目管理在数字制造中的核心地位。它不仅是一种工具和方法，更是保证项目有序推进、实现创意的重要保障。在接下来的课程中，这些项目管理的基础知识将贯穿始终，帮助我们更好地完成每周的作业，并最终实现期末项目的目标。

学习文档索引链接：[http://academy.cba.mit.edu/classes/project_management/index.html](http://academy.cba.mit.edu/classes/project_management/index.html)
