---
layout: doc
title: 第1周：最终项目构思 | 冯磊 Fab Academy 2025
description: Fab Academy 2025 第1周最终项目构思与设计过程全记录
head:
  - - meta
    - name: keywords
      content: fab academy, 最终项目, 数字制造, 走马灯, 创新设计
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第1周：项目管理'
  link: '/zh/assignments/week01/week01_project_management_cn'
next:
  text: '第1周：Git 及 GitLab 环境配置与使用指南'
  link: '/zh/assignments/week01/week01_git_gitlab_setup_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 第1周：最终项目构思

在开始介绍具体的项目构想之前，我认为有必要先明确 Fab Academy 对最终项目的具体要求。这些要求不仅是评估标准，更是指导我们项目构思的重要参考框架。

## 项目要求解析

Fab Academy 期望每位学员在最终项目中展现出全面的数字制造能力。具体来说，项目需要整合以下核心技能：

首先是设计能力，我们需要熟练运用 2D 和 3D 建模工具，从概念草图发展到精确的工程图纸。其次是制造技术，项目必须结合增材制造（如 3D 打印）和减材制造（如激光切割、数控铣削）等工艺。在电子方面，要求我们能够独立设计并制作印刷电路板，包含必要的输入输出设备。最后，在编程领域，我们需要通过嵌入式系统将这些硬件整合成一个智能化的整体。

特别值得注意的是，项目强调"自主制造"理念。这意味着我们需要尽可能减少使用现成部件，而是通过数字制造技术自主完成关键组件的设计与制作。

## 项目构想的演进

基于这些要求，我提出了四个初步构想，每个都有其独特的挑战和机遇：

+ 第一个想法是带可遥控彩灯的风筝。这个构想源于我对航拍的兴趣，试图将传统风筝与现代光效技术结合。然而，经过分析，我发现这个项目可能难以充分展现课程要求的多项技能。
+ 第二个构想是智能桌面系统。这是一个颇具野心的项目，我想把家里工作桌面做一次智能化改造，涉及复杂的机械结构和智能控制。但考虑到时间限制和个人能力，这个项目的工程量可能过于庞大。
+ 第三个方案是面向教师的多人分组定位与防丢装置。这个想法来自于我在教育领域工作时，看到教师带班出游的混乱局面产生的想法。但深入思考后发现其电子系统的复杂度可能超出我当前的能力范围。

## 最终选择：智幻走马灯

经过深入思考，我选择了智幻走马灯作为最终项目。这个选择着眼于将中国传统文化艺术与现代数字制造技术完美融合，创造一件既传承传统又富有创新的作品。

## 传统与创新的融合

传统走马灯是一种精妙的民间艺术品，通常利用烛火产生的热气流带动顶部风叶旋转，从而使镂空图案的灯罩转动，在周围墙面投射出连续动画般的光影效果。这种古老的设计凝聚着先人的智慧，但同时也面临着明火安全、稳定性差等局限。

| ![](/images/week01/week01_final_project_ideas1.jpg) | ![](/images/week01/revolving_lantern.gif) |
| ------------------------------------------------- | --------------------------------------- |

> 中国传统走马灯的构造与实体效果

在保留传统走马灯优雅机理的基础上，我计划通过数字制造技术对其进行创新改造：

首先是动力系统的现代化。我将使用 Seeed Studio ESP32C3 开发板作为主控，替代传统的热气流驱动原理，集成电机控制系统作为主要动力。相较于原计划的双驱动机制，这样的设计更加可控且能够保证系统的稳定性，同时大大降低了实现的复杂度。

其次是光源的智能化升级。传统蜡烛将被可编程的LED阵列取代，不仅提升了安全性，更为作品带来了丰富的视觉可能性。通过精心设计的控制电路，LED可以实现颜色变换、亮度调节等多样化效果。

交互方式也将有创新性的突破。我计划集成超迷你 APDS-9960 手势传感器，实现直观的手势控制功能：

+ 左右手势：控制走马灯正转或反转
+ 上下手势：调节灯光亮度高低

![](/images/week01/w01-fp-1.png)

> 左右手势：控制走马灯正转或反转，上下手势：调节灯光亮度高低
>

这个传感器芯片具有集成的 IR LED 和驱动器，以及四个可感应 LED 反射的 IR 光量的定向光电二极管，能够以高精度测量物体与传感器正面的距离。它通过 I2C 接口连接到微控制器，使用方便且稳定可靠。

同时，还将利用 ESP32C3 的 WiFi 功能，开发简单的手机应用，实现对灯的开关、转动方向、亮度的远程控制，为传统工艺增添现代科技的便捷性。

在结构设计方面，我将运用数字制造工艺重新诠释传统美学。灯罩将采用激光切割技术制作，这使得传统图案可以更加精致细腻。底座则计划通过3D打印实现，融入现代设计语言的同时确保结构的稳定性。

## 技术挑战与创新点
这个项目虽然规模适中，但包含了多个具有挑战性的技术要点：

1. **机械设计与制造**：需要设计精确的电机驱动机制，确保灯罩旋转的平稳性和可靠性。底座需要合理安排元器件布局，兼顾结构强度与散热需求。这将结合激光切割与3D打印工艺实现。
2. **电子系统**：围绕 ESP32C3 开发板设计控制电路，整合 APDS-9960 手势传感器、LED驱动和电机控制模块。需要设计自定义PCB并解决多模块协同工作的技术问题。
3. **交互设计**：实现直观的手势控制体验，并开发简洁易用的手机控制界面。需要在固件中实现稳定的手势识别算法，以及流畅的无线通信机制。
4. **能源管理**：考虑采用可充电锂电池为整个系统供电，需要设计高效的电源管理电路，平衡性能与续航时间。

通过这个项目，我希望不仅展示数字制造技术的应用能力，更能诠释传统工艺与现代科技的优雅融合。这正是我理解的数字制造之道：既要掌握先进技术，又要心怀传统智慧，在创新中传承，在传承中创新。
