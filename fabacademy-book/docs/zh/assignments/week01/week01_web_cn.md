---
layout: doc
title: 第1周：个人网站搭建 | 冯磊 Fab Academy 2025
description: Fab Academy 2025 第一周 VitePress 网站开发与部署指南
head:
  - - meta
    - name: keywords
      content: fab academy, VitePress, 网站开发, 静态网站, 多语言, 部署
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第1周：Git 及 GitLab 环境配置'
  link: '/zh/assignments/week01/week01_git_gitlab_setup_cn'
next:
  text: '第1周：计算机辅助设计'
  link: '/zh/assignments/week02/week02_computer_aided_design_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 第1周：构建个人网站：从写作到发布的工作流程

## 工作流程概述

在 Fab Academy 课程中，构建和维护个人文档网站是一项核心任务。我想通过这个指南的编写，建立一个高效的工作流程，从内容创作到网站部署的每个环节都会详细说明。我们将使用 VitePress 这个现代化的静态站点生成器来构建网站，它基于 Vue 3 和 Vite，提供了优秀的开发体验和性能表现。

![画板](/images/week01/w01-web-vscode-1-cn.png)

> 我设计了 5 个步骤的工作流，来实现从文档到个人主页的过程

这个工作流程分为五个主要阶段，每个阶段都有其特定的工具和注意事项：

+ 阶段 1：内容准备在这个阶段，我个人习惯使用中文进行内容创作和图片管理。选择合适的写作工具可以让您专注于内容本身，而不被技术细节干扰。
+ 阶段 2：导出转换将语雀中的内容导出为 Markdown 格式，同时处理和优化图片资源。确保所有资源都符合网站的组织结构要求。
+ 阶段 3：多语言处理利用 AI 工具辅助翻译，并由人工进行专业术语审核，确保双语内容的准确性和专业性。
+ 阶段 4：VitePress 开发使用 VitePress 进行网站开发，包括本地环境搭建、主题配置和内容整合。这个阶段需要关注开发体验和网站性能。
+ 阶段 5：部署上线
  通过 GitLab CI/CD 实现自动化构建和部署，确保内容更新能够及时反映在线上网站中。

通过这个结构化的工作流程，您可以更高效地管理文档工作，确保产出高质量的课程文档。在接下来的章节中，我们将详细介绍每个阶段的具体操作步骤和最佳实践。

## 1. 内容创作

因为常年用中文写作，所以我的思维模式是中文的，好在现在 AI 工具让内容进行跨语言转换已经变得越来越容易了。我日常的大部分文字工作都是在[语雀](https://www.yuque.com/)中进行的，包括自己编辑或翻译出版的书籍。选择这个工具作为主要的内容创作平台是基于以下考虑：

+ 语雀提供了我能找到的功能最强的在线文字编辑环境，支持导出 Markdown 格式，让我能够专注于内容创作。
+ 其强大的画板功能可以让我快速编辑课程过程中的各种插图，还有对公式语言、代码块、文本绘图语言、UML 图、本地文件嵌入和多样的第三方服务支持等等。
+ 更重要的是，它支持多平台访问、团队协作和版本控制，这让我可以在写作过程中方便地与出版社和同事们进行交流和反馈。

![](/images/week01/yuquelogo.webp)

> yuque app 的 logo

当然，你也可以选择自己熟悉的内容创作工具，不过建议选择那些支持导出 Markdown 文档格式的，方便后续网页的转换输出，专用的内容创作工具能让你专注于内容本身的构建，而不用被代码语言打扰。

中文版内容创作主要做好两件事：

+ 在语雀中，我按照 Fab Academy 的课程结构组织文档，用中文建立周课程文档，记录每周的学习内容和项目进展。
+ 然后创建图片库，统一管理课程相关的图片资源，为后续网页发布做准备。

第一周的作业文档部分接近完成时，我语雀里的文档目录结构如下所示：

![](/images/week01/week01_cn_yuque1.png)

> 在语雀写的第一周作业有关的文档

完成这些后，将修订后的中文文档导出为 Markdown 格式（.md文件）

## 2. 导出转换

在完成内容创作后，我们需要将语雀中的文档转换为适合 VitePress 使用的 Markdown 格式，并对资源文件进行合理的优化和组织。这个阶段的工作对于确保网站性能和符合 Fab Academy 的技术要求至关重要。

### 文档导出和格式转换

从语雀导出 Markdown 文档时，需要注意以下几点：

1. 在导出前检查文档中的图片引用是否使用相对路径
2. 确保文档中的代码块正确标注了编程语言
3. 检查特殊字符的编码是否正确

### 图片资源优化

由于 Fab Academy 对每次作业提交有 10MB 的大小限制，图片优化变得尤为重要。以下是处理图片资源的关键步骤：

### 图片压缩策略

对于不同类型的图片，我们采用不同的优化策略：

对于照片类图片：

+ 将图片尺寸调整到合适的显示大小（通常宽度不超过 1200px）
+ 使用 JPG 格式，质量设置为 80-85%
+ 考虑使用渐进式 JPEG 提升加载体验

对于截图和界面图：

+ 使用 PNG 格式以保持文字清晰
+ 适当裁剪，只保留关键内容区域
+ 考虑使用 WebP 格式作为替代，可以显著减小文件体积

### 推荐的图片处理工具

推荐使用免费工具 [Squoosh](https://squoosh.app/) 进行图片压缩处理。

### 文件命名规范

为了保持项目的可维护性和多语言支持，我们采用严格的文件命名规范。

#### 文档文件命名

文档文件应遵循以下格式：

+ 中文文档：`week[XX]_[描述]_cn.md`
+ 英文文档：`week[XX]_[描述]_en.md`

例如：

```plain
week01_project_management_cn.md
week01_project_management_en.md
```

#### 图片资源命名

图片文件采用以下命名结构：

```plain
w[周数]_[描述性名称]_[语言标识].[扩展名]
```

例如：

+ 语言相关的截图：`w01_git_setup_cn.png`
+ 通用图片：`w01_3d_model_v1.jpg`

### 文件组织原则

为确保项目的长期可维护性，我们建议：

1. 保持一致的命名风格
   - 使用小写字母
   - 用下划线代替空格
   - 版本号采用 v1、v2 格式
2. 资源文件分类存储
   - 将截图和项目照片分开存放
   - 按周次组织文件夹
   - 保持语言版本文件的相同路径结构

### 转换后的质量检查

在完成格式转换和资源优化后，需要进行以下检查：

1. 验证所有图片链接是否正确
2. 验证总体文件大小是否在限制范围内

通过严格遵循这些规范和优化步骤，我们可以确保网站既能保持良好的性能，又便于维护和更新。这些工作虽然看似繁琐，但对于建立一个专业的文档网站来说是必要的。

## 3. 多语言处理

在 Fab Academy 的文档工作流程中，多语言内容处理是一个关键环节。本阶段的目标是确保中英文内容的准确性和专业性，同时建立一个高效的翻译工作流程。

### AI 辅助翻译流程

当我完成中文内容的编写后，会使用 AI 工具（如 Claude）进行初步翻译。在使用 AI 翻译时，建议采用以下方法：

首先将完整的 Markdown 文件提供给 AI，并明确说明这是一份技术文档。请求 AI 在翻译时保留所有的 Markdown 格式，包括标题层级、代码块、图片引用等结构。这样可以确保翻译后的文档保持原有的格式完整性。

### 专业术语审核

AI 翻译后的内容需要进行人工审核，特别注意以下几个方面：

对照 Fab Academy 官方文档中使用的术语，确保技术词汇的一致性。例如，"3D 打印"应统一翻译为"3D printing"而非"three-dimensional printing"。检查数字制造领域的专业词汇，确保它们符合行业标准用语。保持专业术语在整个文档中的一致性。

### 多语言内容同步

为确保中英文内容的同步更新，建议在 VSCode 中并排打开两个文件进行对照。这样可以方便地进行内容比对和更新。同时，对于需要本地化的图片（如包含文字的截图），应及时制作对应的英文版本。

这个阶段的工作虽然耗时，但对于确保文档的专业性和可读性至关重要。通过建立规范的多语言处理流程，可以显著提高工作效率并保证内容质量。

## 4. VitePress 开发：项目搭建与配置指南

### 项目初始化

[VitePress](https://vitepress.dev/) 项目的搭建需要按照特定步骤进行，以确保开发环境的正确配置。首先，我们需要创建项目并安装必要的依赖：

```bash
# 创建项目目录
mkdir fabacademy-book
cd fabacademy-book

# 初始化 npm 项目
npm init -y

# 安装 VitePress 和 Vue
npm add -D vitepress vue

# 创建文档目录结构
mkdir -p docs/{.vitepress,en,zh,public/images}
```

### 目录结构设置

在 VitePress 中，文档的组织结构直接影响网站的导航和访问体验。以下是推荐的目录结构：

```bash
docs/
├── .vitepress/
│   ├── config.ts          # 主配置文件
│   └── theme/            # 主题相关文件
├── en/                   # 英文内容
│   ├── about/
│   │   └── about_en.md
│   └── assignments/
│       └── week01/
├── zh/                   # 中文内容
│   ├── about/
│   │   └── about_cn.md
│   └── assignments/
│       └── week01/
├── public/              # 静态资源
│   └── images/
└── index.md            # 网站首页
```

### 配置多语言支持

VitePress 的多语言支持主要通过配置文件实现。在 `.vitepress/config.ts` 中，我们需要设置语言相关的配置项：

```typescript
import { defineConfig } from 'vitepress'

export default defineConfig({
  locales: {
    '/en/': {
      label: 'English',
      lang: 'en'
    },
    '/zh/': {
      label: '简体中文',
      lang: 'zh-CN'
    }
  },
  themeConfig: {
    locales: {
      '/en/': {
        // 英文界面配置
      },
      '/zh/': {
        // 中文界面配置
      }
    }
  }
})
```

### 导航与侧边栏配置

为了确保良好的文档浏览体验，需要在 `config.ts` 中为不同语言版本配置独立的导航菜单和侧边栏：

```typescript
themeConfig: {
  nav: [
    // 顶部导航配置
  ],
  sidebar: {
    '/en/assignments/': [
      // 英文侧边栏配置
    ],
    '/zh/assignments/': [
      // 中文侧边栏配置
    ]
  }
}
```

### 主题定制与样式调整

VitePress 允许通过主题配置来自定义网站的外观。创建 `.vitepress/theme/index.ts` 文件来定制主题：

```typescript
import DefaultTheme from 'vitepress/theme'
import './custom.css'

export default {
  extends: DefaultTheme,
  enhanceApp({ app }) {
    // 注册自定义组件或进行其他增强
  }
}
```

您说得对。让我补充关于 Markdown frontmatter 的部分，这是 VitePress 文档中的一个重要元素。

### Markdown 文档配置

在 VitePress 中，每个 Markdown 文档都应在文件开头添加 frontmatter 配置，它定义了该页面的元数据和特定配置。这些配置对于文档的组织和展示至关重要。

这些配置信息需要放在文档最前面，使用三个短横线包裹。以下是一个标准示例：

```yaml
---
title: 项目管理基础
description: Fab Academy 第一周课程：项目管理方法与工具介绍
date: 2025-01-15
tags: ['project management', 'documentation', 'git']
prev: 
  text: '课程介绍'
  link: '/zh/about/introduction'
next:
  text: '版本控制'
  link: '/zh/assignments/week01/git-setup'
---
```

#### 多语言文档的特殊考虑

对于多语言网站，我们需要确保中英文文档的 frontmatter 配置保持同步但内容相应调整。例如，一个双语文档的对应关系如下图所示：

![](/images/week01/w01-web-vscode-md1.png)

> about_cn.md 和 about_en.md 文档前面添加的 frontmatter 设置，里面还包含了页面底部的上一页，下一页的名称与链接导航。

#### 重要的 Frontmatter 字段

在编写文档时，应特别注意以下 frontmatter 字段的设置：

+ title: 定义页面标题，会显示在浏览器标签和导航中
+ description: 提供页面描述，有助于搜索引擎优化
+ date: 记录文档创建或更新日期
+ lang: 指定页面语言，确保正确的语言环境
+ layout: 可选择特定的页面布局模板
+ sidebar: 控制当前页面的侧边栏显示
+ tags: 为文档添加标签，便于内容组织和搜索
+ prev/next: 定义上一页和下一页的导航链接

通过合理配置这些 frontmatter 字段，我们可以优化文档的展示效果、改善导航体验，并确保多语言支持的正确实现。在开发过程中，应养成为每个文档添加适当 frontmatter 的习惯，这对于维护一个专业的文档网站至关重要。

### 本地开发与调试

配置完成后，在 `package.json` 中添加开发脚本：

```json
{
  "scripts": {
    "docs:dev": "vitepress dev docs",
    "docs:build": "vitepress build docs",
    "docs:preview": "vitepress preview docs"
  }
}
```

#### 本地预览与开发调试

在进行 VitePress 文档站点的本地开发时，为确保获得最佳的预览效果，我们需要采取一些特定的步骤。以下是我使用的开发预览流程。

#### 清理缓存与构建文件

首先，需要清理之前的构建文件和缓存，以确保预览时使用的是最新的配置和内容：

```plain
rm -rf docs/.vitepress/dist
rm -rf docs/.vitepress/cache
```

这个清理步骤很重要，因为它可以防止旧的构建文件和缓存影响新的开发预览。特别是在修改了配置文件或遇到一些异常情况后，执行这个清理操作可以帮助避免潜在的问题。

#### 启动开发服务器

清理完成后，使用以下命令启动开发服务器：

```plain
npm run docs:dev
```

执行此命令后，VitePress 会启动一个本地开发服务器。您将在终端中看到访问地址，通常是 `http://localhost:5173...`。此时，您可以在浏览器中打开这个地址来查看网站效果，如下图所示。

![画板](/images/week01/w01-web-vscode-md2.jpg)

> 在 VSCode 的终端里运行 `npm run docs:dev` 命令后，VitePress 会提供一个输出链接，通过浏览器访问这个链接可以看到网站效果。

### 实时预览功能

开发服务器提供了许多便捷的功能来提升开发体验。当您修改任何 Markdown 文件或配置文件时，网站会自动刷新以显示最新的更改。这种即时反馈机制让我们能够快速调整和优化内容。

例如，当您：

+ 修改 Markdown 文件内容时，页面会立即更新
+ 更改 `.vitepress/config.ts` 中的配置时，整个站点会自动重新加载
+ 添加或修改图片资源时，相关页面会实时刷新

### 性能优化建议

为确保文档网站的良好性能，建议采取以下措施：

首先，对于图片资源，使用适当的格式和大小，并考虑使用图片懒加载功能。其次，合理组织文档结构，避免过深的目录层级。最后，利用 VitePress 的内置优化功能，如路由预加载和按需加载。

### 版本控制

建议使用 Git 进行版本控制，并创建 `.gitignore` 文件排除不必要的文件：

```plain
node_modules
docs/.vitepress/dist
docs/.vitepress/cache
```

通过这些配置和优化措施，我们可以构建一个性能良好、易于维护的文档网站。在开发过程中，应持续关注网站的用户体验，并根据实际需求进行适当的调整和优化。

## 5. 在 VSCode 中进行网站部署

在使用 VSCode 进行 Fab Academy 文档网站开发时，我们可以通过集成的终端和 Git 功能来简化部署流程。以下是一个完整的操作指南。

### 本地构建验证

在 VSCode 中，首先打开集成终端（快捷键 Ctrl + `或 Command +`），运行构建命令：

```plain
# 清理之前的构建文件和缓存
rm -rf docs/.vitepress/dist
rm -rf docs/.vitepress/cache

# 构建项目
npm run docs:build
```

构建完成后，检查 `docs/.vitepress/dist` 目录，确保所有输出的网站的文件都已正确生成，如下图所示。

![](/images/week01/w01-web-vscode-html.png)

> 运行 `npm run docs:build` 命令后，导出的网站文件会出现在 `docs/.vitepress/dist` 目录下，我需要做的就是把这个目录下的文件全部同步到 gitlab 的 `lei-feng/public` 目录下。

### 使用 VSCode 的 Git 功能

VSCode 提供了优秀的 Git 集成功能，可以帮助我们方便地管理代码提交和推送。在提交更改之前，我们应该：

1. 打开 VSCode 的源代码管理视图（Source Control，快捷键 Ctrl+Shift+G 或 Command+Shift+G）
2. 检查变更的文件列表，确保没有不应该提交的文件
3. 编写清晰的提交信息，描述本次更新的内容
4. 使用 VSCode 的 diff 查看功能，再次确认所有更改

### 提交和推送流程

在 VSCode 的源代码管理界面中：

1. 暂存更改：选择要提交的文件，点击 "+" 号或使用 "Stage Changes" 命令
2. 输入提交信息：在消息框中输入描述性的提交信息
3. 提交更改：点击提交按钮或使用 Command+Enter (Mac) / Ctrl+Enter (Windows)
4. 推送到远程：点击同步更改按钮或使用命令面板中的 Git: Push 命令

### 部署后的验证

提交并推送后，我们可以在 VSCode 中：

1. 使用集成终端打开 GitLab Pipeline 页面
2. 监控构建进度
3. 检查构建日志中的潜在问题
4. 使用内置的浏览器预览功能查看部署后的网站

通过使用 VSCode 的这些集成功能，我们可以更高效地管理网站的开发和部署流程。这种集中式的工作环境不仅提高了效率，也减少了在不同工具间切换的需求。
