---
layout: doc
title: "第2周：3D建模 | 冯磊 Fab Academy 2025"
description: Fab Academy 2025 第二周 使用 FreeCAD 进行参数化 3D 建模学习与实践
head:
  - - meta
    - name: keywords
      content: fab academy, 3D建模, FreeCAD, 参数化设计, CAD, 灯笼建模
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第2周：像素图到矢量图的转换'
  link: '/zh/assignments/week02/week02_raster_to_vector_cn'
next:
  text: '第3周：计算机控制切割'
  link: '/zh/assignments/week03/week03_computer_controlled_cutting_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 第2周：3D 建模智幻灯笼

## FreeCAD 快速入门

使用 CAD 进行 3D 建模对我来说相对陌生，我之前只使用 [SketchUp](https://www.sketchup.com/) 绘制过一些建筑室内空间的草图，Neil 要求我们务必掌握参数化设计，并根据建议我下载并安装了 [FreeCAD](https://www.freecad.org/)。

说实话，学习 FreeCAD 对我来说是个有点艰难的过程，文档介绍的内容比较有限，这个软件要用起来的关键是操作方法。于是我找了一个 CAD 建模练习册，如下图所示，练习册有 100 多个 CAD 图纸，因为时间的原因，我尝试做出了前 3 个。

![](/images/week02/w02-2-1.png)

> 用来作为练习的 CAD 模型图册

![](/images/week02/w02-2-2.png)

> 在 FreeCAD 里完成的前 3 个模型

完成这 3 个练习后，算是基本对 FreeCAD 的使用有点感觉了，其基础要点如下。

#### 工作台切换

作为初学者，主要在下面两个工作台中来回切换：

+ ![](/images/week02/freecad-PartD-icon.svg)创建实体（ Part Design）：主要用于创建实体模型，下图展示了我的模型练习的实体模式工作台。

![](/images/week02/w02-freecad-1.jpg)

> 实体模式工作台

+ ![](/images/week02/freecad-Sketcher-icon.svg)创建草图（ Sketcher）：用于创建 2D 草图，是 3D 建模的基础。下图是上图

![](/images/week02/w02-freecad-2.png)

> 草图模式工作台，且所有点线都处于完全约束状态

#### 基本工作流程

+ 创建新实体（Body）
+ 选择轴平面添加草图（Sketch）
+ 绘制 2D 轮廓
+ 添加约束（Constraint）
+ 拉伸（Pad）、凹坑（Pocket）或旋转（Revolution）成形

#### 常用约束类型

+ 几何约束：垂直、水平、平行、垂直等
+ 尺寸约束：距离、角度、半径等
+ 相对约束：重合、相切、对称等

#### 参数化设计特点

+ 使用约束而不是固定尺寸
+ 通过改变参数快速修改模型
+ 保持设计意图的清晰性

## 智幻灯笼外部框架建模

春节去了中国湖南省衡阳的南岳大庙，看到庙里的路灯是中国传统的灯笼造型，如下图所示，觉得很适合作为智幻灯笼的外部框架，就尝试依据此图样进行建模。

![](/images/week02/Nanyue-Temple-Lantern.jpg)

下面是灯笼外部框架的建模过程。

### 创建新实体

打开 FreeCAD 创建新文件，点击![](/images/week02/freecad-PartD-icon.svg)创建新实体图标添加 `Body`，保存文件并命名为 `Lantern shell`，现在界面如下图所示。

![](/images/week02/w02-freecad-3-1.jpg)

> 创建新实体并保存文件

### 绘制矩形基本轮廓

灯笼整体为六边形棱柱，点击![](/images/week02/freecad-Sketcher-icon.svg)创建草图图标，会出现选择附件，在此处选择 XY-平面（基准平面），如下图所示。

![](/images/week02/w02-freecad-3-2.png)

> 创建草图时需要选择基准平面，此项目我们选择 XY-平面

今日草图工作模式后，顶部菜单栏变成点线、约束等相关工具，我们选择——六边形工具，如下图所示。

![](/images/week02/w02-freecad-3-3.png)

> 选择六边形工具

选中六边形工具后，在坐标中心点击以确定六边形的中心点，然后向外拉伸出一个六边形，我这里让六边形的 2 个点落在水平 x 轴上。再次点击固定六边形大小。此时可以在“约束”面板看到自动添加了很多约束，以保证这是一个等边六边形。求解器信息则显示约束不足：1 个自由度，如下图所示。此时如果你推拉六边形的角点，还是可以改变六边形的大小和角度。

![](/images/week02/w02-freecad-3-4-1.png)

### 添加尺寸约束

点击按 `esc`键或鼠标右键退出六边形工具，如下图所示按 1-4 的顺序添加水平距离约束。

1. 先点击落在 x 轴的 1 号点，被点中的点会变成绿色（有点难以察觉）。
2. 再点击原点。
3. 再打开顶部标注尺寸菜单，选择“水平距离约束”命令。
4. 在弹出的 “插入长度”窗口中，输出长度为 70mm。

![画板](/images/week02/w02-freecad-3-4.png)

> 为六边形的 1 个点设置水平距离约束需要经历 4 步操作

点击 OK 按钮确认后，如下图所示，六边形上的点和原点会出现一个红色的 70mm 的标注线，另外六边形也由开始的白色变成了绿色，左侧的求解器信息显示为绿色的：完全约束状态。

![](/images/week02/w02-freecad-3-6.png)

> 为六边形成功添加约束至“完全约束”状态

`<font style="color:rgb(64, 64, 64);">`用相同的方法绘制内壁六边形线条，添加水平约束为 67mm（这里没有使用 Offset geometry，因为那样需要手动去约束新六边形的 6 个点），现在我们得到两个嵌套的完全约束的六边形，如下图所示。`</font>`

![](/images/week02/w02-freecad-3-8.png)

> 两个嵌套的六边形代表灯笼的内外壁

### 拉伸成型

在上图草图模式下点击任务面板的“Close”按钮后，回到实体模式。如下图所示，保持刚才绘制的草图的选中状态，点击手指指示的![](/images/week02/freeCAD-PartDesign_Pad.svg)“凸台”（[Pad](https://wiki.freecad.org/PartDesign_Pad)）功能。

![画板](/images/week02/w02-freecad-3-8-2.png)

> 在实体模式下选中草图使用凸凹（[Pad](https://wiki.freecad.org/PartDesign_Pad)）功能。

在凸台参数面板设置长度为 240mm，如下图所示。现在我们得到了一个中空的六棱柱，然后点击任务面板的“OK”按钮以确定设定。

![](/images/week02/w02-freecad-3-9.png)

> 凸台（Pad）参数设置

现在模型资源列表在 Sketch 上多了一个 Pad，如下图所示，我们可以双击 Pad 再次打开凸台面板重新设置长度。

![](/images/week02/w02-freecad-3-10.png)

> 模型资源列表增加了 Pad 内容

### 在侧面添加镂空图案

接下来我们需要在 6 个侧面挖出镂空的部分，我对一面的镂空区域做了简单规划，如下图所示，黑色的部分代表要镂空的部分。

![](/images/week02/w02-freecad-3-11.png)

> 对要挖空的内容做了规划，并标注了尺寸

接下来我们需要在棱柱的面上创建新草图，如下图所示，点选一个外立面高亮（1），然后点击草图按钮（2）。

![画板](/images/week02/w02-freecad-3-12.png)

> 选择六棱柱的一个面然后再点选

现在，可以在选中的面上绘制草图，如下图所示。

![](/images/week02/w02-freecad-3-13.png)

> 进入六棱柱 1 个外立面的草图模式

依据上面规划的镂空尺寸，绘制形状并适当添加约束，如下图所示。

![](/images/week02/w02-freecad-3-14.png)

> 绘制镂空区域并适当约束

点击任务面板的“Close”按钮，回到实体工作区，点击下图所示的![](/images/week02/freecad-PartDesign_Pocket.svg)“凹坑”（[Pocket](https://wiki.freecad.org/PartDesign_Pocket)）功能。

![画板](/images/week02/w02-freecad-3-16.png)

> 在实体工作区选择凹坑功能

在凹槽参数面板设置一个高于六棱柱外壳厚度的数值（这里是 5mm），就可以看到镂空效果如下图所示。

![](/images/week02/w02-freecad-3-15.png)

> 在凹槽参数面板设定凹槽长度数值

按 OK 按钮确认后回到实体工作区，在模型资源列表可以看到多了 Pocket 内容，如下图所示。

![](/images/week02/w02-freecad-3-17.png)

> 模型资源列表多了 Pocket 内容

可以回到 Pocket 下的 Sketch001，复制所有草图内容，并为其他 5 个面复制相同的草图后使用“凹坑”功能，就得到了如下图所示的镂空效果。

![](/images/week02/w02-freecad-3-18.png)

### 添加上下横箍

首先进入 Sketch001 草图，复制 2 个嵌套的六边形的所有内容。然后在模型资源管理器在 Body 下创建一个新的草图，还是选择 xy 轴，黏贴草图，修改内壁六边形的约束长度为 80mm，如下图所示。

![](/images/week02/w02-freecad-3-19.png)

> 在新创建的草图中黏贴嵌套六边形，修改内壁六边形的约束长度为 80mm

将 Sketch007 草图的 z 轴位置设置为 20mm，将草图平面提升到如下图所示的位置。

![](/images/week02/w02-freecad-3-20.png)

> 修改新简历的 Sketch007 草图的 z 轴位置

用凸台工具，并设置 5mm 的长度，就得到了下边横箍，如下图所示。

![](/images/week02/w02-freecad-3-21.png)

> 用凸台工具建立下横箍

用同样的方式在 215mm 处建立上横箍，初步完成智幻灯笼外轮廓的建模工作，保存文件。

![](/images/week02/w02-freecad-3-22.png)

> 用同样的方式在 z 轴 215mm 处增加上横箍

### 获取 FreeCAD 源文件

[Lantern shell.FCStd](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/images/week02/Lantern_shell.zip)

## 为适合激光切割建模
上面的练习得到的模型是个整体，考虑到如果使用激光切割机来获得这个外壳，就需要改变设计方案， FreeCAD 虽然免费，但对新手来说操作很多地方并不友好。为此我尝试了几个不同的 CAD 软件。

首先试了试了 [Shapr3D](https://www.shapr3d.com/)。这个软件是我用过最趁手的 CAD 了。交互非常友好，在完成了软件自带的入门教程后，我很快就做出了我的灯笼的外壳模型，如下图所示。但发现导出平面草图需要 Pro 版本，我就决定再换换其他的。

![](/images/week02/w02-3-1-sharp3D.jpg)

> Shapr3D 软件制作的灯笼外壳结构，以适应切割方案
>

Fab Academy 为学生提供了 [Autodesk Fusion](https://www.autodesk.com/products/fusion-360) 的体验账户，所以我又转向 Fusion，鉴于之前对参数化设计的过程的熟悉，所以这个过程也非常快，我使用了 2 个草图分别来构建灯笼的垂直结构和水平箍结构，在垂直结构的每个单元上下各添加了一个 3 x15mm 的孔，用来固定水平箍和为了给中间的设备做预留。完成结构设计后，我在用 Fusion 的草图右键菜单的 “导出 DXF”功能，分别导出了垂直结构和水平箍结构的草图的 DXF 文件。

![](/images/week02/w02-3-2-fusion-2.jpg)

> Autodesk Fusion 软件制作的灯笼外壳结构，以适应切割方案，Fusion 可以直接导出草图为 DXF
>

在 Adobe Illustrator 将 2 个 DXF 文件合并为 1 个，并复制所需数量为后续切割做准备。

![](/images/week02/w02-3-3-AI.png)

> 在 Adobe Illustrator 中为激光切割编辑所需的 DXF 文件
>

#### 获取切割用的 DXF 源文件

[RL-1-line.dxf](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/images/week02/RL-1-line.dxf)