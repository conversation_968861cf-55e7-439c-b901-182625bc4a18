---
layout: doc
title: 第2周：像素图到矢量图的转换 | 冯磊 Fab Academy 2025
description: Fab Academy 2025 第二周 像素图到矢量图转换工具实践与工作流程
head:
  - - meta
    - name: keywords
      content: fab academy, 像素图, 矢量图, Potrace, ImageMagick, Inkscape, 图像转换
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第2周：计算机辅助设计'
  link: '/zh/assignments/week02/week02_computer_aided_design_cn'
next:
  text: '第2周：3D 建模智幻灯笼'
  link: '/zh/assignments/week02/week02_3d_modeling_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 第2周：计算机辅助设计——像素图到矢量图的转换

## 项目概述
对于我的最终项目——智幻走马灯，中间的旋转部分，我想使用我喜欢的张大千临摹的敦煌壁画《[晚唐乐器二身佛](https://week02_computer_aided_design_cn.md)》作为素材。画面中两个飞天造型的佛像，我考虑可以打印在半透光的纸上，做成滚筒形状，随着滚筒旋转，可以看到飞舞的乐器二身佛。

![](/images/week02/w02-1-1.jpg)

> 张大千临摹的敦煌壁画《晚唐乐器二身佛》
>

![画板](/images/week02/w02-1-2.jpg)

> 两个飞天造型的佛像，很适合作为智幻走马灯的旋转图案
>

这个练习我想完成下面的目标：

1. 图片去背景：我希望图像只保留菩萨的内容，背景为透明，这样我就可以把他们打印在用作旋转滚筒的任意半透明纸张或透明薄膜上。
2. 转为黑白线稿：从彩色图像提取黑白线稿。
3. 黑白线稿矢量化：将黑白线稿矢量化，我想为后面激光切割课程准备素材，想试试用激光刻印的效果。

这个过程不仅能帮助我们理解不同图像格式的特点，也为后续的数字制造工艺奠定基础。

## 基础概念
图像与图像是计算机辅助设计的基础，我们设计意图和设计过程，都需要借助图形图像来表达。在开始实际的图像转换工作之前，我们需要理解两种基本的数字图像表示方式：像素图（栅格图像）和矢量图。这两种格式各有特点，在数字设计中扮演着不同的角色。

### 像素图（Raster Images）
像素图，也称为位图或栅格图像，是由像素点阵列组成的图像。每个像素都包含特定的颜色信息，共同构成完整的图像。

像素图的特点：

+ 分辨率依赖：放大时会失真
+ 文件较大：需要存储每个像素的信息
+ 适用于：照片等连续色调的图像
+ 常见格式：JPG、PNG、BMP等

![画板](/images/week02/w02-1-3.jpg)

> 像素图如果放的足够大，就可以看到一个个由色块组成的小方格，图片也开始变得模糊
>

### 矢量图（Vector Graphics）
矢量图是基于数学方程式描述的图形，由点、线、曲线和形状等几何元素组成。

矢量图的特点：

+ 分辨率独立：可无损缩放
+ 文件较小：仅存储图形元素的数学描述
+ 适用于：标志、图标、技术图纸等
+ 常见格式：SVG、AI、EPS等

![画板](/images/week02/w02-1-4.png)

> 经过矢量化后的 SVG 图像，理论上可以“无限”放大
>

:::tips
在数字制造中，正确选择和转换图像格式直接影响到最终的制作质量。例如，激光切割通常需要清晰的矢量路径，而 3D 打印的材质贴图则适合使用像素图。了解这些基础概念有助于我们在后续的数字制造过程中做出正确的技术选择。

:::

## 1. 图片去背景
首先，我从 Google Arts & Culture 平台获取了高分辨率的壁画图像（[https://g.co/arts/vR6XPac9VrxZzyzX8](https://g.co/arts/SrV88ckuJmzNYWGG8)）的截图，以 PNG 格式保存，图像尺寸 2770x1372，约 4.5MB 大小。

图像处理我使用自己熟悉的 Photoshop（PS）。在 PS 里打开壁画图像后，PS 以悬浮窗的形式提供了一个移除背景的功能。

![](/images/week02/w02-1-5.jpg)

> 在 PS 里打开壁画图像
>

点击“移除背景”按钮后，可以看到有大部分背景被去掉了，但还有些错误，有些该被去掉的被保留，有些需要被保留的被去掉了。

![](/images/week02/w02-1-6.jpg)

> 使用移除背景功能后图像的效果，可以看到出现了一些错误
>

注意右侧图层添加了一个蒙版，如下图所示。蒙版可以理解为一个黑白图像，黑色部分就代表被隐藏，白色部分代表被保留。

![](/images/week02/w02-1-7.png)

> PS 在使用移除背景后，为图像添加了一个蒙版，这个蒙版可以理解为一个灰度图像，黑色表示要移除的区域，白色为要显示的区域，中间灰度为透明度
>

我们可以通过下方的工具，以及快速选择工具、画笔工具等编辑这个黑白蒙版，来修正那些需要被隐藏或显示的内容。另外，可以使用键盘的“x”键在“从蒙版中去除”或“添加到蒙版”功能之间快速切换；还可以使用键盘的“[”与“]”按键来控制画笔粗细，以方便进行精确的细节修描。

经过修描蒙版后，现在看上去是期望的去背景效果，保存 ps 文件。

![](/images/week02/w02-1-8.jpg)

然后使用 PS 的导出功能，可以在预览看到，如果保存为 png 有 2.5MB，因为这张图颜色数较少，如果想进一步减少文件尺寸，可以勾选“较小文件（8 位）”，这样图像使用的颜色会被控制在 256 色范围内，使用此模式可以将图片大小降低到 598KB。

![画板](/images/week02/w02-1-9.jpg)

现在，成功获得了一张去了背景的 PNG 格式的透明背景图像。

![](/images/week02/week02-1-6-8.png)

## 2. 转为黑白线稿
+ **复制并去色：**在 PS 中继续，在图层面板将原彩色图层复制一个图层（快捷键 Ctrl+J，mac 为 command+J），然后对复制后的图层去色，可以使用菜单栏的 图像>调整>去色 命令（快捷键 Ctrl+Shift+U，mac 为 command+Shift+U），效果如下图所示：

![](/images/week02/w02-1-10.jpg)

> 添加了灰度调整层后的图像效果。
>

+ 复制并反向：在 PS 中继续复制刚才的去色图层，然后进行反向可以使用菜单栏的 图像>调整>反向（快捷键 Ctrl+I，mac 为 command+I），效果如下图所示：

![](/images/week02/w02-1-11.jpg)

> 反色后的图像效果
>

+ 颜色减淡：在 PS 中将反色后的图层的混合模式改为颜色减淡，这时图像会变成白色，如下图所示。

![](/images/week02/week02-1-7-3.png)

> 设置反色图像的混合模式为“颜色减淡”。
>

+ 使用“最小值”滤镜：使用菜单栏的 滤镜>其他>最小值 ，通常半径数值设置为 1 就足够了。效果如下图所示。

![](/images/week02/w02-1-13.jpg)

> 使用最小值滤镜后的效果
>

+ 盖印图层：选中拷贝的 2 个图层，用快捷键 Ctrl+Alt+E（mac command+Alt+E）盖印所选图层，然后将原来的图层编组然后关闭可见性，图层面板状态如下图所示。

![](/images/week02/w02-1-14.jpg)

> 盖印图层后的图层状态
>

+ 根据色彩范围选择线条：在合并后的图层上，使用菜单的 选择>色彩范围 工具，吸取线条颜色色，然后调整颜色容差。这时可以打开选取预览，设置“快速蒙版”，红色区域就是要抹掉的部分。确保必要的线条部分被保留后确定。

![](/images/week02/w02-1-15.jpg)

> 调整颜色范围，用吸管工具加减线条和背景，并调整颜色容差，让需要的线条范围被保留下来
>

对获得的线条选区建立蒙版，现在图片只有线条部分的内容了。

![](/images/week02/w02-1-16.jpg)

> 对获得的线条选区建立蒙版，现在图像只剩下线条
>

+ 让线条更清晰：对图层（注意不是蒙版）使用菜单的 图像>调整>色相/饱和度 命令（快捷键 Ctrl+U，mac command+U），打开色相/饱和度面板，将明度滑块拉到最左侧（-100）的位置，现在可以看到清晰的线条效果了，如下图所示。

![](/images/week02/w02-1-17.jpg)

> 色相/饱和度面板下，将明度设置为-100 的效果。
>

+ 导出线稿图像：现在可以将清晰的线稿导出为 PNG 格式，将此图像置于白色背景下的效果如下图所示。

![画板](/images/week02/week02-1-7-8-line.png)

> 导出干净的线稿图像效果
>

## 3. 黑白线稿矢量化
我使用 Adobe Illustrator（AI）将线稿矢量化。导入线稿 png 图片后，对图片使用菜单栏的 对象>图像描摹 功能，很快就能得到矢量图像，如下图所示。

![](/images/week02/w02-1-18.jpg)

> AI 中经过描摹后的图像
>

放大后可以看到矢量化后的细节。

![](/images/week02/w02-1-19.png)

> 经过矢量化的线稿图像
>

将描摹后的图像导出为 SVG 格式，与彩色初稿对比。

![](/images/week02/w02-line-outsvg.svg)

![](/images/week02/w02-1-1.jpg)

> 矢量化线稿与彩色原稿对比
>

## 获取文件
[去底的彩色 png 图像](/images/week02/week02-1-6-8.png)

[黑白线稿 PNG 图像](/images/week02/week02-1-7-8-line.png)

[矢量化后的 SVG 图像](/images/week02/w02-line-outsvg.svg)

## 图像与视频压缩
在数字制造过程中，图像和视频的优化对于网页发布和项目展示至关重要。大尺寸的图像和视频不仅会增加网页加载时间，还会占用大量存储空间。本节将介绍如何使用命令行工具 ImageMagick 和 ffmpeg 来优化图像和视频文件。

### 使用 ImageMagick 批量处理图像
[ImageMagick](https://imagemagick.org/) 是一款强大的开源命令行图像处理工具，支持超过 200 种图像格式的读取、写入和转换。

#### 安装 ImageMagick
+ **Windows**: 从[官方网站](https://imagemagick.org/script/download.php)下载安装程序
+ **macOS**: 使用 Homebrew 安装 `brew install imagemagick`
+ **Linux**: 使用包管理器安装 `sudo apt-get install imagemagick`

#### 基本图像压缩
以下是一些常用的 ImageMagick 命令，用于优化我们在前面步骤中处理过的图像：

```bash
# 将 PNG 转换为优化的 JPG (质量为 85%)
convert week02-1-7-8-line.png -quality 85% line-optimized.jpg

# 调整图像大小到指定宽度，保持宽高比
convert week02-1-6-8.png -resize 1200x line-resized.png

# 批量处理文件夹中的所有 PNG 图像
mogrify -resize 1200x -quality 85% -format jpg *.png
```

在我的项目中，我使用以下命令将所有处理过的图像优化到适合网页显示的尺寸和文件大小：

```bash
# 优化我的矢量和位图图像
convert week02-1-6-8.png -resize 1200x -strip -quality 85% web-color-image.jpg
convert week02-1-7-8-line.png -resize 1200x -strip -quality 90% web-line-image.jpg
```

#### 优化效果图展示
一个优化前后的文件大小如下图所示：

+ 原彩色 PNG 文件（2400x1804）`ZhangDaqian1.png`: 5.9M → 优化后 JPG（1200x902）: 342KB

文件尺寸差异巨大。

![](/images/week02/week02-2-1.jpg)

> 一张 png 图像优化为 jpg 图像的文件大小对比
>

#### 高级图像批处理
对于需要在网页上展示的多张图像，我创建了一个批处理脚本：

```bash
#!/bin/bash
# 为博客创建缩略图和优化图像

mkdir -p web-images

for img in *.png; do
  # 创建缩略图 (300px 宽)
  convert "$img" -resize 300x -quality 85% "web-images/thumb-$img.jpg"
  
  # 创建网页优化版 (1200px 宽)
  convert "$img" -resize 1200x -strip -quality 85% "web-images/web-$img.jpg"
done

echo "处理完成! 文件保存在 web-images 文件夹中"
```

### 使用 ffmpeg 处理视频
[ffmpeg](https://ffmpeg.org/) 是处理视频的最佳命令行工具之一，我用 AI 工具 [https://jimeng.jianying.com/ai-tool/image/generate](https://jimeng.jianying.com/ai-tool/image/generate) ，利用 png 优化为 jpg 的测试图像生成了一段 5 秒的视频。

![](/images/week02/week02-2-2.jpg)
> 即梦 AI 工具可以上传图片并添加提示词的方式生成视频
> 

下载后可以看到这段 5 秒的视频，分辨率为 1280×960，大概 4.4 MB。

![](/images/week02/week02-2-3.jpg)
> 分辨率为 1280×960 的 mp4 视频，大概 4.4 MB
> 


#### 安装 ffmpeg
+ **Windows**: 从[官方网站](https://ffmpeg.org/download.html)下载
+ **macOS**: 使用 Homebrew 安装 `brew install ffmpeg`
+ **Linux**: 使用包管理器安装 `sudo apt-get install ffmpeg`

#### 基本视频压缩
以下是使用 ffmpeg 压缩视频的基本命令：

```bash
# 将视频转换为 H.264 编码，适合网页发布
ffmpeg -i input_video.mp4 -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 128k output_video.mp4

# 调整视频分辨率到 720p
ffmpeg -i input_video.mp4 -vf "scale=-1:720" -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 128k output_720p.mp4
```

对于我的这个视频，我使用以下命令进行优化：

```bash
# 优化演示视频，设置分辨率为 854:640
ffmpeg -i ft1.mp4 -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 128k -vf "scale=854:640" ft1_web.mp4
```

优化前后的文件大小对比：

+ 原始视频: 4.4MB (1280 × 960, 5秒)
+ 优化后视频: 1MB (854 × 640", 5秒)

![](/images/week02/week02-2-4.png)

#### 高级视频处理
为了更好地展示视频的效果，我还使用 ffmpeg 创建了一个 GIF 动画：

```bash
# 从视频中提取 5 秒钟创建一个 GIF
ffmpeg -ss 00:00:00 -i ft1_web.mp4 -t 5 -vf "fps=6,scale=320:-1:flags=lanczos,split[s0][s1];[s0]palettegen=max_colors=128[p];[s1][p]paletteuse=dither=bayer:bayer_scale=5" ft1_web.gif
```

这个 Gif 只有 1.1 MB。

![](/images/week02/ft1_web.gif)

> 将视频输出为 gif 图像 
>
