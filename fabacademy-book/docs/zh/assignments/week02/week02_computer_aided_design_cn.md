---
layout: doc
title: 第二周：计算机辅助设计 | 冯磊 Fab Academy 2025
description: Fab Academy 2025 第二周 计算机辅助设计(CAD)工具与方法学习指南
head:
  - - meta
    - name: keywords
      content: fab academy, CAD, FreeCAD, Blender, Inkscape, 参数化设计, 3D建模
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第一周：VitePress 网站开发与部署指南'
  link: '/zh/assignments/week01/week01_web_cn'
next:
  text: '第二周：像素图到矢量图的转换'
  link: '/zh/assignments/week02/week02_raster_to_vector_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第2周：计算机辅助设计

## 概述

作为 Fab Academy 2025 课程的第二周，核心主题聚焦于计算机辅助设计（Computer-Aided Design, CAD）。Neil 这一课的内容旨在帮助学生掌握从基础到进阶的数字设计工具与方法，为后续的物理制造与项目开发奠定技术基础。课程强调设计工具的多样性、参数化思维以及跨平台协作的重要性，同时贯穿开源精神与知识共享的核心理念。

---

### 课程重点

1. 2D 设计工具与流程
    - 栅格图像处理：学习使用 [GIMP](https://www.gimp.org/)、[ImageMagick](https://imagemagick.org/index.php) 等工具进行图像编辑、批量压缩与格式转换，理解像素（raster）的基本原理。
    - 矢量图形设计：通过 [Inkscape](https://inkscape.org/) 掌握布尔运算（Booleans）、克隆（clones）与约束（constraints）操作，并探索 [Potrace](https://potrace.sourceforge.net/) 等工具将栅格图像转换为矢量路径。
    - 参数化设计入门：在 [FreeCAD](https://www.freecad.org/) 中实践约束驱动的草图设计，确保设计可随参数（如材料厚度）自动调整。
2. 3D 建模与进阶技巧
    - 建模范式：学习边界表示（BRep）、函数表示（FRep）、体素（VRep）等几何建模方法，理解其适用场景与局限性。
    - 核心操作：通过 FreeCAD、Fusion 360 等工具实践拉伸（extrude）、旋转（revolve）、放样（loft）、扫掠（sweep）等建模技术，掌握布尔运算（CSG）、对称性（symmetry）与装配（assemblies）设计。
    - 参数化与编程：利用电子表格变量与 Python 脚本实现参数化设计，并探索 [OpenSCAD](https://openscad.org/) 等基于代码的建模工具。
3. 渲染、动画与仿真
    - 视觉呈现：使用 [Blender](https://www.blender.org/features/) 进行高质量渲染与动画制作，结合实时渲染引擎（如 Eevee）生成动态演示。
    - 物理仿真：通过 Blender 的物理引擎模拟机械运动、流体与柔性体行为，为设计验证提供支持。
4. 文件格式与协作
    - 格式选择：优先使用 STEP、SVG 等通用格式进行跨工具协作，避免 DXF、STL 等易丢失设计信息的格式。
    - 开源资源：利用 [McMaster-Carr](https://www.mcmaster.com/)、[FreeCAD 库](https://github.com/FreeCAD/FreeCAD-library)等平台获取标准化零件模型，提升设计效率。
5. AI 与未来工具
    - 初步探索 AI 辅助设计（如文本生成 CAD），了解其潜力与当前局限性（如物理合理性不足）。

---

### 核心工具

+ 开源工具：FreeCAD（参数化建模）、Blender（渲染/动画）、Inkscape（矢量编辑）、GIMP（图像处理）。
+ 商业工具：Fusion 360（全流程集成）、SolidWorks（工业级设计）、Onshape（云端协作）。
+ 协作平台：Git（版本控制）、GrabCAD（模型共享）、OBS Studio（录屏与直播）。

---

### 作业要求

本周需完成以下任务：

1. 多维度建模：使用至少 3 种工具（如栅格、矢量、3D CAD）对可能的期末项目进行设计，涵盖 2D 草图、3D 模型、渲染图及动画演示。
2. 文件优化：通过 ImageMagick、ffmpeg 等工具压缩图像与视频，确保符合网页发布标准（推荐 H.264 编码）。
3. 文档提交：在课程页面发布设计说明，并附上原始设计文件（如 .FCStd、.blend），体现开源共享精神。

---

### 开源与协作

课程延续 Fab Academy 的开放文化，鼓励学生使用 FreeCAD、Blender 等开源工具，同时将设计文件与过程公开分享。通过参与全球创客社区的协作，推动技术经验的积累与创新。

---

### 学习资源

+ 学习文档索引链接：[http://academy.cba.mit.edu/classes/computer_design/index.html](http://academy.cba.mit.edu/classes/computer_design/index.html)

