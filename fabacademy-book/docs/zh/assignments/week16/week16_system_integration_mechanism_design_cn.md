---
layout: doc
title: "第16周个人作业：系统集成运动机构设计 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第16周个人作业，详细记录智幻走马灯运动机构的设计与实现过程。"
head:
  - - meta
    - name: keywords
      content: fab academy, 系统集成, 运动机构, 走马灯, 3D打印, 电机驱动, 个人作业
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第16周：系统集成'
  link: '/zh/assignments/week16/week16_system_integration_cn'
next:
  text: '第16周个人作业：系统集成设计'
  link: '/zh/assignments/week16/week16_system_integration_design_cn'
lastUpdated: true
editLink: true
---

# 智幻走马灯运动机构设计

按最初的规划，走马灯的部分需要用电机驱动，为此我观察了很多 3D 走马灯的结构设计方案后，决定自己尝试做一个运动机构设计。

## 灯笼内部支持与转笼结构设计
在完成第 3 周的激光切割作业后，我就拿到了由薄木板拼接而成的灯笼的外部结构，现在要在这个基础上设计一个可以旋转的灯笼内部结构。

![](/images/week03/w03-p3-7.jpg)

> 组装好的灯笼外壳感觉很坚固的样子
>

### 底部支持结构
我在第 3 周的 Fusion 文件基础上，首先设计了一个底部支持结构，并用 3D 打印机输出了一版，结果发现尺寸过大（我忽略了 6 片围栏的厚度），导致完全无法放入底部框架。

![](/images/week16/w16-p1-1.jpg)

> 第一版设计失败，尺寸过大，导致无法安装
>

第 2 次我预留了外侧栏的厚度，重新调整了设计，Fusion 的设计效果如下图所示。

![](/images/week16/w16-p1-2.jpg)

> 修改后第 2 版的底部支撑结构设计
>

再次用 3D 打印输出了设计，这次感觉正合适。

![](/images/week16/w16-p1-3.jpg)

> 第 2 次的底部支撑结构的设计感觉正合适
>

### 灯笼转笼的设计
灯笼底部我在中轴使用了一个圆锥状结构，可以插入底部支持结构的柱状圆环中，这样转笼就能够方便的转动。通过 6 根灯转笼柱，彼此固定的结构，

![](/images/week16/w16-p1-4.png)

![](/images/week16/w16-p1-5.jpg)

> 转笼底部分为 2 部分，带圆锥的黄色底盘和蓝色的外框，彼此嵌套后，可以锁紧 6 根转笼立柱
>

转笼上部分我用了相同尺寸的蓝色外框，并在上盖板中心开孔，并做了一个带齿轮的轴，中间的孔洞我打算作为穿灯条的孔。

![](/images/week16/w16-p1-6.jpg)

> 转笼的上盖板开孔并做了带齿轮的轴
>

整个结构设计好后，我输出 STL 并用 3D 打印出这 10 个零件进行组装，结果发现上下盖板和 6 根立柱的间隙预留的过大（多了 1mm），导致立柱和上下盖板无法彼此支撑固定，整个转笼倾斜，以至于放到底部支撑板时都无法旋转。

![](/images/week16/w16-p1-7.jpg)

> 转笼上下盖板和 6 根立柱的间隙过大，导致整个结构松散无法使用
>

我将上下盖板到立柱的间隙扩展了 1mm，入下图所示。

![](/images/week16/w16-p1-8.png)

> 在平面草图模式下，将上下盖板到立柱的间隙扩展了 1mm
>

为了确保设计有效，我打印了一个蓝色的下盖板进行测试，发现这个修改能让立柱得到很好的固定（下图左），确定有效后，重新输出打印了上下盖板，现在转笼结构能够很好的支撑。

![](/images/week16/w16-p1-9.jpg)

> 先试打一个盖板，测试缩小间隙对固定 6 个立柱有效（左图），然后重新输出了上下盖板得到支撑结构良好的转笼（右图）
>

## 上部齿轮舱设计
看了一些齿轮设计的资料，初步确定了我的项目的齿轮模数，这样其他齿轮使用相同模数就能确保顺利咬合。

### 模数计算
在齿轮设计中，模数（module）是齿轮尺寸的一个关键参数，定义为齿距（齿与齿之间的距离）与圆周率（π）的比值，单位通常为毫米。模数的计算公式为：

**模数 m = d / z**

其中：

+ **d**：齿轮的分度圆直径（mm）
+ **z**：齿轮的齿数

齿顶圆直径：da = m × (z + 2)

我初步规划了一个走马灯中轴上的传动齿轮方案，齿顶圆直径（da）设置为 20mm，我计划将齿数（z）设置为 16。

![](/images/week16/w16-p1-10.png)

> 转笼中轴的齿轮平面草图
>

**<font style="color:rgb(0, 0, 0) !important;">求解模数</font>**<font style="color:rgba(0, 0, 0, 0.85) !important;">：计算可得 </font>_<font style="color:rgba(0, 0, 0, 0.85) !important;">m</font>_<font style="color:rgba(0, 0, 0, 0.85) !important;">=20÷(16+2)≈1.11</font>_<font style="color:rgba(0, 0, 0, 0.85) !important;">mm</font>_<font style="color:rgba(0, 0, 0, 0.85) !important;">。</font>

<font style="color:rgba(0, 0, 0, 0.85) !important;">然后安装了一个 Fusion 免费的齿轮插件：</font>[GF Gear Generator](https://apps.autodesk.com/FUSION/en/Detail/Index?id=1236778940008086660&appLang=en&os=Win64)

安装后，在实用程序栏可以看到这个插件，现在就可以方便的创建齿轮了。

![](/images/week16/w16-p1-11.png)

> 安装 GF Gear Generator 插件后，会在实用程序多出一个图标
>

点击 GF Gear Generator 工具，会弹出一个设计各种类型齿轮的菜单。我只需要正齿轮（Spur Gear）。

![](/images/week16/w16-p1-12.png)

> GF Gear Generator 工具可以选择多种不同的齿轮类型
>

在正齿轮的窗口，我输入上面测试得到的模数 1.11mm，然后输入齿数 16，设置齿轮厚度 为 4mm，压力角（Pressure angle）使用默认值 14.5 度。确认后就得到了图示的齿轮结构。

![](/images/week16/w16-p1-13.png)

> 创建正齿轮
>

我初步设计了齿轮舱，并打算使用 3D 打印的轴来固定齿轮，如下图所示。

![](/images/week16/w16-p1-14.png)

> 初步设计的齿轮舱，打算用 3D 打印的轴来固定齿轮
>

但打印好后发现 PLC 材料的强度很有限，用很小的力气都能让脆弱的轴折断，如下图所示，这会导致整个结构报废。所以我考虑使用 4mm 直径的钢轴来承载这些传动齿轮，而且对我的项目来说只需要 2cm 长度，有 2 根就够了。

![](/images/week16/w16-p1-15.jpg)

在淘宝看了很多，发现这种小钢轴的最低起售数量是 100 个，而且价格极其便宜，我支付了 4.22 RMB，就拿到了 100 个，多余的留在柴火空间给需要的人用吧。

![](/images/week16/w16-p1-16.png)

![](/images/week16/w16-p1-17.jpg)

> 100 个 4mm 直径，2cm 长的钢轴，算邮费只需 4.22 元 RMB，有点难以置信
>

为了找到适合的孔径，我设计并打印了 3 个不同孔径的齿轮，分别是直径 4mm、4.2mm 和 4.25mm。结果发现：

+ 4mm 的钢轴完全无法插入；
+ 4.2mm 可以较为轻松的插入，但转动齿轮会需要稍微用点力量，适合作为钢轴底座的轴孔，让钢轴能插入底座但又不至于太松动；
+ 4.25mm 的感觉正好，能很轻松的插入，且齿轮能很顺畅的转动，所以这个尺寸我就用作齿轮的轴孔。

![](/images/week16/w16-p1-18.jpg)

> 3D 打印测试不同的齿轮孔径在 4mm 钢轴的效果
>

尝试将几个齿轮摆放在合适的位置，说实话，我在这方面还谈不上什么经验，就是觉得这样应该差不多能行。

![](/images/week16/w16-p1-19.png)

> 通过顶部视图调整几个齿轮的位置
> 

经过一番尝试后，初步完成了齿轮舱和齿轮结构的设计，齿轮舱的盒装结构是用来放置电机的，如下图所示。

![](/images/week16/w16-p1-20.jpg)

> 初步完成的齿轮舱和齿轮结构的设计
>

3D 打印齿轮舱和齿轮，并装上了钢轴后的效果。

![](/images/week16/w16-p1-21.jpg)

尝试转动灯笼转笼，感觉效果不错，转笼和齿轮都能顺畅工作，见下面的视频。

<video width="100%" controls>
  <source src="/images/week16/RL-ok.mp4" type="video/mp4">
  您的浏览器不支持视频标签。
</video>

购买了拓竹小火车模型组件：[https://detail.tmall.com/item.htm?from=detail&id=************&skuId=5589242529650&spm=tbpc.orderdetail.suborder_itemtitle.1.5ae06aa6YUIz6Q](https://detail.tmall.com/item.htm?from=detail&id=************&skuId=5589242529650&spm=tbpc.orderdetail.suborder_itemtitle.1.5ae06aa6YUIz6Q)，找到这个双侧出轴电机：

N20-10D双侧出轴电机-130rpm，售价约 RMB ¥30.02

![](/images/week16/w16-p1-22.jpg)

产品参数

| 产品 ID | LA009 |
| --- | --- |
| 输入端 | SH1.0-2P |
| 额定电压 | 3V |
| 工作电压范围 | 2V ~ 4V |
| 堵转扭矩 | 350 |
| 空载转速（RPM） | 130 |
| 电机转向 | CCW |
| 出轴类型 | 侧向双出D型轴 |

接下来，我准备在齿轮舱上增加一层电子设备层，重新设计一个圆形的 PCB。