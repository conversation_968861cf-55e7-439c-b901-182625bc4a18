---
layout: doc
title: "第16周：系统集成 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第16周系统集成课程，介绍如何将各个独立组件（如机械部件、电子元件、软件等）组合成一个协调工作的整体系统。"
head:
  - - meta
    - name: keywords
      content: fab academy, 系统集成, 项目管理, 时间规划, 组件整合, 测试验证, 个人作业
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第15周个人作业：接口与应用程序编程'
  link: '/zh/assignments/week15/week15_interface_application_programming_personal_cn'
next:
  text: '第16周个人作业：系统集成运动机构设计'
  link: '/zh/assignments/week16/week16_system_integration_mechanism_design_cn'
lastUpdated: true
editLink: true
---

# Fab Academy 2025 - 系统集成课程讲义
## 课程概要
本周课程将深入探讨系统集成的重要概念，这是设计和制造完整功能原型的关键步骤。系统集成涉及将各个独立组件（如机械部件、电子元件、软件等）组合成一个协调工作的整体系统。在这一阶段，我们将学习如何整合之前各周所学的技能，确保所有组件能够顺利协同工作。本课程特别重要，因为它直接关系到最终项目的成功实现，并为最终项目展示做准备。

## 详细的课程内容介绍
### 1. 课程进度与重要日期提醒
#### 课程时间线回顾
+ **当前日期**：5月7日
+ **剩余四周时间**：所有每周作业必须在四周内（6月2-6日）完成并提交
+ **五周后**：最终项目展示（6月9-13日）
+ **七周后**：Fab25大会（7月4-11日，捷克）

#### 重要提示
+ 最终项目展示的报名将于5月14日开放
+ 本周的系统集成作业是确定你是否准备好进行最终项目展示的重要指标
+ 如果你的作业完成度达到75%，并且已经展示了最终项目，但仍有一些工作需要完成，你可以获得有条件毕业资格

### 2. 项目管理与时间规划
在仅剩四周的紧张时间里，完成所有作业需要高效的时间管理：

+ **供需管理**：不能仅靠希望完成项目，必须通过科学安排时间
+ **日程安排**：建立详细的日程表，列出每天需要完成的具体任务
+ **按计划工作**：严格按照制定的计划执行，这是Fab Academy最重要的技能之一
+ **三选法则（Triage）**：区分任务的优先级，确保能完成核心功能

### 3. 系统设计与集成理念
#### 设计方法论
+ **概念设计、初步设计与详细设计**的区别
+ **用户体验（UX）与可用性**考虑
+ **制造设计（DFM）**：如何设计能够实际制造的产品

#### 零部件选择与优化
+ 使用标准零部件的好处
+ 近净成形技术应用
+ 柔性结构 vs 紧固件 vs 粘合剂的选择
+ 自对准特性的设计
+ 如何最小化零部件数量

#### 文档记录
+ 设计文件的组织与管理
+ 物料清单（BOM）的制作
+ 生产文档与操作说明的编写
+ 封装与包装考虑

### 4. 系统集成的关键环节
#### 机械集成
+ PCB安装方法
+ 线缆布线技巧
+ 机构对准方法
+ 表面处理工艺

#### 测试与质量控制
+ 质量保证（QA）与质量控制（QC）的区别
+ 常见测试方法：震动测试、老化测试、循环测试、环境测试
+ 模糊测试在系统稳定性验证中的应用

### 5. 常见故障模式与预防
#### 机械故障
+ 超过弹性极限的载荷
+ 应力集中区域的开裂问题（使用圆角过渡）
+ 紧固件松动、振动及螺纹锁紧解决方案
+ 螺纹滑丝问题
+ 错位、卡滞及柔性结构设计
+ 动态不稳定性

#### 线缆与连接故障
+ PCB走线损坏
+ 连接器脱落问题
+ 应变消除设计
+ 极性连接器的应用
+ 绝缘磨损、介电击穿与短路预防
+ 线缆导向与线束管理
+ 连接器腐蚀问题

#### 电子元件故障
+ MOSFET过压、过流、过温保护
+ 感应反冲与反向保护二极管
+ 稳压器反极性问题
+ 处理器过压与GPIO电流源/汇问题
+ 电流限制设计
+ 瞬态保护设计
+ EMI屏蔽技术

#### 电源问题
+ 功率预算与电源匹配
+ 电池寿命考虑
+ 电源电压匹配问题（如48V供电到24V输入）
+ 瞬态噪声与处理器故障
+ 线缆电阻与电感
+ 旁路电容的应用
+ 接地环路问题

#### 软件故障
+ 内存泄漏
+ 缓冲区溢出
+ 竞态条件
+ 变量作用域问题
+ 对象接口设计
+ 依赖关系管理
+ 过度复杂化问题

### 6. 扩展与制造考虑
#### 可扩展性
+ 复杂度增长的大O表示法
+ 复杂性的相变现象

#### 制造相关问题
+ 供应链管理
+ 产能规划

#### 协作开发
+ 数据交换标准
+ 快速失败策略
+ 前馈vs反馈开发模式

### 7. 维修与生命周期
#### 维修设计
+ 抗跌落与震动设计
+ 模块化设计的重要性
+ Widlarize原则（简化设计）

#### 生命周期考虑
+ 维修权设计
+ 拆卸、重用与回收设计
+ 产品寿命终止管理

## 作业要求
本周作业要求设计并记录你的最终项目的系统集成方案。具体包括：

1. **系统设计文档**：创建详细的系统设计图，清晰展示各组件间的连接与交互
2. **集成计划**：制定具体的集成步骤与测试计划
3. **潜在故障分析**：识别可能的故障点并提出预防措施
4. **维修与生命周期考虑**：说明如何进行维护与最终处置

特别提示：完成本周作业是被安排最终项目展示的前提条件。作业质量将直接影响你是否能够在六月的最终展示周进行项目展示。

## 学习资源
以下是深入学习系统集成的推荐资源：

### 在线资源
+ [制造设计(DFM)指南](https://www.disher.com/blog/design-for-manufacturing/)
+ [自对准特性设计](https://mtm.cba.mit.edu/2011/2011_mtm-snap/mtm_snap-lock/index.html)
+ [物料清单(BOM)工具](https://github.com/openscopeproject/InteractiveHtmlBom)
+ [生产自动化文档](https://wiki.freecad.org/OSH_Automated_Documentation)
+ [机构对准指南](https://help.prusa3d.com/guide/4-z-axis-assembly_67704)

### 案例学习
+ [特斯拉超级压铸技术](https://www.theverge.com/2023/9/14/23873345/tesla-gigapress-gigacasting-manufacturing-breakthrough) - 零件最小化的革命性案例
+ [NASA火星气候轨道器度量单位失误](https://www.simscale.com/blog/nasa-mars-climate-orbiter-metric/) - 数据交换标准化的重要性
+ [SpaceX星舰开发](https://www.space.com/every-spacex-starship-explosion-lessons-learned) - 反馈开发模式的成功案例

### 推荐书籍与文章
+ "The Design of Everyday Things" by Don Norman - 关于用户体验设计
+ "Making Things Move" by Dustyn Roberts - 机械设计与集成
+ [CBA论文：应力集中与圆角设计](https://cba.mit.edu/docs/papers/24.01.MIPS.pdf)

### 参考项目示例
+ [系统集成优秀案例](https://archive.fabacademy.org/archives/2016/fablabtorino/students/440/project/P02/B2/electronics.html)

