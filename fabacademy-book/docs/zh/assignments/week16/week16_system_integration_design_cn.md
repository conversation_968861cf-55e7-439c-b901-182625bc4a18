---
layout: doc
title: "第16周个人作业：系统集成设计 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第16周个人作业，详细记录智幻走马灯系统的设计与实现过程。"
head:
  - - meta
    - name: keywords
      content: fab academy, 系统集成, 系统设计, 走马灯, RGB灯带, 手势传感器, Wi-Fi连接, 个人作业
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第16周个人作业：系统集成运动机构设计'
  link: '/zh/assignments/week16/week16_system_integration_mechanism_design_cn'
next:
  text: '第17周：通配符周'
  link: '/zh/assignments/week17/week17_wildcard_week_cn'
lastUpdated: true
editLink: true
---

# 智幻走马灯系统设计

## 方案调整说明
在项目进展过程中，我最近完成了走马灯的旋转灯罩机械结构设计，这促使我对整个系统进行了重新评估和调整。通过对机械结构的实际测试，我发现原有的硬件方案存在以下局限性：

1. **旋转驱动不足**：原计划使用的Mini Fan风扇无法提供足够的扭矩驱动灯罩旋转，需要更强力的驱动方案
2. **灯光效果有限**：板载的6个LED无法提供全方位的灯光效果，影响走马灯的视觉表现
3. **交互体验受限**：单一方向的手势识别无法适应用户从不同角度操作的需求
4. **供电能力不足**：固定电源线限制了装置的移动性和灵活性

基于这些发现，我对系统方案进行了全面调整，采用了N20双轴蜗轮蜗杆电机、双RGB灯带、多方向手势传感器和便携电池系统等方案，以提供更好的用户体验和系统性能。这些调整也要求我重新设计PCB，以支持新增的硬件组件和功能。

目前项目的核心功能模块调整为：

+ 灯罩旋转系统：通过Grove Mini Fan 的 DC 驱动器驱动 N20 双轴蜗轮蜗杆电机，通过齿轮机构带动灯罩旋转。
+ RGB灯光效果：采用两条14 LED珠可编程全彩RGB灯带，背靠背安装实现360度可视效果。
+ 多方向手势交互：通过3个并联的APDS-9960手势传感器实现全方位的手势识别和控制。
+ Wi-Fi 连接功能：通过MQTT协议实现多灯笼之间的状态同步和Web界面的远程控制。
+ 便携电源系统：集成4000mAh可充电电池，支持移动使用。

本系统集成方案将详细说明如何基于调整后的圆形PCB设计，将这些模块整合为一个完整、可靠的系统。

## 智幻走马灯系统设计图
### 调整后的智幻走马灯系统架构总览
物理结构组成示意图如下图所示。

![](/images/week16/w16-p2-1.jpg)

> 智幻灯笼的物理结构组成示意图
>

智幻走马灯的系统架构组成入下图所示。

![](/images/week16/w16-p2-2.png)

> 智幻走马灯的系统架构组成示意图
>

## 调整后的系统组件清单
![](/images/week16/w16-p2-3.jpg)

我的智幻走马灯准备采用以下电子硬件，价格为在中国购买价格折算为美元。

| 组件类别 | 组件名称 | 数量 | 状态 | 价格(USD) | 说明 |
| --- | --- | --- | --- | --- | --- |
| **控制核心** | XIAO ESP32C3 | 1 | 已有 | $5.00 | 主力MCU，负责系统控制 |
| **输入设备** | XLOT APDS-9960 手势传感器 | 3 | 已有 | $15.00 | 三方向手势识别，并联连接 |
| **输出设备** | 14 LED RGB灯带 | 2 | 已有 | $4.00 | 背靠背安装，提供360°视觉效果 |
| | N20双轴蜗轮蜗杆电机 | 1 | 已有 | $6.00 | 从拓竹3D打印套件获取 |
| | Grove Mini Fan驱动器 | 1 | 已有 | $8.80 | 电机控制 |
| **电源系统** | 4000mAh可充电电池 | 1 | 已有 | $4.00 | 带有TypeC接口，支持同时充放电 |
| **结构部件** | 灯笼外壳 | 1 | 已完成 | - | 第3周完成激光切割原型 |
| | 旋转机构 | 1 | 已设计<br/>已制造 | - | 3D 打印，适配N20双轴电机 |
| | PCB 与电池舱 | 1 | 待设计<br/>待制造 | - | 在 PCB 板设计后进行 |
| **PCB** | 圆形PCB板 | 1 | 待设计制作 | - | 根据新方案定制 |


## 软件架构图
![](/images/week16/w16-p2-4.png)



## 信号流程图
![](/images/week16/w16-p2-5.png)

## 圆形 PCB 设计规格与要求
### 基本规格
+ **形状**：圆形PCB板
+ **直径**：约70mm（根据XIAO ESP32C3的尺寸和所有接口需求调整）
+ **层数**：双层 PCB（提供足够的走线空间）
+ **材质**：FR-4标准材质
+ **表面处理**：HASL铅免（符合环保要求）

### 关键设计要求
1. **去除原有设计中的6个板载LED**
2. **添加以下功能接口**： 
    - 5V电源输入/输出接口
    - 3个并联 APDS-9960 手势传感器接口
    - 2个可编程 RGB 灯带连接接口
    - 电机驱动器连接接口

### 接口设计详情
#### 电源系统接口
+ **电池接口**：JST 2 针连接器用于连接 4000mAh 可充电电池
+ **USB-C接口**：用于外部供电或调试
+ **5V输出接口**：2组5V和GND引脚，用于供电给外部组件

#### 手势传感器接口
+ **I2C接口×3**：为3个并联的APDS-9960手势传感器设计的接口 
    - 每组接口包含：VCC(3.3V)、GND、SDA(数据线)、SCL(时钟线)
    - 3个接口共享同一 I2C 总线，但每个接口可独立控制 VCC 电源

#### RGB灯带接口
+ **灯带接口×2**：支持2条可编程RGB灯带的并联连接 
    - 每组接口包含：5V、GND、数据控制引脚
    - 数据控制引脚连接到XIAO ESP32C3的GPIO引脚

#### 电机控制接口
+ **电机驱动接口**：连接 Grove Mini Fan 电机驱动器 
    - 包含：VCC(5V)、GND、控制信号引脚
    - 控制信号引脚连接到 XIAO ESP32C3 的 GPIO 引脚

### 调整后的集成步骤
#### 阶段一：PCB设计与制作（预计 5-7 天）
1. **圆形PCB设计**
    - 根据新方案完成原理图设计
    - 设计圆形PCB布局
    - PCB布线并验证设计
2. **PCB制作与组装**
    - 生产PCB板
    - 采购和准备所有元器件
    - 焊接XIAO ESP32C3及接口连接器
3. **接口测试**
    - 测试5V电源系统
    - 测试I2C总线
    - 测试GPIO输出接口

#### 阶段二：组件集成（预计2-3天）
1. **传感器系统集成**
    - 连接3个APDS-9960手势传感器
    - 编写多传感器管理代码
    - 测试传感器响应
2. **RGB灯带集成**
    - 安装2条RGB灯带
    - 整合Adafruit_NeoPixel库
    - 实现基础灯光效果
3. **电机系统集成**
    - 安装N20双轴蜗轮蜗杆电机
    - 连接Grove Mini Fan驱动器
    - 测试电机控制功能
4. **电源系统集成**
    - 连接4000mAh可充电电池
    - 测试充放电功能
    - 测量系统功耗

#### 阶段三：软件开发（预计2-3天）
1. **基础固件开发**
    - 整合多传感器处理代码
    - 实现RGB灯带控制逻辑
    - 实现电机控制功能
2. **通信系统实现**
    - 设置WiFi连接功能
    - 实现MQTT通信协议
    - 开发Web控制界面
3. **功能整合**
    - 实现手势控制逻辑
    - 开发多灯同步功能
    - 创建电池管理系统

#### 阶段四：机械结构整合（预计 5-7 天）
1. **传动机构实现**
    - 使用已设计好的传动装置
    - 3D打印传动零件
    - 测试传动效果
2. **灯笼外壳适配**
    - 修改灯笼外壳设计
    - 创建PCB和电池的安装结构
    - 安装RGB灯带支架
3. **最终组装**
    - 组装所有组件
    - 固定连接线缆
    - 进行整体平衡调整

## 调整后的测试计划
### 单元测试
| 测试对象 | 测试内容 | 预期结果 | 通过标准 |
| --- | --- | --- | --- |
| 多传感器系统 | 三方向手势识别 | 从三个不同方向识别手势 | 识别准确率>85% |
| RGB灯带控制 | 灯光效果控制 | 实现灯带跑马灯效果 | 视觉效果流畅 |
| 电机控制 | 速度控制和启停 | 电机平稳启动和调速 | 响应时间<150ms |
| 电池系统 | 续航能力 | 在正常使用下的续航时间 | >3小时 |
| 通信系统 | MQTT消息收发 | 设备间消息同步 | 延迟<200ms |


### 集成测试
| 测试场景 | 测试内容 | 预期结果 | 通过标准 |
| --- | --- | --- | --- |
| 全方位手势控制 | 从三个方向进行手势操作 | 系统对任意方向手势做出响应 | 响应率>90% |
| 灯光与电机协同 | 控制灯光和旋转同步变化 | 灯光和旋转效果协调一致 | 视觉效果良好 |
| 多灯笼同步 | 两台以上设备的联动 | 一台设备的变化同步到其他设备 | 同步延迟<300ms |
| 电源性能 | 满负载运行测试 | 电源系统稳定供电 | 电压波动<0.2V |


系统测试

| 测试方面 | 测试内容 | 预期结果 | 通过标准 |
| --- | --- | --- | --- |
| 可靠性 | 连续运行测试 | 系统长时间稳定运行 | 24小时无故障 |
| 网络适应性 | 不同网络环境测试 | 系统能适应网络变化 | 自动重连成功率>95% |
| 用户体验 | 实际用户操作测试 | 用户能直观操作系统 | 操作满意度高 |
| 功耗测试 | 不同模式下的功耗 | 记录并优化功耗表现 | 符合电池续航要求 |


## 潜在故障分析
| 故障点 | 风险描述 | 预防措施 | 故障处理方案 |
| --- | --- | --- | --- |
| PCB过载 | 多设备并联可能导致PCB过载 | 1. 增加PCB铜箔厚度<br/>2. 宽设计电源走线<br/>3. 监控系统温度 | 1. 软件限流保护<br/>2. 温度过高自动降低功耗 |
| I2C冲突 | 多传感器并联可能导致地址冲突 | 1. 验证传感器地址<br/>2. 软件检测地址冲突<br/>3. 使用I2C多路复用器 | 1. 自动检测可用传感器<br/>2. 降级为单传感器模式 |
| RGB灯带故障 | 灯带数据线干扰或损坏 | 1. 添加信号缓冲<br/>2. 保持数据线短而直接<br/>3. 屏蔽高速信号线 | 1. 灯带状态自检<br/>2. 单灯带降级模式 |


## 维修与生命周期考虑
### 模块化设计与维修性
调整后的智幻走马灯采用高度模块化设计，确保易于维护和升级：

1. **组件独立性**
    - PCB设计采用接插件连接方式，避免直接焊接外部组件
    - 三个手势传感器可单独更换和调整
    - RGB灯带采用标准接口，便于更换或升级
2. **维修便利性**
    - 灯笼外壳设计可拆卸式底座，方便维修内部组件
    - 电池采用可拆卸设计，支持快速更换
    - 所有接口和连接器都有明确标识
3. **故障诊断支持**
    - 内置自诊断程序，可通过Web界面检查各模块状态
    - LED指示灯提供系统状态和错误代码
