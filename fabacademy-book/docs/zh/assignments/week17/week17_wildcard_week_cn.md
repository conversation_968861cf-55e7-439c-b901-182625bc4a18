---
layout: doc
title: "第17周：通配符周 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第17周通配符周课程，学生可以自行选择想要学习的数字制造工艺，探索之前课程中未涉及的数字制造技术。"
head:
  - - meta
    - name: keywords
      content: fab academy, 通配符周, 数字制造, 多轴加工, 复合材料, 个人作业
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第16周个人作业：系统集成设计'
  link: '/zh/assignments/week16/week16_system_integration_design_cn'
next:
  text: '第17周个人作业：通配符周项目'
  link: '/zh/assignments/week17/week17_wildcard_week_project_cn'
lastUpdated: true
editLink: true
---

# 第17周：通配符周 (Wildcard Week)
## 课程概要
通配符周是Fab Academy课程中一个独特的环节，允许学生探索之前课程中未涉及的数字制造技术。本周的特点是学生可以自行选择想要学习的数字制造工艺，不同于往期课程中所有学生学习相同技能的模式。选择的工艺需满足以下条件：必须涉及数字设计与制造过程，需详细记录以便他人复制，且必须是课程中其他作业未涵盖的技能。这一周的灵活性使学生能够利用所在实验室的特殊设备或引入新的制造能力。

本课程分为两个部分：前半部分介绍各种可选的数字制造技术，后半部分深入讲解复合材料技术(以前是单独的一个课程周)。在选择项目时，学生需要权衡时间与注意力的分配，因为最终项目的截止日期临近。

查看[Wildcard Week资源库](http://inventory.fabcloud.io/?purpose=Wildcard%20Week)了解更多信息。

## 详细的课程内容介绍
### 第一部分：通配符周可选技术概述
#### 1. 多轴加工 (Multi-axis Machining)
+ **三轴加工**：虽然之前课程中已经介绍过二维半加工(XY平面移动加上Z轴步进)，但完整的三轴联动加工(同时控制XYZ三个轴)是一项可选的新技能
+ **四轴/五轴加工**：如有条件，可以学习更高级的多轴加工，其中工作台可以旋转(四轴)，或工作台旋转与刀头摆动结合(五轴)
+ **具体示例**: [5轴加工中心](https://cba.mit.edu/tools/#Hurco_VMX42SRTi_5_axis_machining_center)

#### 2. 特殊切割技术
+ **电火花加工(EDM)**：利用电极周围的等离子体进行金属切割 - [Sodick SL-400G 线切割电火花](http://cba.mit.edu/tools/index.html#Sodick_SL-400G_Wire_EDM)
+ **水射流切割**：使用超音速水射流混合研磨石榴石进行切割，可加工玻璃、石材等几乎任何材料 - [Omax 5555水射流切割机](http://cba.mit.edu/tools/index.html#Omax_5555_waterjet)
+ **等离子切割**：使用火炬产生的等离子体切割金属，需要在适当的安全环境中使用 - [Forest Scientific等离子切割机](http://forestscientific.com/hs-cnc-plasma-cutter/)
+ **光纤激光切割**：1000瓦特级别的高功率激光，能够切割金属 - [Fablight 3000激光金属切割机](http://cba.mit.edu/tools/index.html#Fablight_3000_Laser_Cutter)
+ **微加工激光**：超高精度激光，可以制作微米级别的零件 - [Oxford Alpha532微加工激光](http://cba.mit.edu/tools/index.html#Oxford_Alpha532_Laser)
+ **大型平板切割机**：类似于乙烯基切割机，但工作区域可达4×8英尺，适合大型切割项目 - [Zund G3数字切割系统](http://cba.mit.edu/tools/index.html#Zund_G3_L-2500_Digital_Cutter)

#### 3. 焊接 (Welding)
+ 单纯的焊接不满足数字制造的要求，但可以结合其他数字工具
+ 可以用计算机设计零件，使用数控工具切割，再通过MIG、TIG或点焊技术连接零件
+ **技术参考**: [焊接技术基础](http://fab.cba.mit.edu/classes/863.03/topics/assembly/forming/welding.pdf)

#### 4. 特殊模具和铸造技术
+ **真空成型**：加热塑料片，利用真空使其贴合模具形状，常用于包装制造 - [Brent的真空成型项目](http://fab.academany.org/2019/labs/lccc/students/brent-richardson/assignments/week18)
+ **旋转铸造**：通过旋转模具使材料均匀涂覆在内部表面，形成空心结构而非实心体 - [Saverio的旋转铸造项目](http://fabacademy.org/archives/2015/eu/students/silli.saverio/project07.html)

#### 5. 折叠技术 (Folding)
+ **曲面折纸**：利用激光切割机或乙烯基切割机制作可折叠结构
+ **数学折纸**：基于Eric Demaine等人的数学算法设计曲线折痕结构 - [Erik Demaine的折纸数学研究](http://erikdemaine.org)
+ **从3D到2D的转换**：设计二维切割图案以填充三维形状 - [Calisch的形状与功能研究](http://cba.mit.edu/docs/theses/19.09.calisch.pdf)
+ **切纸术(Kirigami)**：结合切割线和折痕线的折纸变体，可用于金属等材料 - [MIT切纸术研究](https://news.mit.edu/2023/using-kirigami-ultrastrong-lightweight-structures-0822)

#### 6. 机器人技术 (Robotics)
+ **通用机械臂**：如Elephant Robotics的低成本机械臂，可编程执行各种任务 - [UR10机器人手臂](http://cba.mit.edu/tools/index.html#Universal_Robotics_UR10_Robot_Arm)
+ **建筑机器人**：通过放置零件进行构建的移动机器人 - [Gramazio Kohler机器人建筑](https://www.google.com/search?q=gramazio+kohler+robotic&tbm=isch)
+ **软体机器人**：使用模具铸造后通过气压膨胀的软体抓取器和操作器 - [Adriana的软体机器人项目](https://adrianacabrera.github.io/SoftRobotics) 和 [Harvard软体执行器研究](https://gmwgroup.harvard.edu/soft-robotics)
+ **气动系统**：用于软体机器人的开源便携式气动生成器 - [Soft Robotics平台](https://www.softrobotics.io)
+ **充气结构**：通过选择性充气创建柔性运动结构 - [Otherlab充气结构](https://www.google.com/search?q=otherlab+inflatable&tbm=vid)

#### 7. 电子制造
+ **自动贴片机**：使用开源贴片机自动放置电子元件 - [Mechatronika M10V贴片机](http://cba.mit.edu/tools/index.html#Mechatronika_M10V_Pick_and_Place)
+ **可编程逻辑**：使用FPGA(现场可编程门阵列)创建可重构集成电路 - [嵌入式编程中的可编程逻辑](http://academy.cba.mit.edu/classes/embedded_programming/index.html)

#### 8. 嵌入式编程的特殊应用
+ **嵌入式AI**：使用TensorFlow Lite、TinyML、ESP-DL或Edge Impulse等平台在微控制器上实现机器学习 - [嵌入式AI资源](http://academy.cba.mit.edu/classes/embedded_programming/index.html#AI)
+ **机器视觉**：使用OpenCV或AI相机识别人脸、追踪移动物体等 - [输入设备中的图像处理](http://academy.cba.mit.edu/classes/input_devices/index.html#image)

#### 9. 食品制造
+ **食品打印**：设计和制造食品挤出、成型或处理的数字系统 - [哥伦比亚食品打印研究](https://magazine.columbia.edu/article/all-food-thats-fit-print)
+ **数字美食学**：使用数字技术操作食品 - [数字美食学平台](http://digitalgastronomy.co)

#### 10. 材料科学
+ **材料合成**：使用咖啡渣、蛋壳、海藻等易得材料创建开源材料库 - [Materiom开源材料库](https://materiom.org)
+ **材料测试**：设计和构建开源材料测试设备 - [位移测试练习](https://gitlab.cba.mit.edu/jakeread/displacementexercise)
+ **材料特性分析**：创建可测量材料特性的3D打印机 - [MIT流变学3D打印研究](https://news.mit.edu/2024/3d-printer-can-print-with-unknown-material-0408)

#### 11. 生物技术
+ **DIY生物学**：结合Fab Lab和生物实验室的技术 - [DIYbio组织](https://diybio.org)
+ **合成生物学**：参考iGEM竞赛的系统编程方法 - [iGEM国际遗传工程机器竞赛](https://igem.org)
+ **生物培育**："如何培育万物"课程的概念 - [HTGAA课程](http://fab.cba.mit.edu/classes/S61.20/index.html)和[Bio Academy平台](http://bio.academany.org)

#### 12. 纺织技术
+ **纤维加工**：编织、针织、钩编、毡制、编组等不同工艺 - [纤维加工工艺介绍](http://fab.cba.mit.edu/classes/865.18/fiber/index.html)
+ **电脑针织机**：学习编程控制针织机创建定制纺织品 - [机器针织指南](https://akaspar.pages.cba.mit.edu/machine-knitting/)
+ **刺绣机**：使用可编程的混合缝纫/刺绣机器 - [刺绣技术研究](http://cba.mit.edu/docs/theses/99.02.post.pdf)
+ **开源刺绣软件**：
    - [PEmbroider](https://github.com/CreativeInquiry/PEmbroider)
    - [Ink/Stitch](https://inkstitch.org)
+ **Fabricademy**：专注于纺织技术的姊妹课程 - [Textile Academy](http://textile-academy.org)

### 复合材料组成
1. **纤维材料选择**：
    - 短切纤维：易于使用但性能较差，因为不能有效传递力
    - 长丝：可以缠绕使用
    - 带状材料：可以铺设
    - 织物：最常用于Fab Lab，可以是玻璃纤维、碳纤维或天然纤维(棉、丝、竹、木、亚麻、黄麻)
2. **基质材料选择**：
    - 环氧树脂：常用但需注意安全
    - 酚醛树脂：用于FR1电路板等
    - 水泥：用于建筑
    - 天然树脂：更环保的植物基树脂
    - **供应商资源**: [Entropy Resins (生物基环氧树脂)](https://entropyresins.com/)、[McMaster-Carr](http://www.mcmaster.com/#garolite)、[West System](http://www.westsystem.com/)
3. **层压材料**：
    - 木材层压：例如定制滑板，使用多层木材在模具中压制并粘合
    - **参考示例**: [定制滑板项目](https://www.google.com/search?q=fab+lab+skateboard&source=lnms&tbm=isch)定制滑板，使用多层木材在模具中压制并粘合
    - **相关资源**: [纸板复合技术](http://fabacademy.org/2018/labs/fablabbcn/students/santi-fuentemilla/weeks/week15.html)

### 复合材料设计原则
+ **弯曲刚度原理**：外层纤维承受拉伸力，内层对结构贡献较小
+ **芯材使用**：在中间部分使用蜂窝结构或纸板等材料作为芯，减轻重量
+ **夹层结构**：底层复合材料、芯材和顶层复合材料的组合
+ **数字设计方法**：利用计算机设计复合结构的形状和性能 - [数字复合材料研究](http://cba.mit.edu/docs/papers/13.09.Science.pdf)

### 复合材料制作过程
1. **模具制作**：
    - 机械加工模具 - [加工模具示例图](http://academy.cba.mit.edu/classes/composites/machine.jpg)
    - 折叠模具 - [折叠模具示例图](http://academy.cba.mit.edu/classes/composites/fold.png)
    - 纸板框架作为内部形式 - [West Kust皮划艇表皮技术](http://westkustsurf.nl)
2. **铺层工艺**：
    - 湿法铺层：逐层铺设织物并涂抹树脂 - [湿法铺层示例](http://fab.cba.mit.edu/classes/863.12/people/calisch/10/week10.html)
    - 预浸料：已预先浸渍树脂的织物
    - RTM(树脂传递成型)：在压力下注入树脂
    - 材料比例控制：测试不同压缩条件下的纤维与树脂比例 - [复合材料优惠券测试](http://fab.cba.mit.edu/classes/863.16/doc/tutorials/composites/coupon-testing.html)
3. **压缩方法**：
    - 真空袋压缩：使用真空袋、透气层和排气层，靠大气压力压缩 - [真空袋技术详细指南](http://www.westsystem.com/ss/assets/HowTo-Publications/Vacuum-Bagging-Techniques.pdf)
    - 压模成型：使用两侧模具机械夹紧，挤出多余树脂
    - 水袋压力：简易方法，用装满水的垃圾袋提供重量 - [真空收纳袋示例](https://www.amazon.com/Space-Saver-Vacuum-Storage-Jumbo/dp/B004X98B5A)
4. **后处理**：
    - 表面处理：最终复合材料通常需要清漆等后处理获得光滑亮丽的表面

### 安全注意事项
+ **纤维危害**：碳纤维和玻璃纤维如果被切割、钻孔或打磨会产生短纤维，吸入后可能导致肺部疾病
+ **保护措施**：使用碳纤维时需要全面防护装备(呼吸保护、眼睛保护、皮肤保护)
+ **树脂安全**：普通环氧树脂挥发性较高，需要良好通风；推荐使用低挥发性的生物基树脂(如Entropy Resins)
+ **热反应**：大型复合材料固化时可能产生大量热量，需要准备冷却措施

### 建议与最佳实践
+ **对于初学者**：避开玻璃纤维和碳纤维，使用天然纤维和低挥发性树脂
+ **材料比例**：初学者可能使用50:50的纤维与树脂比例，高级复合材料可达90%纤维和10%树脂
+ **测试优先**：在制作大型部件前，先制作小样本("优惠券")测试工艺 - [复合材料优惠券集合](https://pub.pages.cba.mit.edu/compositecoupons)
+ **结合其他技能**：复合材料技术与"制作大型物品"和"模具与铸造"周的技能相结合，能大幅扩展实验室的制造能力

#### 建议与最佳实践
+ **对于初学者**：避开玻璃纤维和碳纤维，使用天然纤维和低挥发性树脂
+ **材料比例**：初学者可能使用50:50的纤维与树脂比例，高级复合材料可达90%纤维和10%树脂
+ **测试优先**：在制作大型部件前，先制作小样本("优惠券")测试工艺
+ **结合其他技能**：复合材料技术与"制作大型物品"和"模具与铸造"周的技能相结合，能大幅扩展实验室的制造能力

## 作业要求
本周的作业要求学生自己定义并完成：

1. **设计并制造**：使用数字制造过程(包含计算机辅助设计和制造)创建一个物品 - [参考示例项目](http://fab.cba.mit.edu/classes/863.12/people/calisch/10/skin.html)
2. **独特性要求**：所选技术必须是其他作业中未涵盖的
3. **文档化**：
    - 明确说明你的作业满足哪些要求
    - 详细记录所有必要的步骤，使他人能够复制你的项目
    - 解释你选择的技术和工艺
4. **数字化过程**：项目必须涉及计算机设计和制造，不仅仅是手工技能
5. **时间管理**：平衡这个新项目与最终项目的工作，合理安排时间

### 官方Fab Academy资源
+ [Wildcard Week资源库](http://inventory.fabcloud.io/?purpose=Wildcard%20Week)
+ [复合材料课程](http://academy.cba.mit.edu/classes/composites/index.html)
+ [往年Wildcard Week项目](http://fabacademy.org/archive/courses/index.html)
