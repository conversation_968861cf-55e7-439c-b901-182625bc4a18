---
layout: doc
title: "第17周个人作业：智幻走马灯圆环 PCB 设计与制造 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第17周个人作业，设计和制造智幻走马灯控制板，包括圆环PCB设计和PCB舱结构设计。"
head:
  - - meta
    - name: keywords
      content: fab academy, 通配符周, PCB设计, 圆环PCB, 灯笼控制板, 个人作业
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第17周：通配符周'
  link: '/zh/assignments/week17/week17_wildcard_week_cn'
next:
  text: '第18周：项目开发与应用前景展望'
  link: '/zh/assignments/week18/week18_project_development_and_applications_outlook_cn'
lastUpdated: true
editLink: true
---

# 第17周个人作业：智幻走马灯圆环 PCB 与 PCB 舱结构设计与制造
## 项目背景
本周我的主要时间都放在了我的 Fab Academy 2025 最终项目中的一个重要环节 —— 智幻走马灯控制板的设计和制造。在第 16 周，我进行了系统设计，修改了硬件方案。到了第17周，我需要设计一个全新的圆环形PCB（适合放置在灯笼的圆形齿轮箱上方）来控制整个系统，包括灯笼的旋转机构、LED灯效以及手势交互功能。这次的设计过程基本上通过和 Claude 沟通，在 AI 的指导下完成了整个过程（当然，绘制原理图和布线这些还得自己来）。完成 PCB 的设计后，我还用嘉立创 EDA 导出了 PCB 的模型，并在 Fusion 完成了 PCB 舱结构的设计。

![](/images/week17/w17-p-1.jpg)

> 智幻灯笼的 PCB 计划放在圆形齿轮箱的上方
>

## 设计需求分析
经过前面几周的折腾，我终于差不多搞清楚这块 PCB 到底需要什么功能了：

1. **造型要求**：必须是圆环形，我在 Fusion 里估算出 PCB 的外径92mm、内径22mm，这是因为要塞进灯笼的圆形齿轮舱的上面。
2. **核心控制 MCU**：用 Seeed Studio 的 XIAO ESP32C3，小巧且带 Wi-Fi ，功能够用。
3. **感知系统**：接3个APDS-9960手势传感器，这样才能360°无死角地接收手势命令。
4. **灯光与动力**：控制2条 14 灯珠的 RGB 灯带和 1个 Grove mini fan 电机驱动器控制的电机。
5. **电源管理**：最好是能自动在电池和 USB 之间切换，这个挺关键的。
6. **未来扩展**：把没用到的引脚引出，预留几个 Grove 接口，万一以后想加功能。
7. **制造工艺**：这次必须上双层板了，单层实在满足不了我的野心。

## 与 AI 沟通设计 PCB
首先，我向 Claude 提供了我认为尽可能详细的项目背景资料，包括基本外观规则、关键设计要求、APDS-9960 手势传感介绍资料、智幻走马灯的背景资料，当前项目进度和已解决与待解决的问题等，以及 XIAO ESP32C3 的引脚图，以及购买 LED 灯条网点的参数表截图内容。然后请 Claude 指导我使用 KiCad 绘制原理图和 PCB 设计。

![](/images/week17/w17-p-2.png)

> 让 Claude 指导我使用 KiCad 绘制原理图和 PCB 设计需要提供尽可能详细的背景信息
>

因为沟通的过程信息量很大，可以访问我分享的对话链接。

[https://claude.ai/share/430d4652-be82-4899-aa72-39eb7a691ac2](https://claude.ai/share/430d4652-be82-4899-aa72-39eb7a691ac2)

下面的内容是根据最终完成的设计和制造过程进行整理，我依据 AI 提供的初稿，结合实际情况做了改写。

## 设计工具的纠结过程
一开始我用的是 KiCad 画原理图，但搞着搞着就转战嘉立创 EDA了。为啥呢？主要有这几点原因：

+ 嘉立创的界面对我这种中文用户实在太友好了。
+ 它家的元件库超级丰富，而且自带3D模型，不用自己找。
+ 最爽的是可以直接从BOM表下单买元件，省去了到处找配件的麻烦。
+ 设计完直接发给制造商，一条龙服务。
+ [嘉立创 EDA 专业版](https://pro.lceda.cn/)是免费的，还提供在线版本，无需安装软件，用浏览器就可以进行从设计到制造的整个过程。

说实话，从 KiCad 迁移到嘉立创 EDA 的过程并不太平滑，但为了后面的便利还是值得的。而且一旦熟悉了原理图和 PCB 设计后，工具就渐渐变得不重要了。

| **比较项目** | **KiCad** | **嘉立创EDA** |
| --- | --- | --- |
| 开源性 | 完全开源 | 封闭平台 |
| 上手难度 | 较陡峭的学习曲线 | 对新手友好 |
| 元件库 | 需要自行寻找/创建 | 丰富且带3D模型 |
| 中文支持 | 有但不完善 | 原生支持 |
| 制造集成 | 需要导出Gerber | 一键下单生产 |
| 元件采购 | 分开处理 | 直接从 BOM 下单 |


![](/images/week17/w17-p-3.png)

> 开始用 KiCad 绘制的原理图
>

![](/images/week17/w17-p-4.png)

> 用嘉立创 EDA 绘制的原理图，整个过程都是在浏览器里进行的
>

## 原理图设计过程
### 核心模块设计
画原理图的时候，我得先搞定电源系统：

```plain
电池 ----> 二极管 ----> 开关 ----> V_BATT_PROTECTED 线路
USB 5V ----> 二极管 ----> V_BATT_PROTECTED 线路
```

别看这个简单，里面有很多学问。 Claude 建议我用了 1N5822 肖特基二极管而不是普通二极管，因为它的压降低(只有0.4V左右)，这意味着会浪费更少的电能。这对电池供电的设备来说可是省电的好办法！

另外，在 AI 的建议下还加了两个电容：一个 10μF 的电解电容和一个 0.1μF 的陶瓷电容。这两个电容就像一个电力的"蓄水池"和"过滤器"的组合 —— 大容量电解电容处理低频噪声，小容量陶瓷电容负责高频噪声。没有它们，电路里可能会出现各种莫名其妙的问题。

![](/images/week17/w17-p-5.png)

> 图3：电源管理电路原理图
>

###  Grove 接口连接方案
我打算把多余的引脚通过 Grove 接口方式引出，但怎么连线是个问题。最初 AI 给出的 Grove 接口引脚排列是VCC-GND-SIG1-SIG2，结果我研究了 Grove for Beginners 的 Grove 接口后，发现真正的顺序是SIG1-SIG2-VCC-GND！差点就接错了。AI 在一些细节的地方特别是背景资料没有说明的细节之处容易出错，要尤其小心。

![](/images/week17/w17-p-6.jpg)

> 我向 Claude 提供了一个满是 Grove 接口板子的参考图
>

### 输出控制接口
LED灯带用的是WS2812B芯片，又叫"内置驱动IC的智能RGB LED"。这东西只需要一根数据线就能控制一长串LED的颜色，省去了很多接线的麻烦。

> 小知识点：WS2812B的通信协议是单线归零码，时序要求比较严格。每个LED接收24位数据(RGB各8位)后，会将剩余信号传给下一个LED，从而实现级联控制。
>

## PCB布局设计 - 有趣的转折
本来我计划做个完美的圆环形PCB，如下图所示，放在齿轮舱的上方。

![](/images/week17/w17-p-7.jpg)

结果等我拿到电池一看，发现这个大家伙根本放不进去！我用电池的长宽高尺寸在 Fusion 里做了一个占位模型（下图的红色部分），比划了半天，发现比较好的方式是充分利用齿轮舱的一半空间，如下图所示。

![](/images/week17/w17-p-8.jpg)

> 充分利用齿轮舱的一半空间是比较好的放置方案
>

我临时决定把圆环切掉一块，给电池腾地方。根据这个思路，我为齿轮箱设计了一个上盖，同时也作为 PCB 的支撑结构，并掏了一些孔以方便走线和降低用料量。Fusion 里的效果如下图所示。

![](/images/week17/w17-p-9.jpg)

> 为齿轮箱加了一个缺掉一块的圆形盖子同时也作为 PCB 的支撑结构，
>

于是。这就是为什么 PCB 的设计看起来像个"缺了一口的月饼"，我先按自己的理解将元件摆放的尽量合理。

![](/images/week17/w17-p-10.jpg)

> PCB布局3D效果图
>

布局时我遵循了几个原则：

+ XIAO ESP32C3放在顶部，这样程序上传接口容易够到
+ 三个手势传感器接口尽量均匀分布，覆盖不同方向
+ 电源管理元件集中在一起，避免干扰其他电路

说到布局，有个实用技巧：先布大件，再布小件。先确定那些位置受限或固定的元件(比如连接器、大型元件)，然后再布置那些位置灵活的小元件(如电阻、电容)。

## 走线策略 - 双层板的奥秘
这次尝试了双层板，比起单层板，自由度高了不少。我的布线策略如下：

+ **顶层**：主要走电源线和重要信号线，用粗线 1.0mm 走电源
+ **底层**：绝大部分做成GND平面，提供低阻抗的接地路径
+ **过孔**：适当添加过孔，连接顶层GND和底层地平面

走线时的个人心得：

> "别小看走线这活儿，感觉门道很多！弯线最好用45°角，不要90°直角；信号线尽量不要并行太长，否则可能互相干扰；高速信号尽量短而直接；电源和地之间一定要加足够的去耦电容。这些小细节决定了板子做出来后是否稳定可靠。"
>

走线可能是我 PCB 设计里花时间最多的过程，很多细节要做多次尝试才能找到合适的解决方案。
![](/images/week17/w17-p-11.png)

> 最终完成的 PCB 板设计，我加了些支撑固定孔
>

![](/images/week17/w17-p-12.png)

> 设计完成后的 PCB 板的 3D 效果图 
>

嘉立创的 EDA 还允许导出 3D 模型为 obj 文件，如下图所。

![](/images/week17/w17-p-13.png)

> 导出 3D 文件里支持导出 OBJ 模型的选项
>

在 Fusion 我可以通过设计界面的 实体/插入/插入网格 来导入 OBJ 文件，这样我就可以在我的设计中看到 PCB 部分的实际 3D 效果。

![](/images/week17/w17-p-14.jpg)

> 将导入的 PCB 模型放入到现在的结构中来，可以帮助评估 PCB 和结构的
>

## 元件选择与订购 - 淘宝工程师上线
作为资深"淘宝工程师"，元件选购现在也变成了我的的“强项”。以下是我的一些选择和思考：

### 肖特基二极管(1N5822)
这货看着不起眼，其实很重要。选它是因为：

+ 正向压降低，只有0.4V左右，比普通二极管的0.7V省电多了
+ 3A的电流能力，远超我系统需要的0.5A，有足够余量
+ 响应速度快，适合保护电路

### 电容选择
我在电容选择上纠结了好久：

+ 电解电容要低ESR(等效串联电阻)的，这样滤波效果才好
+ 陶瓷电容选了Y5V介质，虽然不如X7R稳定，但价格便宜，基本够用

### 连接器
+ 用了JST-SH兼容的1.0T-4P立贴，这种小巧精致的连接器很适合手势传感器
+ LED灯带和Grove接口用了更常见的PH2.0系列接口

### 导出 BOM 表
嘉立创 EDA 导出 BOM 表的时候，提供了“元件下单”功能，如下图所示。

![](/images/week17/w17-p-15.png)

> 嘉立创 EDA 导出 BOM 表时的“元件下单”功能按钮位置
>

接下来可以选择采购套数，我准备采购 3 套（如果时间允许，我想制造 3 只灯笼）。

![](/images/week17/w17-p-16.png)

> 直接输入采购套数，因为我对元件品牌还完全没有太多认知，就没有展开选择
>

在 BOM 配单页面，可以选择需要哪些物料。我去掉了 XIAO ESP32C3 和 Grove 接口（手头有）。

![](/images/week17/w17-p-17.png)

最终所有需要的元件总共才花了32.13元！还包邮！选择了广东仓发货，第二天元件就到手了，效率简直感人。下图中的订单中的数量，全部都按最低起订数量下单。

![](/images/week17/w17-p-18.png)

> 图6：元件订单截图
>

![](/images/week17/w17-p-19.jpg)

![](/images/week17/w17-p-20.jpg)

> 收到的元件都按袋分装，倒出来查看拍照
>

## PCB制造 - 意外的惊喜
提交PCB制造订单时，发生了一个意外惊喜。这块双层板，比我之前做的单层板还便宜！只花了33元就拿下了5片PCB。

仔细研究后发现，原来我第一版选了"无铅喷锡"这个非标准工艺。这次选了普通的"有铅喷锡"，便宜多了。

> 制造工艺小知识：普通的有铅喷锡工艺完全能满足个人项目需求，而且成本低。无铅工艺主要是为了满足环保要求，对于原型开发阶段小批量制造来说可以先不考虑。
>

![](/images/week17/w17-p-21.png)
> PCB制造订单截图
>

## 剩余部分结构设计
这部分花了 1 天多时间，在 Fusion 逐步完成了 PCB 舱和手势传感器支架部分的设计，然后用 3D 打印输出了结构进行了测试。

![](/images/week17/w17-p-22.png)

> 在 Fusion 逐步完成了 PCB 舱和手势传感器支架部分的设计
>

3D 打印了 PCB 舱和手势传感器支架，3 个主结构直接通过嵌套方式就可以很紧实的连接在一起。

![](/images/week17/w17-p-23.jpg)

> 3D 打印后 3 个结构舱通过嵌套就可以很紧实的连接在一起，和外框和旋转机构组合在一起后，整个结构主体部分的工作就全部完成了
>

现在就差 PCB 了。

## 遇到的坑和解决方案
说实话，这个过程也不是一帆风顺的，遇到了不少坑：

1. **电流估算不足**
    - 问题：低估了LED灯带的电流需求
    - 解决：重新计算了全亮时的最大电流(约0.5A)，选了更大容量的二极管和开关
2. **空间布局挑战**
    - 问题：电池太大，标准圆环放不下
    - 解决：修改PCB形状，切掉一部分给电池留位置

我的经验是：遇到问题不要慌，先仔细分析，和 AI 沟通要充分告知背景资料，以便得到有价值的指导。

## 一些个人心得和建议
作为一个PCB设计新手，这个项目让我学到了不少东西，分享几点心得：

1. **工具不重要，重要的是理解基本原理**
    - 无论用KiCad还是嘉立创EDA，电路原理是通用的
2. **双层板真的比单层板好用太多**
    - 布线更灵活，信号质量更好，强烈推荐新手直接从双层板开始学
3. **国产工具和服务生态已经很成熟了**
    - 嘉立创EDA + 嘉立创制造 + 嘉速电子元件，这一条龙服务真的很香
4. **纸上谈兵不如实际动手**
    - 再多理论知识也比不上亲手设计一块板子，遇到问题解决问题

## 后续工作计划
PCB弄好只是第一步，接下来我还要：

1. 把所有元件焊接到板子上
2. 编写控制固件，测试每个功能模块
3. 将PCB整合到灯笼的机械结构中
4. 进行整体系统测试
5. 灯笼转笼贴片设计安装
6. 编写文档

希望再有一周，能看到这个智幻走马灯完美运转起来！

---

PS: 有啥问题或建议，欢迎随时交流！我这个项目的所有资料都会开源分享，希望对其他maker有所帮助！
