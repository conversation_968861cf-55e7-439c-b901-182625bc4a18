---
layout: doc
title: "第10周：小组作业：测量输出设备功耗 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第十周小组作业：使用YZXstudio ZY1271电压电流容量表测量LED、Grove迷你风扇和LCD显示屏的功耗特性，分析输出设备的电气特性"
head:
  - - meta
    - name: keywords
      content: fab academy, 小组作业, 功耗测量, LED功耗, 电机功耗, 显示屏功耗, 电压电流容量表, 电气特性, XIAO ESP32C3
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第10周：输出设备'
  link: '/zh/assignments/week10/week10_output_devices_cn'
next:
  text: '第10周：个人作业：手势控制迷你风扇与LED'
  link: '/zh/assignments/week10/week10_individual_gesture_fan_led_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第10周小组作业：测量输出设备功耗
## 任务概述
根据[第10周课程大纲](http://academy.cba.mit.edu/classes/output_devices/index.html)的要求，我们小组需要选择输出设备，使用电压电流容量表测量其电气特性，分析并理解其工作原理。在本次作业中，我们将分为 3 个部分介绍：

1. 测量自制XIAO ESP32C3开发板上的6个LED的功耗特性（由冯磊编写）
2. 测量Grove迷你风扇的功耗和工作特性（由冯磊编写）
3. 测量XIAO ESP32S3 接入 LCD的功耗 （由刘鸿泰编写）

## 材料与设备
### 硬件设备
1. 自制的XIAO ESP32C3开发板（带有6个LED）
2. Grove迷你风扇模块
3. YZXstudio ZY1271 USB电压电流容量表
4. 连接线/杜邦线
5. USB数据线
6. 电脑（安装Arduino IDE）

### 核心测量设备：YZXstudio ZY1271 USB电压电流容量表
![](/images/week10/w10-g-1.jpg)

> 彩色屏幕的 YZXstudio ZY1271 USB 电压电流容量表
>

**主要技术规格**：

+ 输入电压：DC4-24V
+ 输入电流：连续正负3A
+ 电压分辨率：0.0001V
+ 电流分辨率：0.0001A
+ 容量累计：0-99999Ah；0-99999Wh
+ 容量分辨率：0.0001Ah；0.0001Wh
+ 精度：电压档0.2%+2d；电流档0.1%+2d
+ 显示屏幕：1.3寸128*104点阵彩色TFT液晶显示器
+ 刷新速度：0.36秒/次

## 测量指导
### 使用YZXstudio ZY1271进行测量的步骤
1. **硬件连接**
    - 将USB电压电流容量表与电源适配器连接
    - 将测试仪输出端与XIAO ESP32C3开发板连接
    - 完整连接顺序：电源适配器 → YZXstudio ZY1271 → XIAO ESP32C3开发板

![](/images/week10/w10-g-2.jpg)

> 设备连接顺序：电脑 USB 接口（作为电源适配器） → YZXstudio ZY1271 → XIAO ESP32C3开发板
>

2. **测试仪操作说明**

按测试仪器侧面的按钮，可以在多个功能模式下切换，按钮位置如下图所示。

![](/images/week10/w10-g-3.jpg)

> YZXstudio ZY1271 USB 电压电流容量表的功能切换按钮位置
>

+ **功能1：**大字体显示电压、电流、Ah、Wh，右边显示温度，当前容量组，此界面长按可以跳到下一组，超长按可以清零当前组，短按切换到下一功能。 

![](/images/week10/w10-g-4.jpg)

> YZXstudio ZY1271 USB 电压电流容量表功能 1 显示的内容画面
>

> Ah (安时): 这是电流随时间的累积值，表示用电设备消耗的电荷总量。1安时等于1安培电流持续1小时。例如，如果一个设备持续消耗0.5安培电流2小时，那么它消耗了1安时的电荷量。Ah值反映了电池容量和设备耗电量的物理单位。
>
> Wh (瓦时): 这是功率随时间的累积值，表示设备消耗的能量总量。1瓦时等于1瓦功率持续1小时。计算公式为：Wh = V × Ah，即电压乘以安时数。Wh值直接反映了实际能量消耗，考虑了电压因素。
>
> 在测量设备功耗时：
>
> + Ah告诉你消耗了多少电荷
> + Wh告诉你消耗了多少能量
>
> 这两个值对于评估电池寿命、比较不同设备的能效以及计算电源系统运行成本非常有用。在YZXstudio ZY1271电压电流容量表中，这些数值会随着设备运行不断累计，直到手动清零。
>

+ 功能2：电压电流不变，下面显示大字体功率、等效负载电阻。此界面长按可以跳到下一组，超长按可以清零当前组，短按切换到下一功能。 

![](/images/week10/w10-g-5.jpg)

> YZXstudio ZY1271 USB 电压电流容量表功能 2 显示的内容画面
>

功能3：D+ D-电压表，可以测得USB插座的 D+ D-电压，用于判断适配器类型和USB口类型。注意测适配器类型建议空载测试，插入手机或设备会干扰测试结果。右边显示记录时间和涓流屏蔽，电流大于涓流屏蔽值系统才会记录容量，时间才会增加，同时这里显示·REC，可在设置里改涓流值。长按进入系统设置。NORM 正常输出，HI A过流，HI V过压，LO V欠压，HOT过热。 

![](/images/week10/w10-g-6.jpg)

> YZXstudio ZY1271 USB 电压电流容量表功能 3 显示的内容画面
>

+ 功能4：测微电阻功能，推荐用于数据线测试。有2种测试模式，分别为  
1：压降检测  
2：4线开尔文检测。  
压降检测模式下， REF显示参考数据，CRT当前电压电流，RES计算出的微电阻，Vloss数据线压降。测试方法：USB表直插适配器，恒流负载调到稳定的1-2A插USB表，长按记录REF参考数据，然后待测线插适配器，另外一头插USB表和刚才的恒流负载，RES栏就会显示阻值和压降。4线开尔文检测请购买专用测试治具，直接把被测数据线插到治具上就可以显示线阻。 

![](/images/week10/w10-g-7.jpg)

> YZXstudio ZY1271 USB 电压电流容量表功能 4 显示的内容画面
>

功能5：曲线绘制，可用于监控电压电流波动的情况。长按可以切换正常慢速曲线、快速电压电流曲线、D+ D-电压曲线、后台记录曲线。曲线绘制速度和离线曲线记录的时间间隔可在设置里修改，一共可以记录384点离线数据，时间范围从1小时-48小时。 

![](/images/week10/w10-g-8.jpg)

> YZXstudio ZY1271 USB 电压电流容量表功能 5 显示的内容画面
>

系统设置： 按住按键再通电进入系统设置，如下图所示。 

![](/images/week10/w10-g-9.jpg)

> YZXstudio ZY1271 USB 电压电流容量表进入系统设置的内容画面
>

进入设置后短按切换设定项，长按进入设定项目，短按微调参数，设定好后长按保存。 

> 01：长按退出设定。 
>
> 02：参数加减，设定项目时单次按键参数增大或减小。 
>
> 03：屏幕显示亮度。 
>
> 04：超时待机屏幕显示亮度，0为待机关闭屏幕。 
>
> 05：无操作待机延迟时间，0为不待机。 
>
> 06：大数字字体；旋转显示方向。 
>
> 07：串口上传周期；格式，0关闭串口上传功能。 
>
> 08：涓流屏蔽；记录门限，当实际电流小于此值停止记录。 
>
> 09：开机画面延迟，0关闭开机画面。 
>
> 10：温度校准，调节到显示温度和室温相同。 
>
> 11：后台记录周期；离线曲线周期，0关闭后台记录。 
>
> 12：清空所有记录的数据。 
>
> 13：恢复校准过的出厂设置。 
>
> 14-19：ZY1272的保护功能设置，其他型号自动跳过。 
>
> 20：10V电压基准，输入精确的10V选择此项校准。 
>
> 21：2A电流基准，输出精确的2A选择此项校准。 
>
> 22：备份出厂设置，请勿擅自备份避免覆盖出厂校准。 
>
> 23：恢复没有校准过的初始设置。 
>
> 24：恢复校准过的出厂设置。 
>

3. **读取测量数据**
    - 电压值：直接从顶部大数字读取，精确到0.0001V
    - 电流值：从第二行大数字读取，精确到0.0001A
    - 功率值：短按切换到功能2，从第三行读取，单位为W
    - 累计容量：停留在功能1，读取第三、四行的Ah和Wh值
4. **捕获瞬态数据**
    - 电机启动电流峰值：使用功能5曲线绘制功能
    - 长按进入曲线绘制，选择"快速电压电流曲线"
    - 在电机启动瞬间观察曲线，记录峰值

5. **注意事项**
    - 每次测量前确保读数稳定（约等待2-3秒）
    - 对于快速变化的值，使用曲线功能捕获
    - 涓流屏蔽值设为低值（<10mA），确保捕获小电流变化
    - 测量启动电流峰值时，可使用测试仪的截屏功能（长按后选择截屏）

## 第一部分：测量XIAO ESP32C3开发板LED功耗
### 开发板LED布局简介
自制的XIAO ESP32C3开发板上有6个LED（D0-D5），均通过合适的限流电阻连接到ESP32C3的GPIO引脚。根据原理图，每个LED的电路结构如下：

```plain
Vcc (3.3V) --- LED --- 限流电阻(220Ω) --- GPIO引脚
```

### 实验设置
1. **硬件连接**
    - 将YZXstudio ZY1271 USB 电压电流容量表串联在电源与XIAO ESP32C3开发板之间
    - 使用USB数据线将开发板连接到电脑
    - 烧录测试程序到开发板
2. **测试程序**

我编写了有个测试程序，并添加了针对 YZXstudio ZY1271 USB 电压电流容量表的按键提示，方便在记录过程中能在串口监视器中按提示操作。

```cpp
// LED引脚定义
const int LED_PINS[] = {D0, D1, D2, D3, D4, D5};
const int LED_COUNT = 6;

// 测试状态标记
int testPhase = 0;

void setup() {
  Serial.begin(115200);
  
  // 等待串口连接
  delay(2000);
  
  Serial.println("\n===== XIAO ESP32C3 LED与风扇功耗测试 =====");
  Serial.println("测试开始前请按照以下步骤设置电源测试仪:");
  Serial.println("1. 短按测试仪按键切换到功能2界面(显示电压、电流、功率和等效电阻)");
  Serial.println("2. 长按清零Ah/Wh累计值(按住按键3-5秒直到归零)");
  Serial.println("3. 准备记录初始基准功耗");
  Serial.println("\n按下Arduino IDE的回车键开始测试...");
}

void loop() {
  // 等待用户输入开始测试
  if (testPhase == 0) {
    if (Serial.available() > 0) {
      Serial.read(); // 清除输入缓冲
      
      // 基准功耗测量
      Serial.println("\n----- 基准功耗测量 -----");
      Serial.println("所有LED处于关闭状态");
      Serial.println("【操作提示】请记录当前电压、电流、功率值");
      Serial.println("【操作提示】测量完成后按回车键继续...");
      
      // 关闭所有LED以确保基准状态
      for (int i = 0; i < LED_COUNT; i++) {
        pinMode(LED_PINS[i], OUTPUT);
        digitalWrite(LED_PINS[i], LOW);
      }
      
      testPhase = 1;
    }
    return;
  }
  
  // 单个LED测试
  else if (testPhase == 1) {
    if (Serial.available() > 0) {
      Serial.read(); // 清除输入缓冲
      
      Serial.println("\n----- 测试1：单个LED功耗测量 -----");
      Serial.println("将依次点亮每个LED 5秒钟");
      Serial.println("【操作提示】请记录每个LED点亮时的电压、电流和功率值");
      Serial.println("测试自动开始...\n");
      
      delay(2000);
      
      // 顺序点亮LED
      for (int i = 0; i < LED_COUNT; i++) {
        // 先关闭所有LED
        for (int j = 0; j < LED_COUNT; j++) {
          digitalWrite(LED_PINS[j], LOW);
        }
        
        // 点亮当前测试的LED
        digitalWrite(LED_PINS[i], HIGH);
        
        Serial.print("LED ");
        Serial.print(i);
        Serial.println(" 已开启 - 记录测量值");
        
        // 等待测量
        delay(5000);
      }
      
      // 关闭所有LED
      for (int i = 0; i < LED_COUNT; i++) {
        digitalWrite(LED_PINS[i], LOW);
      }
      
      Serial.println("\n单个LED测试完成");
      Serial.println("【操作提示】按回车键继续PWM亮度测试...");
      
      testPhase = 2;
    }
    return;
  }
  
  // PWM亮度测试
  else if (testPhase == 2) {
    if (Serial.available() > 0) {
      Serial.read(); // 清除输入缓冲
      
      Serial.println("\n----- 测试2：PWM亮度控制与功耗 -----");
      Serial.println("将以不同占空比点亮LED 0");
      Serial.println("【操作提示】请记录不同亮度下的电压、电流和功率值");
      Serial.println("测试自动开始...\n");
      
      delay(2000);
      
      // 各种占空比测试
      int pwmValues[] = {64, 128, 192, 255};
      for (int i = 0; i < 4; i++) {
        analogWrite(LED_PINS[0], pwmValues[i]);
        
        Serial.print("LED 0的PWM占空比：");
        Serial.print((pwmValues[i] / 255.0) * 100);
        Serial.println("% - 记录测量值");
        
        // 等待测量
        delay(5000);
      }
      
      // 关闭LED
      digitalWrite(LED_PINS[0], LOW);
      
      Serial.println("\nPWM亮度测试完成");
      Serial.println("【操作提示】按回车键继续全部LED测试...");
      
      testPhase = 3;
    }
    return;
  }
  
  // 所有LED同时点亮测试
  else if (testPhase == 3) {
    if (Serial.available() > 0) {
      Serial.read(); // 清除输入缓冲
      
      Serial.println("\n----- 测试3：所有LED同时点亮 -----");
      Serial.println("将同时点亮所有LED");
      Serial.println("【操作提示】请记录总电压、电流和功率值");
      Serial.println("测试自动开始...\n");
      
      delay(2000);
      
      // 点亮所有LED
      for (int i = 0; i < LED_COUNT; i++) {
        digitalWrite(LED_PINS[i], HIGH);
      }
      
      Serial.println("所有LED已开启 - 记录测量值");
      
      // 等待测量
      delay(10000);
      
      // 关闭所有LED
      for (int i = 0; i < LED_COUNT; i++) {
        digitalWrite(LED_PINS[i], LOW);
      }
      
      Serial.println("\n所有LED测试完成");
      Serial.println("【操作提示】按回车键开始曲线记录测试...");
      
      testPhase = 4;
    }
    return;
  }
  
  // 曲线记录测试提示
  else if (testPhase == 4) {
    if (Serial.available() > 0) {
      Serial.read(); // 清除输入缓冲
      
      Serial.println("\n----- 测试4：曲线记录测试 -----");
      Serial.println("【操作提示】短按多次切换到功能5(曲线界面)");
      Serial.println("【操作提示】准备记录LED闪烁电流变化");
      Serial.println("【操作提示】按回车键开始LED闪烁...");
      
      testPhase = 5;
    }
    return;
  }
  
  // LED闪烁测试
  else if (testPhase == 5) {
    if (Serial.available() > 0) {
      Serial.read(); // 清除输入缓冲
      
      Serial.println("\n开始LED闪烁测试 - 观察曲线变化");
      
      // 快速闪烁所有LED
      for (int cycle = 0; cycle < 5; cycle++) {
        // 打开所有LED
        for (int i = 0; i < LED_COUNT; i++) {
          digitalWrite(LED_PINS[i], HIGH);
        }
        Serial.println("LED全部点亮");
        delay(1000);
        
        // 关闭所有LED
        for (int i = 0; i < LED_COUNT; i++) {
          digitalWrite(LED_PINS[i], LOW);
        }
        Serial.println("LED全部关闭");
        delay(1000);
      }
      
      Serial.println("\nLED闪烁测试完成");
      Serial.println("【操作提示】短按返回功能2进行下一轮测试");
      Serial.println("\n===== 全部测试流程结束 =====");
      Serial.println("按回车键重新开始测试流程...");
      
      testPhase = 0;
    }
    return;
  }
}
```

程序中的 PWM 是脉冲宽度调制。

上传程序后打开串口监视器，按提示操作，我用摄像头录下了屏幕提示和 YZXstudio ZY1271 USB 电压电流容量表的读数，如下图所示。

![](/images/week10/w10-g-10.jpg)

> 上传程序后打开串口监视器，按提示操作
>

### 测量结果与分析
#### 1. 基准功耗（所有LED关闭）
| 参数 | 测量值 |
| --- | --- |
| 电压 | 5.2199 V |
| 电流 | 0.0184 A |
| 功率 | 0.096 W |


这代表开发板的基础功耗，包括ESP32C3芯片和板上其他外设的静态功耗。

#### 2. 单个LED功耗测量
| LED编号 | 电压 (V) | 电流 (A) | 功率 (W) | 增量电流 (mA) | 增量功率 (mW) |
| --- | --- | --- | --- | --- | --- |
| D0 | 5.2195 | 0.0208 | 0.1085 | 2.40  | 12.5 |
| D1 | 5.2194 | 0.021 | 0.1096 | 2.60  | 13.6 |
| D2 | 5.2193 | 0.0208 | 0.1085 | 2.40  | 12.5 |
| D3 | 5.2195 | 0.0208 | 0.1085 | 2.40  | 12.5 |
| D4 | 5.2194 | 0.021 | 0.1096 | 2.60  | 13.6 |
| D5 | 5.2194 | 0.0212 | 0.1106 | 2.80  | 14.6 |


从测量结果可见，每个LED增加约 2.4-2.8mA的电流消耗。

#### 3. PWM亮度控制功耗（D0 LED）
| PWM占空比 (%) | 电压 (V) | 电流 (A) | 功率 (W) | 相对基准增量电流 (mA) |
| --- | --- | --- | --- | --- |
| 25% (64/255) | 5.2196 | 0.0190 | 0.0991 | 0.6 |
| 50% (128/255) | 5.2196 | 0.0195 | 0.1017 | 1.1 |
| 75% (192/255) | 5.2194 | 0.0202 | 0.1054 | 1.8 |
| 100% (255/255) | 5.2194 | 0.0208 | 0.1085 | 2.4 |


PWM控制结果显示，LED的功耗与占空比成正比关系，这证实了PWM调光的工作原理：通过改变LED的导通时间比例来调整亮度，而不是改变LED两端的电压。

#### 4. 所有LED同时点亮
| 参数 | 测量值 |
| --- | --- |
| 电压 | 5.2180 V |
| 电流 | 0.0339 A |
| 功率 | 0.1768 W |
| 相对基准增量 | 15.5 mA / 80.8 mW |


所有LED同时点亮时，总增量电流约为 15.5mA，略高于单个LED增量电流之和（约 15.2mA），如下图所示。这可能是由于电源压降导致的轻微效率变化。

![](/images/week10/w10-g-11.jpg)

> LED 全部点亮时的读数
>

### LED功耗测试结论
1. **单LED功耗**：每个LED在全亮状态下消耗约 2.4-2.8mA电流，功耗约12.5-14.6mW。
2. **PWM调光效率**：PWM调光方式下，功耗与占空比呈线性关系，证实了数字PWM调光的高效性。
3. **总体效率**：六个LED全部点亮时总功耗增加约15.5mW，平均每个LED约 2.6mW。
4. **电流分布均匀**：六个LED的电流消耗基本一致，表明电路设计均衡。

## 第二部分：Grove迷你风扇功耗测量
### Grove迷你风扇简介
[Grove迷你风扇](https://wiki.seeedstudio.com/Grove-Mini_Fan/)是一款小型直流风扇模块，采用标准 Grove 接口设计，无需焊接即可连接到微控制器。风扇由直流电机驱动，可通过数字引脚控制开关，或通过 PWM 信号调节速度。

**基本规格**：

+ 工作电压：3.3V-5V
+ 接口类型：数字
+ 额定电流：约130mA（5V供电）
+ 带基于 AVR Atmega168 微控制器的直流电机驱动器

![](/images/week10/w10-g-12.jpg)

> Grove - Mini Fan 模块是基于 AVR Atmega168 微控制器的直流电机驱动器
>

### 实验设置
1. **硬件连接**
    - 将 Grove 迷你风扇的驱动器的 A5 （多数 Grove 传感器模块都会使用 A5 作为主要信号线，所以建议优先尝试连接 A5 到 D6）连接到XIAO ESP32C3开发板的 D6 引脚；驱动器的 GND 引脚与 XIAO 的 GNG 引脚相连； 驱动器的 VCC 引脚与 XIAO 的 3V3 引脚连接，如下图所示。

![](/images/week10/w10-g-13.png)

> Grove 迷你风扇的驱动器与 XIAO ESP32C3 的接线图
>

    - 将 YZXstudio ZY1271 USB电压电流容量表串联在电源与开发板之间。
    - 使用USB数据线将开发板连接到电脑。

![](/images/week10/w10-g-14.jpg)

> 实物连接图
>

2. **测试程序**

```cpp
// Grove迷你风扇基础测试 - 避免PWM导致的不稳定
#define FAN_PIN D6  // 将风扇连接到数字引脚D6

// 测试状态标记
int testPhase = 0;

void setup() {
  Serial.begin(115200);
  
  // 等待串口连接
  delay(2000);
  
  Serial.println("\n===== XIAO ESP32C3 Grove迷你风扇基础测试 =====");
  Serial.println("这是一个简化版测试程序，只测试风扇的开关状态");
  Serial.println("以避免PWM控制导致的连接不稳定问题");
  Serial.println("\n按下Arduino IDE的回车键开始测试...");
  
  pinMode(FAN_PIN, OUTPUT);
  digitalWrite(FAN_PIN, LOW); // 开始时风扇关闭
}

void loop() {
  // 等待用户输入开始测试
  if (testPhase == 0) {
    if (Serial.available() > 0) {
      Serial.read(); // 清除输入缓冲
      
      // 基准功耗测量
      Serial.println("\n----- 基准功耗测量 -----");
      Serial.println("风扇处于关闭状态");
      Serial.println("【操作提示】请记录当前电压、电流、功率值作为基准");
      Serial.println("【操作提示】完成后按回车键启动风扇...");
      
      digitalWrite(FAN_PIN, LOW); // 确保风扇关闭
      
      testPhase = 1;
    }
    return;
  }
  
  // 风扇全速测试
  else if (testPhase == 1) {
    if (Serial.available() > 0) {
      Serial.read(); // 清除输入缓冲
      
      Serial.println("\n----- 测试：风扇全速运行功耗 -----");
      Serial.println("风扇将开始运行");
      
      // 直接开启风扇，不使用PWM
      digitalWrite(FAN_PIN, HIGH);
      
      Serial.println("风扇已开启 - 全速运行");
      Serial.println("【操作提示】请记录电压、电流和功率值");
      Serial.println("【操作提示】完成记录后按回车键关闭风扇...");
      
      testPhase = 2;
    }
    return;
  }
  
  // 关闭风扇
  else if (testPhase == 2) {
    if (Serial.available() > 0) {
      Serial.read(); // 清除输入缓冲
      
      Serial.println("\n----- 测试结束 -----");
      Serial.println("关闭风扇");
      
      // 关闭风扇
      digitalWrite(FAN_PIN, LOW);
      
      Serial.println("风扇已关闭");
      Serial.println("【操作提示】测量仪长按清零累计数据(Ah/Wh)");
      Serial.println("\n===== 测试流程结束 =====");
      Serial.println("按回车键重新开始测试流程...");
      
      testPhase = 0;
    }
    return;
  }
}
```

将程序上传到 XIAO ESP32C3，打开串口监视器，根据提示信息进行记录。	·	3

### 测量结果与分析
由于电机启动电流较大导致开发板连接的不稳定性，我们采用了简化版测试程序，主要测量风扇关闭和全速运行两种状态下的功耗。在测试过程中，使用YZXstudio ZY1271的曲线记录功能捕获启动电流特性。

#### 1. 风扇启动特性测量
通过视频记录 YZXstudio ZY1271记录捕获风扇启动电流特性：

| 状态 | 电压 (V) | 电流 (A) | 功率 (W) | 备注 |
| --- | --- | --- | --- | --- |
| 静止状态 | 5.2179 | 0.0308 | 0.1607 | 基准功耗 |
| 启动峰值 | 5.1964 | 0.2019 | 1.0491 | 启动瞬间 |
| 稳定运行 | 5.2032 | 0.1499 | 0.7799 | 启动1秒后 |


风扇启动时表现出明显的电流峰值，这是由于直流电机启动时需要克服静态摩擦力和转子惯性，因此需要较大的启动转矩，对应较大的启动电流。这种启动电流峰值甚至导致开发板瞬间连接中断，证实了直流电机启动特性的高电流需求。

![](/images/week10/w10-g-15.jpg)

> 电机稳定全速转动时的读数
>

#### 2. 负载测试
通过轻微阻碍风扇叶片测试负载变化对功耗的影响：

| 负载状态 | 电压 (V) | 电流 (A) | 功率 (W) | 备注 |
| --- | --- | --- | --- | --- |
| 无负载 | 5.2041 | 0.1484 | 0.7722 | 自由旋转 |
| 轻微阻碍 | 5.2026 | 0.1610 | 0.8376 | 手指轻触风扇外壳 |
| 重度阻碍 | 5.1928 | 0.2425 | 1.2592 | 接近停转状态 |


当风扇负载增加时，电流也相应增加，这符合直流电机的工作特性。值得注意的是，重度阻碍状态下的电流值与启动峰值电流接近，这表明电机在这两种状态下都需要产生较大转矩。

![](/images/week10/w10-g-16.jpg)

> 无负载、轻微阻碍、重度阻碍 3 种状态的读数
>

### Grove迷你风扇测试结论
1. **启动特性**：风扇启动时有明显的电流峰值（约 202mA），比稳定运行电流（约150mA）高出约 35%，足以导致开发板供电不稳定。
2. **负载响应**：负载增加导致电流增加，电机试图通过增加转矩来维持转速。
3. **硬件设计考虑**：直流电机应用需要考虑启动电流对系统供电的影响，可能需要添加滤波电容等缓冲措施。



## 第三部分：GC9107 0.85" LCD 功耗测量
#### 线性电源简介
**Emkia 850 线性电源分析仪**

_主要规格参数_:

| 参数 | 规格 |
| --- | --- |
| 电压范围 | 0-12V DC |
| 电流范围 | 0-2A |
| 采样率 | 100KS/s |
| 测量精度 | ±0.5% + 2mV |
| 波形分析 | 真有效值 & 瞬态捕获 |
| 接口 | USB-B |


---

#### GC9107 0.85" LCD 简介
GC9107 是一款 0.85 英寸 LCD 屏幕，主要用于低功耗显示应用。

![](/images/week10/w10-g2-1.jpg)

#### 实验设置
+ **将 XIAO ESP32S3 VBAT 接出**，用于电源测量
+ **模拟 3.7V 电池供电**，确保实验条件接近实际应用
+ **测量空载功耗及接入 LCD 后的功耗**

![](/images/week10/w10-g2-2.jpg)

| XIAO ESP32S3 | Emkia 分析仪 |
| --- | --- |
| BAT+ (3.7V) | 红色端子 |
| BAT- (GND) | 黑色端子 |


**关键改动**:

1. 拆除 XIAO 开发板的电池连接器
2. 安装 2.54mm 排针以便于电源接入
3. 制作带有分流电阻的测试夹具

---

#### 测量结果与分析
##### 空载功耗（无 LCD）
![](/images/week10/w10-g2-3.jpg)
| 参数 | 值 |
| --- | --- |
| 平均电压 | 3.70V |
| 平均电流 | 54.71mA |
| 功耗 | 206.0mW |


##### 负载功耗（接入 LCD）
烧录测试程序，详情可以见

[1. Output Devices | Hongtai’s Fab Academy Journal](https://fabacademy.org/2025/labs/chaihuo/students/hongtai-liu/docs/assignments/week10/01-output-devices#12-lcd-display-module)

```c
#include "esp_err.h"
#include "esp_log.h"
#include "esp_check.h"

#include "openGlasses.h"

#include "lv_demos.h"

static const char *TAG = "app_main";

static lv_disp_t *lvgl_disp = NULL;

void app_main(void)
{
    lvgl_disp = bsp_lvgl_init();
    assert(lvgl_disp != NULL);

    // Lock the mutex due to the LVGL APIs are not thread-safe
    if (lvgl_port_lock(0))
    {
        lv_demo_benchmark();    /* A demo to measure the performance of LVGL or to compare different settings. */
        lvgl_port_unlock();
    }

    while (1)
    {
        printf("free_heap_size = %ld\n", esp_get_free_heap_size());
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
}
```

![](/images/week10/w10-g2-4.jpg)

| 参数 | 值 |
| --- | --- |
| 平均电压 | 3.70V |
| 平均电流 | 85.0mA |
| 功耗 | 304.0mW |


---

#### 结论
GC9107 0.85" LCD 的功耗约为 **96.0mW**，比空载功耗高出 **30%**。该功耗水平在可接受范围内，但在低功耗应用中仍需考虑优化策略。



## 参考资料
1. [Grove迷你风扇官方Wiki](https://wiki.seeedstudio.com/Grove-Mini_Fan/)
2. YZXstudio ZY1271 USB电压电流容量表用户手册
3. [Seeed Studio XIAO ESP32C3官方文档](https://wiki.seeedstudio.com/XIAO_ESP32C3_Getting_Started/)
4. [第10周课程大纲](http://academy.cba.mit.edu/classes/output_devices/index.html)
