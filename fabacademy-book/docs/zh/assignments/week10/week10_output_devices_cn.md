---
layout: doc
title: "第10周：输出设备 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第十周：学习各种输出设备的工作原理与应用，包括LED、显示屏、电机、执行器等硬件组件的控制与功耗测量"
head:
  - - meta
    - name: keywords
      content: fab academy, 输出设备, LED控制, 电机驱动, PWM, 显示屏, 执行器, 功耗测量, 电气安全, 电源管理
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第9周：个人作业2：为XIAO扩展板添加手势传感器'
  link: '/zh/assignments/week09/week09_individual_gesture_sensor_cn'
next:
  text: '第10周：小组作业：测量输出设备功耗'
  link: '/zh/assignments/week10/week10_group_power_measurement_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第10周：输出设备 (Output Devices)
> 本文档内容由我向 Claude 3.7 Sonnet 提供[课程大纲](http://academy.cba.mit.edu/classes/input_devices/index.html)内容，以及本课程视频会议课程的字幕后，由 AI 提炼生成。
>

## 课程概要
本周课程将介绍各种输出设备及其控制方法，为即将到来的机器构建课程做准备。我们将学习如何连接和控制LED、显示屏、电机和各种执行器，以及如何测量它们的功耗。这些知识对于Fab 2.0时代（从购买机器转向制造机器）至关重要。本课程涵盖的主要内容包括：

1. 电气安全基础知识
2. 电源管理与供电方式
3. 电流测量技术
4. LED控制（单色、RGB和阵列）
5. 显示屏（LCD、OLED、TFT）
6. 电机控制（直流电机、伺服电机、步进电机、无刷电机）
7. 其他执行器（扬声器、继电器、人造肌肉等）

## 详细的课程内容介绍
### 1. 电气安全
电气安全是处理输出设备时的首要考虑因素，特别是当我们开始使用更高电压和电流时：

+ **电流对人体的影响**：
    - ~1 mA：安全范围
    - ~10 mA：会感到电击，肌肉收缩
    - ~100 mA：可能导致心脏纤颤，致命
+ **人体电阻**：
    - 外部皮肤：兆欧级别
    - 内部组织：千欧级别
+ **绝缘击穿**：大约每毫米1千伏
+ **安全注意事项**：
    - 电源电容可能存储电量很长时间，需要安全放电
    - 感应反向电动势（如电机中）可产生高电压
    - 极性保护（二极管、MOSFET）
    - 电平转换（使用MOSFET）
    - 连接器极性设计
    - 电路保护元件的使用

处理电力电子设备的安全建议：不要在疲劳时单独工作；保持工作区整洁；心态平静专注；做好随时断电的准备。

参考链接：[电气安全基础](https://www.esfi.org/)

### 2. 电源管理
为输出设备提供足够的电力是关键，有多种供电方式可供选择：

#### USB供电
+ **USB PD（Power Delivery）**：允许USB提供更高电压和电流
+ **USB QC（Quick Charge）**：比USB PD更容易实现的专有协议
+ 可使用USB电源适配器、集线器和电池组
+ USB电源模块和电表可用于监控

#### 电源类型
+ **开关电源**：高效但可能噪声大
+ **线性电源**：噪声小但效率较低
+ **台式电源**：可调节电压和电流，适合测试

#### 电池
+ **锂聚合物电池（LiPo）**： 
    - 高能量密度，广泛使用
    - 需要专用充电控制器
    - 存在火灾风险，应妥善储存

#### 无线电源
+ 通过磁场在短距离内传输能量
+ 在长距离上传输功率较低

参考链接：

+ [电线规格指南](https://www.powerstream.com/Wire_Size.htm)
+ [USB电源传输](https://www.renesas.com/us/en/support/engineer-school/usb-power-delivery-02-fast-role-swap-programmable-power-supply)

### 3. 电流测量技术
了解输出设备的功耗对于选择合适的电源至关重要，有几种方法可以测量电流：

1. **使用台式电源**：直接读取电流表显示
2. **感测电阻法**：在电路中串联一个小电阻（如1欧姆），测量其两端电压降
3. **磁场传感**：测量导线周围的磁场强度
4. **电感检测**：对于交流负载，可以使用线圈感应变化的磁场

### 4. LED控制
#### 4.1 基本LED控制
+ LED需要限流电阻（计算方法：(电源电压-LED压降)/所需电流）
+ 可以使用Arduino或MicroPython控制
+ 示例代码（MicroPython）： 

```python
from machine import Pinimport timeled = Pin(2, Pin.OUT)  # 使用GPIO 2连接LEDwhile True:    led.value(1)  # 打开LED    time.sleep(0.5)    led.value(0)  # 关闭LED    time.sleep(0.5)
```

#### 4.2 PWM控制LED亮度
PWM（脉冲宽度调制）是控制LED亮度的重要技术：

+ 通过快速开关LED（使用高低脉冲）来控制亮度
+ 人眼会将快速闪烁感知为不同亮度
+ 占空比决定亮度（高占空比=亮，低占空比=暗）

示例代码（MicroPython）：

```python
from machine import Pin, PWM
import time

pwm = PWM(Pin(2))  # 在GPIO 2上创建PWM对象
pwm.freq(1000)     # 设置频率为1000Hz

# 渐变效果
while True:
    # 逐渐变亮
    for duty in range(0, 65535, 1024):
        pwm.duty_u16(duty)
        time.sleep(0.01)
    # 逐渐变暗
    for duty in range(65535, 0, -1024):
        pwm.duty_u16(duty)
        time.sleep(0.01)
```

#### 4.3 Charlieplexing（查理多路复用）
Charlieplexing是一种使用较少引脚控制多个LED的技术：

+ 利用三态逻辑（高、低、高阻）
+ 用n个引脚可以控制n(n-1)个LED
+ 每个引脚既可以作为行也可以作为列

工作原理：

+ 每次只点亮一个LED
+ 通过快速切换，人眼感知为所有LED同时亮起
+ 三种引脚状态：输出高、输出低、高阻态（输入模式）

示例应用：LED矩阵、条形图、指示器

#### 4.4 RGB LED控制
RGB LED包含红、绿、蓝三个LED，可以混合产生各种颜色：

+ 可以是共阳极或共阴极类型
+ 通过分别控制三个LED的亮度来混合颜色
+ 蓝色LED通常效率较低，需要不同的限流电阻

#### 4.5 高功率LED驱动
对于高功率LED：

+ 处理器引脚无法直接提供足够电流（通常限制在20-50mA）
+ 使用MOSFET作为开关元件
+ N沟道MOSFET用于低端开关，P沟道MOSFET用于高端开关
+ 可以将多个LED串联（总压降接近电源电压）以减少需要的限流电阻
+ 并联多组串联LED以增加总体亮度

### 5. 显示屏
#### 5.1 LCD显示屏
液晶显示器（LCD）的基本使用：

+ 常见HD44780控制器
+ 可使用并行或I2C通信（I2C需要额外适配器芯片如PCF8574）
+ 需要注意I2C需要上拉电阻（通常为1-10kΩ）

#### 5.2 OLED显示屏
有机发光二极管（OLED）显示器：

+ 常用SSD1306控制器
+ 可通过I2C或SPI通信
+ 比LCD更亮、对比度更好、更低功耗
+ 常见分辨率为128×64像素

MicroPython示例代码：

```python
from machine import Pin, I2C
import ssd1306

# 创建I2C对象
i2c = I2C(0, scl=Pin(22), sda=Pin(21))

# 创建OLED对象
oled = ssd1306.SSD1306_I2C(128, 64, i2c)

# 显示文本
oled.text("Hello World!", 0, 0)
oled.show()

# 绘制图形
oled.rect(10, 20, 30, 30, 1)  # 矩形
oled.show()
```

#### 5.3 TFT显示屏
薄膜晶体管（TFT）显示器：

+ 常用控制器包括ILI9341、ST7735等
+ 通过SPI通信
+ 支持全彩色显示，分辨率更高
+ 适用于需要显示更复杂图形和UI的场景

#### 5.4 电子墨水显示屏（E Ink）
+ 非易失性（断电仍保持显示内容）
+ 刷新率较低
+ 极低功耗，适合电池供电设备
+ 在强光下可读性好

### 6. 电机控制
#### 6.1 直流电机（DC Motor）
直流电机基础：

+ 需要H桥驱动以实现正反向控制
+ H桥基本原理：四个开关（通常为MOSFET）组成的电路
+ 可以使用集成H桥芯片（如DRV8251A、TB67H451、A4950等）

H桥操作模式：

+ 正向：左上和右下开关闭合
+ 反向：右上和左下开关闭合
+ 制动：上两个或下两个同时闭合
+ 滑行：所有开关断开

使用集成H桥芯片的优势：

+ 内置电荷泵（生成高电压开关信号）
+ 具有比较器（用于硬件PWM）
+ 内置保护功能（过流、过热保护）

注意事项：

+ 电源去耦（使用不同容值的电容）
+ 使用粗电路走线以处理大电流
+ 热设计（散热）

#### 6.2 扬声器控制
扬声器可以视为特殊的电机，也可以使用H桥驱动：

+ 使用PWM产生音调
+ 波表合成（Wavetable Synthesis）可以产生更复杂的声音
+ 可以播放预录音频样本

代码示例（音调生成）：

```python
from machine import Pin, PWM
import time

speaker = PWM(Pin(15))
speaker.freq(440)  # 设置频率为440Hz（A音）
speaker.duty_u16(32767)  # 50%占空比

time.sleep(1)  # 播放1秒
speaker.duty_u16(0)  # 停止声音
```

#### 6.3 伺服电机（Servo Motor）
伺服电机控制：

+ 标准伺服使用50Hz PWM信号
+ 脉冲宽度通常在1-2ms之间
+ 1ms = 0度，2ms = 180度（具体值取决于伺服型号）
+ 连续旋转伺服可以用作轮子（脉冲宽度控制方向和速度）

代码示例：

```python
from machine import Pin, PWM
import time

servo = PWM(Pin(15))
servo.freq(50)  # 伺服标准频率为50Hz

# 转到0度
servo.duty_ns(1000000)  # 1ms脉冲
time.sleep(1)

# 转到90度
servo.duty_ns(1500000)  # 1.5ms脉冲
time.sleep(1)

# 转到180度
servo.duty_ns(2000000)  # 2ms脉冲
```

#### 6.4 无刷直流电机（BLDC）
无刷电机的优势与控制：

+ 更高效率、更低噪音、更长寿命
+ 通常有三相绕组
+ 需要三个半桥驱动（或三相H桥）
+ 常用电子调速器（ESC）来控制
+ ESC接受与伺服相同的PWM信号

无刷电机的种类：

+ 内转子（Inrunner）
+ 外转子（Outrunner）
+ 盘式（Pancake）
+ 云台电机（Gimbal）

#### 6.5 步进电机（Stepper Motor）
步进电机控制：

+ 典型步进角为1.8度（200步/圈）
+ 需要两个H桥或专用步进驱动器
+ 支持全步、半步和微步进
+ 步进/方向接口简化控制

驱动器选项：

+ 使用两个H桥（如DRV8251A）
+ 专用步进电机驱动器（如DRV8428）
+ 商业驱动模块（如Trinamic、Pololu产品）

微步进的优势：

+ 更平滑的运动
+ 更低的噪音
+ 更高的精度

### 7. 其他执行器
#### 7.1 固态继电器
用于控制交流负载：

+ 通过光耦合器实现电气隔离
+ 切换高压端（热端）以确保安全
+ 可用于控制家用电器、加热元件等

#### 7.2 人造肌肉与软执行器
创新的执行器技术：

+ 形状记忆合金（SMA）
+ 使用钓鱼线制作的人造肌肉
+ 压电材料
+ 软机器人执行器
+ 气动/液压系统

这些技术可以提供独特的运动特性，适用于需要模仿自然运动的应用。

## 作业要求
### 小组作业
测量输出设备的功耗：

1. 选择一个输出设备（LED、显示屏、电机等）
2. 使用适当的方法测量其功耗（USB电源计、感测电阻、电源显示等）
3. 记录不同操作条件下的功耗变化（如不同速度的电机、不同亮度的LED）
4. 分析结果并讨论对系统设计的影响

### 个人作业
将输出设备添加到你设计的微控制器板上，并编程使其工作：

1. 选择一个与你最终项目相关的输出设备
2. 将其正确连接到你之前设计的微控制器板上
3. 编写程序控制该设备
4. 记录过程并展示结果（包括电路设计、代码和演示视频）

## 学习资源
### 电气安全
+ [电气安全基础知识](https://www.esfi.org/)
+ [反极性保护电路设计](https://www.monolithicpower.com/designing-a-reverse-polarity-protection-circuit-part-i)
+ [逻辑电平转换基础](https://www.digikey.com/en/blog/logic-level-shifting-basics)
+ [电路保护元件](https://www.digikey.com/en/products/category/circuit-protection/9)

### 电源管理
+ [电线规格指南](https://www.powerstream.com/Wire_Size.htm)
+ [USB电源传输](https://www.renesas.com/us/en/support/engineer-school/usb-power-delivery-02-fast-role-swap-programmable-power-supply)
+ [锂离子和锂聚合物电池指南](https://learn.adafruit.com/li-ion-and-lipoly-batteries?view=all)
+ [无线电源传输研究](https://science.sciencemag.org/content/317/5834/83.full)

### LED控制
+ [查理多路复用技术](http://inventory.fabcloud.io/static/docs/components/Charlieplexing.pdf)
+ [NeoPixel/WS2812B资料](https://github.com/adafruit/Adafruit_NeoPixel)
+ [MicroPython中的NeoPixel库](https://docs.micropython.org/en/latest/library/neopixel.html)

### 显示屏
+ [HD44780 LCD控制器](http://inventory.fabcloud.io/static/docs/datasheets/44780.pdf)
+ [SSD1306 OLED控制器](http://inventory.fabcloud.io/static/docs/datasheets/SSD1306.pdf)
+ [ILI9341 TFT控制器](http://inventory.fabcloud.io/static/docs/datasheets/ILI9341.pdf)
+ [U8g2显示库](https://github.com/olikraus/u8g2/)

### 电机控制
+ [集成电路H桥](https://www.allegromicro.com/en/Products/Motor-Driver-And-Interface-ICs.aspx)
+ [德州仪器电机驱动器](http://www.ti.com/motor-drivers/overview.html)
+ [Trinamic电机控制产品](https://www.trinamic.com/products)
+ [Pololu运动控制模块](https://www.pololu.com/category/9/motion-control-modules)

### 创新执行器
+ [使用钓鱼线制作人造肌肉](https://www.instructables.com/Fabricating-Fishing-Line-Artificial-Muscle-at-Home)
+ [哈佛大学软机器人研究组](https://gmwgroup.harvard.edu/soft-robotics)
+ [形状变化纤维研究](https://news.mit.edu/2023/shape-shifting-fiber-can-produce-morphing-fabrics-1026)

### 推荐实例项目
+ [Lingdong的OLED输出设备项目](https://fab.cba.mit.edu/classes/863.21/CBA/people/lingdong/site/16-output-device.html) - 展示了OLED显示屏的多种高级应用，包括3D渲染和菜单系统

