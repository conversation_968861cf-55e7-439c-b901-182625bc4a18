---
layout: doc
title: "第12-13周：机械与机器设计 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第12-13周机械与机器设计个人作业：小组协作完成机械结构和自动化机器的设计与实现，涵盖机械原理、结构搭建、运动控制、分工协作等内容。"
head:
  - - meta
    - name: keywords
      content: fab academy, 个人作业, 机械设计, 机器设计, 机械结构, 自动化, 小组协作, 运动控制, 机械原理
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第11周：个人作业 - 双节点手势控制智能网络系统'
  link: '/zh/assignments/week11/week11_individual_gesture_network_system_cn'
next:
  text: '第12周：Fab生态系统课程讲义'
  link: '/zh/assignments/week12/week12_fab_ecosystem_lecture_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 机械与机器设计 (Mechanical & Machine Design)

## 课程概要
本节课程重点讲解机械设计和机器自动化的核心原理和实践方法。课程分为两大部分：第一部分介绍机械机构(mechanism)的设计原理和关键组件；第二部分探讨将机械转变为自动化机器所需的驱动、控制和集成技术。本周作业为小组任务，要求学生合作设计并制造一台功能性机器，展示从机构设计到自动化控制的全过程。

## 详细课程内容
### 一、基础概念与原理
#### 1. 机器的定义
一个完整的机器包含四个关键元素：

+ 机构(Mechanism)：实现运动的结构部分
+ 驱动(Actuation)：提供运动的动力源
+ 自动化(Automation)：控制运动的系统
+ 应用(Application)：机器的实际用途

#### 2. 材料力学基础
+ 应力-应变曲线：材料在受力时的行为特性
    - 应力(Stress)：外力作用，"stress sounds like press"
    - 应变(Strain)：材料响应，"strain sounds like pain"
    - 线性区域：材料可恢复形变
    - 非线性区域：塑性流动，材料永久变形
    - 失效点：材料强度极限
+ 重要参数：
    - 刚度(Stiffness)：曲线斜率
    - 强度(Strength)：失效点
    - 韧性(Toughness)：曲线下面积

#### 3. 结构设计原则
+ 约束与自由度：根据James Clark Maxwell的理论
    - 结构需要适当约束以防止不必要的运动
    - 同时保留所需的运动自由度
    - 例如：书架若无交叉支撑会摇晃，加入支撑后稳定

#### 4. 间隙与回程误差
+ 回程误差(Hysteresis)与间隙(Backlash)：
    - 机械连接处的间隙导致方向变化时的位置不精确
    - 如螺母在螺纹上正反向旋转时的位置偏差

#### 5. 力环路
+ 力环路(Force Loops)：机器中力传递的路径
    - 例如铣床中从切削工具到工件的所有连接部分
    - 精度取决于整个力环路中的误差累积
    - 设计原则：最小化力环路以减少误差累积

#### 6. 运动学耦合
+ 运动学耦合(Kinematic Coupling)：高精度定位方法
    - 利用球体和槽的组合实现微米级精度定位
    - 三个球与三个槽只有一个稳定位置，确保一致定位

#### 7. 精度与准确度
+ 精度(Precision)：重复性，数值集中程度
+ 准确度(Accuracy)：与目标值的接近程度
+ 理想机器应同时具备高精度和高准确度

### 二、机器材料与组件
#### 1. 常用材料
+ 塑料：高密度聚乙烯(HDPE)等
+ 金属：铝型材等
+ 橡胶与泡沫：用于能量吸收
+ Garolite(环氧玻璃布层压板)：易于加工的PCB材料
+ 木材：不推荐用于精密机器(受温度湿度影响变形)
+ 水泥：提供刚度和质量
+ 陶瓷：用于硬度要求高的部件

#### 2. 紧固件
+ 螺母与螺栓：
    - 平垫圈(Plain Washer)：分散负载
    - 锁紧垫圈(Lock Washer)：防止松动
    - 锁紧螺母(Lock Nut)：内含弹性体防松动
+ 其他紧固方式：
    - 热嵌入螺母(Heat Set Inserts)：3D打印件中的螺纹连接
    - 铆钉(Rivets)：快速永久连接
    - 定位销(Pins)：防止部件移动，限制行程

#### 3. 机架设计
+ 型材框架：
    - 铝型材：成本低廉，刚度高
    - T型槽：便于安装其他组件
    - 配件系统：滑动螺母、角连接件等
+ 自对准连接：
    - 机加工零件可设计自对准特性
    - 通过卡扣式连接实现零件准确定位
    - 优点：可拆卸，装配误差小

### 三、传动系统
#### 1. 齿轮系统
+ 渐开线齿轮(Involute Gear)：
    - 最常见的齿轮形式
    - 特点：啮合时只有一点接触，运动平滑
    - 设计需使用齿轮生成器获取准确齿形
+ 其他齿轮类型：
    - 摆线齿轮(Cycloidal Gear)：加工更容易但效率较低
    - 斜齿轮(Helical Gear)：运转更平稳、噪音更小
    - 人字齿轮(Herringbone Gear)：自对准，消除轴向力
+ 减速系统：
    - 行星齿轮(Planetary Gear)：紧凑型减速装置
    - 谐波齿轮(Harmonic Drive)：高减速比，用于机器人关节

#### 2. 线性传动
+ 齿条齿轮(Rack and Pinion)：
    - 将旋转运动转换为直线运动
    - 可自行加工制作，长度可定制
+ 丝杠传动(Lead Screw)：
    - 普通丝杠存在回程误差
    - 防隙螺母(Anti-backlash Nut)：弹簧预载减少间隙
    - 滚珠丝杠(Ball Screw)：循环滚珠降低摩擦，运动更平滑
+ 无螺纹线性驱动：
    - 利用三个头部在硬化轨道上的稳定位置实现线性运动
+ 皮带传动：
    - 内部加强的同步带不可拉伸但可弯曲
    - 用于分布机器中的力
+ 绞盘驱动(Capstan Drive)：
    - 使用金属丝或渔线作为传动元件
    - 通过张紧和缠绕增加摩擦力
    - 优点：形状灵活，容易定制

#### 3. 导向系统
+ 导向轴(Guide Shafts)：硬化钢棒用于滑动引导
+ 导轨(Guide Rails)：线性轴承在轨道上运动
+ 滑块(Slides)：低精度应用的简易导向

#### 4. 轴承与联轴器
+ 轴承类型：
    - 旋转轴承(Rotary Bearing)：最常见
    - 推力轴承(Thrust Bearing)：承受垂直力
    - 线性轴承(Linear Bearing)：直线运动
    - 转盘轴承(Turntable)：旋转负载
    - 滑动轴承(Sleeve Bearing)：小负载应用
+ 轴承预载(Preload)：
    - 给轴承施加轻微轴向力
    - 确保球与槽良好接触，减少噪音和抖动
+ 联轴器(Couplers)：
    - 连接电机与传动组件
    - 能适应轻微的角度偏差
    - 减少振动和噪音

### 四、机构设计
#### 1. 柔性机构
+ 弹性铰链(Flexures)：
    - 利用材料弹性实现平滑运动
    - 适用于有限行程的精密应用
    - 例如：开源显微镜的精细调焦机构

#### 2. 弹性传动
+ 弹性串联(Series Elastic)：
    - 电机与负载间增加弹簧元件
    - 控制力而非位置，实现更平滑的运动
    - 类似人体肌肉工作原理

#### 3. 连杆机构
+ 各种连杆组合：转换不同类型的运动
+ 潘托图(Pantograph)：放大或缩小运动

#### 4. 特殊机构
+ Delta机器人：三个线性运动合成3D空间运动
+ 六足机器人(Hexapod)：六个线性执行器实现包括倾斜在内的全方位运动
+ CoreXY：两个固定电机协同控制XY平面运动
    - 优点：移动质量小，速度快
    - 原理：两电机同向旋转移动X轴，反向旋转移动Y轴
+ 折叠机构(SARS)：利用折叠板实现线性运动
+ 绞盘悬挂机构(Hang Printer)：通过线缆定位实现3D空间运动
+ 艺术与机构设计：
    - Chuck Hoberman的可展机构
    - Theo Jansen的风力驱动生命机械兽

### 五、机器自动化
#### 1. 驱动与控制
+ 电机类型：
    - 步进电机(Stepper Motor)
    - 伺服电机(Servo Motor)
    - 减速电机(Geared Motor)
+ 控制方式：
    - 开环控制(Open Loop)：无反馈，依靠电机步数估计位置
    - 闭环控制(Closed Loop)：利用编码器反馈实际位置

#### 2. 控制理论
+ 简单控制(Bang-Bang Control)：
    - 仅开关控制，运动不平滑
    - 加速和减速突兀，容易过冲
+ PID控制：
    - 比例(Proportional)：误差修正
    - 积分(Integral)：累积误差修正
    - 微分(Derivative)：抑制快速变化
+ 模型预测控制(Model Predictive Control)：
    - 利用机器模型预测未来行为
    - 可采用物理模型或机器学习训练的模型
    - 适用于复杂、高精度要求的系统

#### 3. 机器网络
+ 集中式控制：单一控制器管理所有电机和传感器
    - 优点：简单
    - 缺点：扩展性差，修改困难
+ 分布式控制：
    - 将机器各部分连入实时网络
    - 每个电机独立控制，通过网络协调
    - 优点：模块化，易于扩展和修改

#### 4. 机器指令与接口
+ G代码：传统数控机床指令
    - 历史悠久但仍广泛使用
    - 解释器将G代码转换为电机控制信号
+ 用户界面：
    - 功能：可视化G代码、编辑指令、实时控制、设置原点
    - 开源选项：UGS、CNC.js、Chili Pepper、Candle等
+ 路径规划：
    - 从设计文件到机器指令的转换
    - 包括边缘检测、工具直径补偿、转向控制等步骤

### 六、机械模块化设计
+ 模块化概念：
    - 独立功能模块组合成不同机器
    - 可互换端部效应器(End Effector)
+ 运动学耦合在工具更换中的应用：
    - 通过定位球和槽实现精确工具更换
    - 一台机器可执行多种功能(打印、切割、铣削等)

### 七、开源机器案例
+ Fab Labs中的开源机器：
    - Rumbo：简化机器控制，电机直接连接USB
    - Modular Things：基于实时网络的模块化系统
    - Jubilee：带工具更换的模块化机器
    - Fellow Machines：开源机器系列
    - Open Lab Starter Kit：完整的开源Fab Lab机器套件
+ 商业衍生项目：
    - Shaper Tools、Ultimaker、Form Labs等
    - 源自Fab Lab的创业公司

## 作业要求
本周作业为小组任务，要求设计并制造一台功能性机器。具体要求：

1. 机器设计与自动化：
    - 设计机械机构并实现自动化控制
    - 先手动操作测试机构，再添加电机控制
2. 团队合作：
    - 分工协作(机构设计、电机控制、末端执行器、用户界面等)
    - 可以是每个实验室一台机器，或多个实验室协作
3. 文档记录：
    - 创建小组页面记录整个机器项目
    - 个人页面记录自己的贡献部分
    - 包含演示视频和详细说明
4. 成果展示：
    - 准备简短演示(约1分钟)
    - 计划在两周后的课程中进行全球展示

## 学习资源
### 供应商与材料
1. 机械零件供应商：
    - McMaster-Carr: [https://www.mcmaster.com](https://www.mcmaster.com) (文档齐全)
    - Stock Drive Products: [https://sdp-si.com](https://sdp-si.com) (工业供应商)
    - Amazon (多种工业供应商)
    - Misumi: [https://us.misumi-ec.com](https://us.misumi-ec.com) (机械组件)
    - Taobao (中国地区)
2. 电机与控制：
    - 步进电机与伺服电机供应商
    - 控制器：PLUS、Rumba、Modular Things等

### 参考项目
1. 机械参考：
    - 肯塔基实验室的时钟项目
    - Nadia和Jonathan的自对准接头
    - Yen的自制齿条齿轮
    - Quentin的绞盘驱动
2. 开源机器项目：
    - 参考：[https://mtm.cba.mit.edu/machines/](https://mtm.cba.mit.edu/machines/)
3. 控制系统参考：
    - G代码解释器：GRBL: [https://github.com/gnea/grbl](https://github.com/gnea/grbl)
    - 用户界面：
        * Universal Gcode Sender: [https://winder.github.io/ugs_website/](https://winder.github.io/ugs_website/)
        * CNC.js: [https://cnc.js.org](https://cnc.js.org)
        * Chili Pepper
        * Candle: [https://github.com/Denvi/Candle](https://github.com/Denvi/Candle)
    - 路径规划：Mods: [https://mods.cba.mit.edu](https://mods.cba.mit.edu)
4. 创意参考：
    - Joseph的"糟糕机器"
    - Simone Giertz的创意机器: [https://www.youtube.com/c/simonegiertz](https://www.youtube.com/c/simonegiertz)

### 深入学习资源
+ 机构学：
    - 507 Mechanical Movements: [http://507movements.com](http://507movements.com)
    - 设计运动学模型数字图书馆: [https://digital.library.cornell.edu/collections/kmoddl](https://digital.library.cornell.edu/collections/kmoddl)
+ 控制系统：
    - PID控制指南: [https://cllom.gitlab.io/mynotes/PID](https://cllom.gitlab.io/mynotes/PID)
    - 模型预测控制（MPC）: [https://ocw.mit.edu/courses/16-323-principles-of-optimal-control-spring-2008/resources/lec16/](https://ocw.mit.edu/courses/16-323-principles-of-optimal-control-spring-2008/resources/lec16/)
+ Fab Labs开源机器项目：
    - MTM Snap: [https://mtm.cba.mit.edu/2011/2011_mtm-snap/mtm_snap-lock/index.html](https://mtm.cba.mit.edu/2011/2011_mtm-snap/mtm_snap-lock/index.html)
    - Jubilee: [https://jubilee3d.com](https://jubilee3d.com)
    - Modular Things: [https://github.com/modular-things/modular-things](https://github.com/modular-things/modular-things)
+ 工业设计参考：
    - Open Source Ecology: [https://www.opensourceecology.org](https://www.opensourceecology.org)
    - RepRap Project: [https://reprap.org](https://reprap.org)

通过这些资源，学生可以设计和构建自己的机器，从简单的机械机构到复杂的自动化系统，实现Fab Labs"制造自己的工具"的核心目标。
