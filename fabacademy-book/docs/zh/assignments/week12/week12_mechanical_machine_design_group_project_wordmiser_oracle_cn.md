---
layout: doc
title: "第12周：机械与机器设计小组项目：跨境 AI 写字机 - The WordMiser Oracle | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第12周机械与机器设计小组项目，介绍跨境 AI 写字机（The WordMiser Oracle）的设计、实现与团队协作过程。"
head:
  - - meta
    - name: keywords
      content: fab academy, Fab Lab, 机械设计, 机器设计, AI写字机, 跨境协作, SenseCAP Watcher, MQTT, Arduino, Gcode
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: 'Fab生态系统课程讲义'
  link: '/zh/assignments/week12/week12_fab_ecosystem_lecture_cn'
next:
  text: '第12周：机械与机器设计小组项目：跨境 AI 写字机 - The WordMiser Oracle 个人贡献'
  link: '/zh/assignments/week12/week12_wordmiser_oracle_personal_contribution_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 机械与机器设计小组项目：跨境 AI 写字机 - "The WordMiser Oracle"

本周的小组作业见：[https://fabacademy.org/2025/labs/chaihuo/docs/week12/week12_group_assignment/](https://fabacademy.org/2025/labs/chaihuo/docs/week12/week12_group_assignment/)

<iframe width="560" height="315" src="https://www.youtube.com/embed/sVUsoqN4CzQ" frameborder="0" allowfullscreen></iframe>

## 项目概述
"The WordMiser Oracle"（惜字如金预言机）是一个跨境协作项目，将深圳柴火创客空间的 SenseCAP Watcher AI 助手与澳门科学馆的机械写字机连接起来，创造了一个独特的交互体验。

项目的核心理念是创建一个"惜字如金"的 AI 预言机，它必须将回答限制在25个字符内，并由机械写字机物理书写出来。这种设计旨在提醒人们在信息爆炸的时代，精炼的表达往往比冗长的内容更有价值。

项目通过 MQTT 协议实现两地之间的实时通信：

+ **深圳端**：使用 SenseCAP Watcher AI 助手接收用户问题并生成简短回答
+ **澳门端**：设计机械写字机接收 AI 生成的文本并将其物理书写在纸上

![](/images/week12/w12-g-1.png)

> The WordMiser Oracle 的系统结构图
>

### 团队成员
+ [**郭俊傑 (Chon Kit Kuok)**](https://fabacademy.org/2025/labs/chaihuo/students/chonkit-kuok/docs/week12/week12_mechanical_machine_design/): 负责写字机硬件与程序设计
+ [**陈朗维 (Long Wai Chan)**](https://fabacademy.org/2025/labs/chaihuo/students/longwai-chan/docs/week_assignment/week12/machine_design/): 负责机械结构设计
+ [**刘洪泰 (Hongtai Liu)**](https://fabacademy.org/2025/labs/chaihuo/students/hongtai-liu/docs/assignments/week12/01-individual-contribution/): 负责 SenseCAP Watcher AI 编程
+ [**冯磊 (Lei Feng)**](/zh/assignments/week12/week12_wordmiser_oracle_personal_contribution_cn)：负责 MQTT 文本到 G-code 转换程序设计

## 第一阶段：机器设计与构建
在第一阶段，我们的目标是构建基本的写字/绘图机器，具备接收文本或图像并进行书写/绘制的功能。

### 机器结构选择
经过研究比较不同类型的机器结构后，我们选择了**笛卡尔坐标机器人结构**（Cartesian coordinate robot structure），因为 X 和 Y 轴的控制是分离的，便于控制和编程。

### 硬件组件
为了节省时间专注于改进和应用，我们尽可能使用实验室中现有的资源组装机器：

| 组件 | 规格 | 数量 |
| --- | --- | --- |
| 铝型材 | 20 × 20 × 25cm | 2 |
| 铝型材 | 20 × 40 × 35cm | 2 |
| 铝型材 | 20 × 40 × 29.7cm | 2 |
| 步进电机 | Nema 17 | 3 |
| 微控制器 | Arduino Nano | 1 |
| 步进电机驱动器 | A4988 | 2 |
| Arduino CNC 扩展板 | Rev2.7 | 1 |


![](/images/week12/w12-g-2.jpg)

> 组装写字机的硬件
>

Machine design_v1
<iframe src="https://a360.co/4cJhU4b" width="700" height="500"></iframe>


### 固件与控制
我们使用 GRBL 作为控制 CNC 机器的固件。GRBL 是一个流行的开源固件，用于使用 Arduino 控制 CNC 机器。它可以解释 G-code命令并相应地控制步进电机。

#### 上传 GRBL 固件的步骤
1. 从 GitHub 下载 GRBL 固件
2. 将库添加到 Arduino IDE
3. 上传固件到 Arduino 开发板

#### 连接 CNC 扩展板
按照连接图，我们将步进电机、舵机和限位开关连接到 CNC 扩展板，然后连接 12V 电源驱动步进电机。

![](/images/week12/w12-g-3.jpg)

>  G-code 通过 PC 的 USB 发送到 Arduino 开发板，再通过 CNC 扩展板控制步进电机
>

### 机器测试
我们使用 [Universal G-Code Sender ](https://winder.github.io/ugs_website/)(UGS) 软件测试机器。UGS 是一个免费的 CNC 控制器软件，兼容 GRBL、TinyG、Smoothieware 和 G2core 等多种固件选项。

![](/images/week12/w12-g-4.jpg)

> UGS 软件的界面
>

通过 UGS，我们可以：

+ 连接机器
+ 手动控制机器移动
+ 运行 G-code文件
+ 调整移动分辨率

#### G-code测试
我们测试了一些基本的 G-code命令：

+ G00：快速定位（通常用于起点定位）
+ G01：线性插值（进给）
+ G21：公制系统
+ G28：返回参考点
+ M03：顺时针启动主轴（在本项目中用于放下笔）
+ M05：关闭主轴（在本项目中用于抬起笔）

#### 图像转换为 G-code
我们使用 `G-codePlot` 扩展将图像矢量转换为 G-code。这是一个用于将图像矢量转换为 G-code的 Inkscape 扩展。

![](/images/week12/w12-g-5.jpg)

> 使用 `G-codePlot` 扩展将图像转为 G-code
>

然后，我们在机器上测试相关G-code能否运行。
![](/images/week12/w12-g-6.jpg)

>  在UGS介面上的G-code模拟
>

[test_drawing.mp4](/images/week12/test_drawing.mp4)

![](/images/week12/w12-g-7.jpg)

> 验证了我们设计的机器可以透过 Serial 串口接收 G-code 后画出图形，接下来我们将针对 G-code 传输的部份进行探讨。
>

## 第二阶段：无线通信实现

### MQTT 通信协议
由于在尝试使用开发板直接连接到 Arduino Nano 时遇到了电压不匹配的问题（ESP32 的电压为 3.3V，而 Arduino Nano 的电压为 5V），我们决定建立 MQTT 服务，在深圳柴火创客空间和澳门科学馆之间传输数据。

![](/images/week12/w12-g-8.svg)

> SenseCAP Watcher 通过 MQTT 协议将回答的文本内容发送到 MQTT 服务器
>

### SenseCAP Watcher AI 编程
SenseCAP Watcher 设备作为 AI 助手，其主要硬件规格如下：

+ MCU：ESP32 系列芯片
+ 显示：1.45英寸圆形 LCD
+ 音频输入：数字麦克风
+ 音频输出：扬声器
+ 输入设备：多功能滚动按钮
+ 通信接口：Wi-Fi / BLE / MQTT

我们将 XIAOZHI AI 框架移植到 SenseCAP Watcher 上，这是一个轻量级 AI 框架，支持：

+ 离线语音识别
+ 本地推理和命令解析
+ 访问在线大型语言模型
+ MQTT 通信扩展

#### MQTT 配置
我们配置了 MQTT 客户端：

+ 服务器地址：`broker.emqx.io`
+ 主题：`fablab/chaihuo/machine/text`
+ 端口：`1883`
+ 认证（用户名/密码）：无

#### AI 响应处理
AI 生成响应后，我们将其包装成标准格式，然后通过 MQTT 发布到特定主题。

### MQTT 文本到 G-code转换
冯磊开发了 MQTT 文本到 G-code的转换程序，主要功能模块包括：

1. **MQTT 客户端模块**：
    - 连接到 MQTT 服务器
    - 订阅 `fablab/chaihuo/machine/text` 主题接收来自深圳端的文本消息
    - 实现断线重连、连接状态监控和错误处理
2. **文本到 G-code转换模块**：
    - 基于 Stypox/text-to-G-code 库适配和优化
    - 包含完整的字符映射和绘制路径定义
    - 生成带有抬笔(z-up)和落笔(z-down)命令的完整 G-code序列
3. **GRBL 通信模块**：
    - 实现与 GRBL 控制器的串口通信
    - 处理命令队列管理和发送节流
    - 适配非标准 GRBL 响应模式
4. **主程序流程控制**：
    - 整合以上模块，形成完整的工作流程
    - 管理程序状态和错误处理
5. **配置与命令行界面**：
    - 通过 `config.yaml` 文件提供完整的配置选项
    - 支持精确控制笔的高度参数和进给速率

MQTT G-code转换程序的 GitHub 仓库：[MQTT-text-G-code-GRBL](https://github.com/mouseart/MQTT-text-Gcode-GRBL)

运行项目程序，如果收到 MQTT 的内容“Life is a journey of growth. ”，在编程环境的输出区可以看到以下的内容：

```cpp

--- MQTT Message Received ---
Topic: fablab/chaihuo/machine/text
Raw Payload: b'Life is a journey of growth.'
Decoded Text Payload: Life is a journey of growth.
Checking Serial Port: ser=Exists, is_open=True
Converting text to G-code...
Warning: Character '.' not found in definitions. Skipping.
Processing 377 G-code line(s) generated from text:
[G-code->GRBL] G0 F1500.0 X0.00 Y6.04        
[GRBL<-] ok
[G-code->GRBL] M03 S150
[GRBL<-] ok
[G-code->GRBL] G1 F1500.0 X0.00 Y0.00
[GRBL<-] ok
[G-code->GRBL] G1 F1500.0 X3.56 Y0.00
[GRBL<-] ok
[G-code->GRBL] M05 F1500.0
[GRBL<-] ok
    
...Omit intermediate content
    
[GRBL<-] ok
[G-code->GRBL] G1 F1500.0 X122.24 Y0.00
[GRBL<-] ok
[G-code->GRBL] M05 F1500.0
[GRBL<-] ok
[G-code->GRBL] G0 F1000.0 X123.74 Y0.00
[GRBL<-] ok
Finished processing G-code from message.
Sending post-message G-code (input did not end with newline)...
[G-code->GRBL] G1 F500.0 X0.00 Y-8.00
[GRBL<-] ok
Successfully sent post-message command: G1 F500.0 X0.00 Y-8.00
--- End MQTT Message Processing ---
Disconnected from MQTT broker with reason code Unspecified error
Successfully connected to MQTT broker with reason code Success
Subscribed to topic: fablab/chaihuo/machine/text

```

## 项目演示
项目工作流程如下：

1. 在深圳柴火创客空间，用户通过 SenseCAP Watcher 向 AI 提问：What's the meaning of life?

![](/images/week12/w12-g-9.jpg)



2. AI 生成简短回答（限制在 25 个字符内）：“Life is a journey of growth.”并通过 MQTT 发送。

![](/images/week12/w12-g-10.jpg)

3. 澳门科学馆的程序接收文本并转换为 G-code。

![](/images/week12/w12-g-11.jpg)

4. 写字机执行 G-code指令，将回答物理书写在纸上。

![](/images/week12/w12-g-12.jpg)

## 项目总结与反思
"The WordMiser Oracle" 项目不仅是一次技术挑战，更是一次关于"如何在数字时代重新思考通信价值"的探索。通过物理书写这种相对"慢"的媒介，我们希望提醒人们在这个信息爆炸的时代，有时"少即是多"，精炼的表达可能比冗长的内容更有价值。

项目成功地实现了跨境技术协作，将 AI、物联网和机械控制技术融合在一起，创造了一个独特的交互体验。

### 未来改进方向
1. 支持更多字符和符号的绘制
2. 优化绘写路径算法，提高书写速度和质量
3. 开发更友好的用户界面，增强系统的可用性
4. 增加更多错误恢复机制，提高系统在复杂环境下的稳定性
5. 改善機身硬件的結構，增大書寫範圍

## 个人贡献
### [郭俊傑 (Chon Kit Kuok)](https://fabacademy.org/2025/labs/chaihuo/students/chonkit-kuok/docs/week12/week12_mechanical_machine_design/)
+ 写字机硬件与程序设计
+ MQTT 数据接收器

### [陈朗维 (Long Wai Chan)](https://fabacademy.org/2025/labs/chaihuo/students/longwai-chan/docs/week_assignment/week12/machine_design/)
+ 机械结构设计
+ 硬件连接与升级

### [刘洪泰 (Hongtai Liu)](https://fabacademy.org/2025/labs/chaihuo/students/hongtai-liu/docs/assignments/week12/01-individual-contribution/)
+ SenseCAP Watcher AI 编程

### [冯磊 (Lei Feng)](/zh/assignments/week12/week12_wordmiser_oracle_personal_contribution_cn)
+ MQTT 文本到 G-code转换程序设计 
    - 文本到 G-code转换
    - MQTT 传输
+ 项目视频导演与后期制作

## 参考资源
+ GRBL: [https://github.com/robottini/grbl-servo](https://github.com/robottini/grbl-servo)
+ Universal G-Code Sender: [https://winder.github.io/ugs_website/](https://winder.github.io/ugs_website/)
+ G-codePlot: [https://github.com/arpruss/G-codeplot](https://github.com/arpruss/gcodeplot)
+ SenseCAP Watcher: [https://www.seeedstudio.com/watcher](https://www.seeedstudio.com/watcher)
+ XIAOZHI AI: [https://github.com/78/xiaozhi-esp32](https://github.com/78/xiaozhi-esp32)
+ MQTT-text-G-code-GRBL: [https://github.com/mouseart/MQTT-text-G-code-GRBL](https://github.com/mouseart/MQTT-text-Gcode-GRBL)
