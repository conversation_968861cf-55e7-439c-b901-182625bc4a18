---
layout: doc
title: "第12周：机械与机器设计小组项目：The WordMiser Oracle 个人贡献 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第12周机械与机器设计小组项目个人贡献，详述在跨境 AI 写字机项目中负责的 MQTT 文本到 Gcode 转换程序设计。"
head:
  - - meta
    - name: keywords
      content: fab academy, MQTT, Gcode转换, 写字机, 跨境协作, 机械设计, Arduino, Python, 串口通信
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第12周：机械与机器设计小组项目：跨境 AI 写字机 - The WordMiser Oracle'
  link: '/zh/assignments/week12/week12_mechanical_machine_design_group_project_wordmiser_oracle_cn'
next:
  text: '第14周：模具制作与铸造'
  link: '/zh/assignments/week14/week14_molding_and_casting_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# "The WordMiser Oracle" - 跨境 AI 写字机项目个人贡献报告

## 项目概述
"The WordMiser Oracle"是一个跨境协作项目，将深圳柴火创客空间的 SenseCAP Watcher AI 助手与澳门科学馆的机械写字机连接起来，创造了一个独特的交互体验。这个项目的核心理念是创建一个"惜字如金"的 AI 预言机，它必须将回答限制在30个字符内（我们实际测试后调整为 25 个字符内），并由机械写字机物理书写出来。

项目通过 MQTT 协议实现两地之间的实时通信：

+ 深圳端：使用 SenseCAP Watcher AI 助手接收用户问题并生成简短回答
+ 澳门端：设计机械写字机接收 AI 生成的文本并将其物理书写在纸上

本周的小组作业见：[https://fabacademy.org/2025/labs/chaihuo/docs/week12/week12_group_assignment/](https://fabacademy.org/2025/labs/chaihuo/docs/week12/week12_group_assignment/)

我们的团队成员包括:

+ 郭俊傑(Chon Kit Kuok)：负责写字机硬件与程序设计
+ 陈朗维(Long Wai Chan)：负责机械结构设计
+ 刘洪泰(Hongtai Liu)：负责 SenseCAP Watcher AI 编程
+ 冯磊(Lei Feng)：负责 MQTT 文本到 Gcode 转换程序设计

## 我的任务目标
作为项目团队成员，我的主要职责是开发 MQTT 文本到 Gcode 的转换程序，具体目标包括：

1. 设计并实现一个能够从 MQTT 服务器接收文本消息的程序
2. 开发算法将接收到的文本转换为 GRBL 控制器可识别的 Gcode 指令
3. 确保生成的 Gcode 能够准确控制写字机执行书写动作
4. 实现深圳端与澳门端的稳定通信，确保系统的实时性和可靠性
5. 编写完整的项目文档和使用说明

## 开发过程
### 使用 Windsurf 工具辅助开发
在开发过程中，我充分利用了 [Windsurf](https://windsurf.com/editor) 工具，结合 AI 技术大幅提高了开发效率。Windsurf 允许我通过自然语言描述需求，直接生成功能代码，同时提供代码解释和优化建议。

#### 开发流程：
1. 需求分析与规划：
    - 明确定义程序需要完成的功能
    - 确定使用 Python 作为主要开发语言
    - 规划程序结构和关键模块
2. 通过 AI 辅助编程：
    - 向 Windsurf 描述 MQTT 客户端需求："设计一个 Python 程序，能够连接到 MQTT 服务器并订阅特定主题，接收来自AI助手的消息"
    - 向 Windsurf 描述文本到 Gcode 转换的需求："开发一个函数，将接收到的文本转换为适合 XY 写字机的 Gcode 指令，包括笔的抬起和落下动作"
    - 向 Windsurf 描述 GRBL 通信需求："实现一个函数，通过串口将生成的 Gcode 指令发送到 GRBL 控制器"
3. 代码整合与优化：
    - 整合AI生成的各个模块
    - 基于开源库 [Stypox/text-to-gcode](https://github.com/Stypox/text-to-gcode/tree/master) 改进文本到G代码的转换逻辑
    - 优化错误处理和异常情况
    - 添加日志记录功能便于调试

![](/images/week12/w12-p-1.png)

> 第一次尝试用 Windsurf 的 AI 做这个比较复杂点的项目，Windsurf 允许你通过自然语言的方式与 AI 沟通直接生成和修改项目的相关文件
>

### 解决 GRBL 通信难题
开发过程中最具挑战性的部分是解决 GRBL 控制器的通信问题。通过与 AI 的多轮对话，我采用了系统化的故障排除方法：

1. 问题识别：
    - 初始时，我们发现脚本在发送`$X`命令后卡住，没有接收到任何响应
    - 使用`ser.readline()`方法无法获取到任何数据，脚本一直处于阻塞状态
2. 诊断分析：
    - 与AI协作，我们分析了可能的原因：GRBL未响应、硬件通信问题、固件异常等
    - 确定需要进行手动串口测试以直接判断问题根源
3. 代码调整：
    - 修改代码使用`ser.read()`替代`ser.readline()`，不再依赖换行符
    - 发现GRBL返回非常规的点(`.`)字符而非标准的"ok"或"error"响应
    - 尝试跳过`$X`解锁命令，但问题依然存在
4. 根源分析：
    - 确认问题不是特定命令导致，而是 GRBL 控制器对任何命令都只回复点字符
    - 怀疑可能是波特率不匹配、固件异常或硬件问题
    - 通过手动串口测试确认了控制器的实际状态和响应方式
5. 最终解决：
    - 通过修改波特率和通信参数
    - 调整代码以适应非标准的 GRBL 响应模式
    - 建立了稳定可靠的通信机制

这一过程不仅解决了具体的技术难题，也展示了如何系统化地进行硬件通信问题的排查和解决。

### 项目实现细节
最终完成的程序包含以下主要功能模块：

1. **MQTT 客户端模块**： 
    - 使用Paho MQTT客户端库连接到MQTT服务器
    - 订阅`fablab/chaihuo/machine/text`主题接收来自深圳端的文本消息
    - 实现断线重连、连接状态监控和错误处理
    - 支持 QoS 级别设置和保留消息处理
2. **文本到 G 代码转换模块**： 
    - 基于[ Stypox/text-to-gcode](https://github.com/Stypox/text-to-gcode) 库适配和优化
    - 包含完整的字符映射和绘制路径定义
    - 支持英文字母、数字和基本符号
    - 实现文本分析和坐标变换，将文字转换为可执行的绘图路径
    - 生成带有抬笔(z-up)和落笔(z-down)命令的完整G代码序列
3. **GRBL 通信模块**： 
    - 使用 PySerial 库实现与 GRBL 控制器的串口通信
    - 处理不同波特率和通信参数配置
    - 实现命令队列管理和发送节流
    - 适配了非标准 GRBL 响应模式
    - 包含超时处理和错误恢复机制
    - 添加了详细的调试日志输出，记录所有通信过程
4. **主程序流程控制**： 
    - 整合以上三个模块，形成完整的工作流程
    - 初始化 GRBL 控制器，设置正确的工作模式(G90绝对坐标模式、G21毫米单位)
    - 处理 MQTT 消息回调，触发文本到G代码的转换
    - 管理程序状态和错误处理
    - 实现优雅的启动和关闭过程
5. **配置与命令行界面**： 
    - 通过`config.yaml`文件提供完整的配置选项，使用 YAML 格式清晰组织各项配置
    - MQTT 配置支持自定义代理服务器(broker)、端口、主题和可选的认证信息
    - GRBL 配置包括串口设备选择、波特率设置(115200)和缓冲区大小(128字节)
    - 提供丰富的初始化命令集，包括解锁($X)、单位设置(G21)、定位模式(G90)等
    - 支持自定义加工后的位置移动(`post_message_command`)
    - 文本转G代码配置支持自定义字符文件目录、行长度、行间距和内边距等参数
    - 支持精确控制笔的高度参数(上笔高度5.0mm、下笔深度-1.0mm)和进给速率
    - 提供命令行输出，实时显示程序状态、MQTT 连接状态和 GRBL 通信过程

仓库还包含详细的 README 文档，提供了安装指南、使用说明和故障排除指南，帮助用户快速部署和使用这一系统。

项目完整代码和文档已上传至GitHub：[MQTT-text-Gcode-GRBL](https://github.com/mouseart/MQTT-text-Gcode-GRBL)，欢迎其他Fab Academy学员和创客社区的成员参考和改进。

运行项目程序，如果收到 MQTT 的内容“Life is a journey of growth. ”，在编程环境的输出区可以看到以下的内容：

```cpp

--- MQTT Message Received ---
Topic: fablab/chaihuo/machine/text
Raw Payload: b'Life is a journey of growth.'
Decoded Text Payload: Life is a journey of growth.
Checking Serial Port: ser=Exists, is_open=True
Converting text to G-code...
Warning: Character '.' not found in definitions. Skipping.
Processing 377 G-code line(s) generated from text:
[GCODE->GRBL] G0 F1500.0 X0.00 Y6.04        
[GRBL<-] ok
[GCODE->GRBL] M03 S150
[GRBL<-] ok
[GCODE->GRBL] G1 F1500.0 X0.00 Y0.00
[GRBL<-] ok
[GCODE->GRBL] G1 F1500.0 X3.56 Y0.00
[GRBL<-] ok
[GCODE->GRBL] M05 F1500.0
[GRBL<-] ok
    
...Omit intermediate content
    
[GRBL<-] ok
[GCODE->GRBL] G1 F1500.0 X122.24 Y0.00
[GRBL<-] ok
[GCODE->GRBL] M05 F1500.0
[GRBL<-] ok
[GCODE->GRBL] G0 F1000.0 X123.74 Y0.00
[GRBL<-] ok
Finished processing G-code from message.
Sending post-message G-code (input did not end with newline)...
[GCODE->GRBL] G1 F500.0 X0.00 Y-8.00
[GRBL<-] ok
Successfully sent post-message command: G1 F500.0 X0.00 Y-8.00
--- End MQTT Message Processing ---
Disconnected from MQTT broker with reason code Unspecified error
Successfully connected to MQTT broker with reason code Success
Subscribed to topic: fablab/chaihuo/machine/text

```

## 项目演示
我还负责编写脚本和剪辑制作了完整的项目演示视频，展示了" The WordMiser Oracle "的全部功能和工作流程。视频中可以看到：

1. 在深圳柴火创客空间，用户通过 SenseCAP Watcher 向AI提问
2. AI 生成简短回答并通过 MQTT 发送
3. 我的程序接收文本并转换为 Gcode
4. 澳门科学馆的写字机执行 Gcode 指令，将回答物理书写在纸上

完整的双语视频脚本已经整理完成，将作为最终项目文档的一部分。
<iframe width="560" height="315" src="https://www.youtube.com/embed/sVUsoqN4CzQ" frameborder="0" allowfullscreen></iframe>

## 总结与反思
参与开发"The WordMiser Oracle"项目是一次非常有价值的经验。通过这个项目，我不仅锻炼了跨境协作的能力，还深入学习了 MQTT 通信、Gcode 生成和机器控制的相关知识。

使用 Windsurf 工具和 AI 辅助编程极大地提高了开发效率，尤其是在解决硬件通信问题时。AI 不仅提供了代码生成，更重要的是帮助我系统化地分析问题、提出解决方案并验证结果。这种人机协作的开发方式体现了现代创客精神与AI技术的完美结合。

遇到的最大挑战是 GRBL 控制器的非标准响应问题，这促使我深入理解了串口通信原理和 GRBL 固件的工作机制。通过系统化的排查和多次尝试，最终找到了可靠的解决方案，这一经验对未来的硬件相关项目非常宝贵。

未来，我计划进一步优化这个项目：

1. 支持更多字符和符号的绘制
2. 优化绘写路径算法，提高书写速度和质量
3. 开发更友好的用户界面，增强系统的可用性
4. 增加更多错误恢复机制，提高系统在复杂环境下的稳定性

这个项目不仅是一次技术挑战，更是一次关于"如何在数字时代重新思考通信价值"的探索。通过物理书写这种相对"慢"的媒介，我们希望提醒人们在这个信息爆炸的时代，有时"少即是多"，精炼的表达可能比冗长的内容更有价值。

