---
layout: doc
title: "第11周：网络与通信技术 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第十一周：学习网络通信基础知识及其在制造项目中的应用，包括有线通信协议、无线通信技术和网络架构设计"
head:
  - - meta
    - name: keywords
      content: fab academy, 网络通信, 串行通信, SPI, I2C, WiFi, 蓝牙, ESP32, 节点网络, 模块化设计, 通信协议
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第10周：个人作业：手势控制迷你风扇与LED'
  link: '/zh/assignments/week10/week10_individual_gesture_fan_led_cn'
next:
  text: '第11周：个人作业：双节点手势控制智能网络系统'
  link: '/zh/assignments/week11/week11_individual_gesture_network_system_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 网络与通信技术
## 课程概要
本周课程讲解了网络通信的基础知识及其在制造项目中的应用。我们学习了各种有线通信协议(如串行通信、SPI、I2C等)和无线通信协议(如WiFi、蓝牙等)。课程强调了网络通信不仅用于远距离连接，还可以作为系统架构的重要组成部分，用于实现模块化、并行处理和干扰隔离。

## 详细的课程内容介绍
### 1. 网络通信的基本概念与用途
网络通信在项目中的作用不仅限于远距离连接设备，还有以下几个重要目的：

+ **位置连接**：连接不同物理位置的设备
+ **并行处理**：通过多处理器分担任务，每个处理器负责特定功能
+ **模块化设计**：允许系统按模块分解，便于独立开发、测试和重新配置
+ **干扰隔离**：将高电压/高电流部分与敏感电路分开，减少电气干扰

### 2. 有线通信技术
#### 2.1 异步串行通信
异步串行通信是最基本的通信方式，通过高低电平变化传输数据。

**常见标准**：

+ RS-232、RS-422、RS-485等

**三种常用的串行通信方法**：

1. **串行广播**：
    - 使用一个主机和多个节点
    - 每个节点具有唯一地址
    - 主机向所有节点广播，节点识别自己的地址并响应
    - 代码实现：使用软件"bit-banging"可以在任何引脚上实现，不需要专用硬件
2. **跳数计数法(Hop Count)**：
    - 节点串联连接（输出连接到下一个节点的输入）
    - 消息经过每个节点时增加计数值
    - 末端节点自动识别并发送返回消息
    - 适用于像LED条这样的设备，不需要预设地址
3. **广播跳数混合法(Broad Hop)**：
    - 结合广播和跳数计数的优点
    - 发送时使用广播（快速到达所有节点）
    - 接收时使用跳数（识别节点位置）
    - 适合有一个主控设备的网络架构

#### 2.2 同步串行通信
同步串行通信使用单独的数据线和时钟线，时钟线指示何时读取数据，有更好的同步性能。

**两种主要的同步串行协议**：

1. **SPI (Serial Peripheral Interface)**：
    - 使用四条线：数据输出、数据输入、片选、时钟
    - 主要用于处理器与外设（如存储卡）之间的通信
    - 实例应用：SD卡接口 
        * 可以为项目添加GB级的非易失性存储
        * 使用FAT文件系统格式化
        * 通过库函数简化访问
2. **I2C (Inter-Integrated Circuit)**：
    - 使用两条线：SCL（时钟）和SDA（数据）
    - 通过上拉电阻实现双向通信
    - 每个设备需要预设地址
    - 多用于连接传感器和显示器等外设

#### 2.3 异步无时钟协议
**ATP (Asynchronous Token Protocol)**：

+ 设计用于不需要共享时钟的通信
+ 使用令牌传递机制
+ 允许不同速度的处理器相互通信
+ 可以随时停止和恢复通信
+ 适用于实时系统和异构处理器

#### 2.4 其他有线通信协议
+ **USB**：通用串行总线，可以使用库实现各种USB配置文件（键盘、鼠标、MIDI设备等）
+ **以太网**：高速网络通信
+ **CAN、LIN、MODBUS、DMX**：用于工业、汽车和灯光控制系统

### 3. 无线通信技术
#### 3.1 无线通信基础
+ **无线电基本组件**：振荡器、混频器、功率放大器、低噪声放大器、中频、I/Q、解调器、基带处理器、滤波器
+ **天线知识**：谐振频率、天线增益、阻抗匹配
+ **单芯片无线电**：集成了所有无线电功能的芯片，简化设计

#### 3.2 WiFi和蓝牙通信
**ESP32系列**：

+ ESP32-C3（5美元左右）：支持WiFi和蓝牙
+ 通过MicroPython可以轻松实现： 
    - Web服务器功能
    - Web客户端功能
    - 蓝牙串口通信

**实际应用示例**：

1. **WiFi应用**：
    - 将处理器设置为接入点
    - 通过Web浏览器控制LED开关
    - 使用按钮发送消息到浏览器
2. **蓝牙应用**：
    - 使用手机应用或支持Web蓝牙的浏览器连接
    - 实现无线控制和数据传输
    - 可用于远程控制和手机接口

#### 3.3 其他无线技术
+ **RFID/NFC**：使用MFRC522模块实现近场通信，可用于标签识别
+ **ISM频段无线电**：如nRF24L01+，适用于简单的点对点通信
+ **LoRa**：长距离低功耗通信，适用于城市规模的网络

### 4. 网络协议与结构
+ **OSI七层模型**：从物理层到应用层的网络通信结构
+ **无线频率规范**：不同地区有不同的频率分配和功率限制
+ **调制技术**：PCM、FSK、PSK、QAM等不同的信号调制方式
+ **信道共享**：TDMA、FDMA、CSMA等多址接入技术
+ **Internet协议**：IP、TCP/UDP、HTTP等标准互联网协议

## 作业要求
### 个人作业：
设计、构建并连接有线或无线节点网络，要求：

1. 节点具有网络或总线地址
2. 节点具有本地输入和/或输出设备
3. 实现节点间通信
4. 可以将通信功能集成到最终项目中，作为模块化系统的一部分

### 小组作业：
在两个项目之间发送消息，实现跨项目通信。

## 学习资源
### 有线通信资源
1. **串行通信**：
    - [RS-232, RS-422, RS-485标准文档](https://pdfserv.maximintegrated.com/en/an/AN723.pdf)
    - [ATtiny412串行总线示例](http://fabacademy.org/2020/labs/leon/students/adrian-torres/week14.html#hello_serial_bus)
2. **I2C和SPI**：
    - [I2C规范](https://www.nxp.com/docs/en/application-note/AN10216.pdf)
    - [SPI应用笔记](http://ww1.microchip.com/downloads/en/AppNotes/Atmel-2585-Setup-and-Use-of-the-SPI_ApplicationNote_AVR151.pdf)
    - [SD卡规范](https://claude.ai/chat/SD/SD.pdf)和[FAT文件系统](https://claude.ai/chat/SD/FAT.pdf)
3. **USB通信**：
    - [TinyUSB库](https://github.com/hathach/tinyusb)
    - [CircuitPython HID库](https://docs.circuitpython.org/projects/hid/en/latest)
    - [Arduino HID库](https://www.arduino.cc/reference/en/libraries/hid-project)

### 无线通信资源
1. **WiFi和蓝牙**：
    - [ESP32-C3数据手册](https://www.espressif.com/en/products/socs/esp32-c3)
    - [ESP32-WROOM数据手册](https://claude.ai/chat/ESP32/esp32-wroom-32_datasheet_en.pdf)
    - [Web蓝牙API](https://developer.chrome.com/articles/bluetooth)
2. **RFID和其他无线技术**：
    - [MFRC522数据手册](https://www.nxp.com/docs/en/data-sheet/MFRC522.pdf)
    - [nRF24L01+数据手册](https://claude.ai/chat/nRF/nRF24L01.pdf)
    - [LoRa Alliance官网](https://lora-alliance.org/)
3. **天线和无线电基础**：
    - [ARRL手册](https://www.arrl.org/shop/ARRL-Handbook)（无线电基础知识）
    - [天线理论分析与设计](https://www.wiley.com/en-us/Antenna+Theory%3A+Analysis+and+Design%2C+4th+Edition-p-9781118642061)

### 网络协议资源
1. **Internet协议**：
    - [RFC文档库](https://www.rfc-editor.org/)
    - [IPv4规范](http://www.ietf.org/rfc/rfc0791.txt)和[IPv6规范](http://www.ietf.org/rfc/rfc2460.txt)
    - [HTTP规范](http://www.ietf.org/rfc/rfc2616.txt)
2. **Python网络编程**：
    - [Python socket库文档](http://docs.python.org/library/socket.html)
    - [UDP发送示例](https://claude.ai/chat/socket/udpsnd.py)和[UDP接收示例](https://claude.ai/chat/socket/udprcv.py)
3. **网络分析工具**：
    - [Wireshark](http://www.wireshark.org/)

通过掌握这些网络通信技术，你将能够设计更复杂、更模块化的项目，实现设备间的高效通信和协作。

