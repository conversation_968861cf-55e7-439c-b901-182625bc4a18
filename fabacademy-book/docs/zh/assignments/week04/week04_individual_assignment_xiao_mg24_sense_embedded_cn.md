---
layout: doc
title: "第4周：基于XIAO MG24的嵌入式开发实践 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第四周 XIAO MG24 Sense开发板嵌入式开发教程"
head:
  - - meta
    - name: keywords
      content: fab academy, XIAO MG24, 嵌入式开发, Arduino IDE, 单片机编程, IMU传感器
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第4周：Grove入门工具包与XIAO系列开发板'
  link: '/zh/assignments/week04/week04_group_assignment_grove_beginner_kit_xiao_cn'
next:
  text: '第4周：基于AI的XIAO MG24 Sense应用开发'
  link: '/zh/assignments/week04/week04_individual_assignment_ai_xiao_mg24_sense_app_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第 4 周个人作业：基于 XIAO MG24 的嵌入式开发实践

对于我的智幻走马灯最终项目，我想尝试较新推出的 [XIAO MG24 Sense](https://www.seeedstudio.com/Seeed-XIAO-MG24-Sense-p-6248.html)，这个拇指大小的开发板上整合了基于 Silicon Labs EFR32MG24 SoC 的超低功耗无线开发板，采用高性能78MHz ARM Cortex®-M33内核，板载模拟麦克风和六轴IMU传感器，让我的智幻走马灯在功能上有了很多想象空间。而价格只有不到 11 美元。

![画板](/images/week04/w04-xiao-mg24-1.jpg)

>  XIAO MG24 Sense 的正面和背面
>

## 一、微控制器数据手册分析
Seeed Studio 提供的 Datashee 是 [EFR32MG24 系列无线 SoC 的数据手册](https://www.silabs.com/documents/public/data-sheets/efr32mg24-datasheet.pdf)，由 Silicon Labs 发布，主要面向物联网无线连接应用。文档详细描述了产品的核心特性，如32位ARM Cortex-M33处理器、2.4GHz高性能无线收发器、低功耗设计以及安全功能（包括Secure Vault和AI/ML硬件加速器）。此外，文档还涵盖了产品的功能列表、订购信息、系统架构、无线电设计、各类外围接口（如GPIO、USART、I2C等）、时钟与电源管理、引脚定义以及封装规格，为工程师提供了完整的技术参数和应用指导，帮助他们在无线通信和物联网设备设计中做出最佳选择。

手册第一页展示了 EFR32MG24 芯片的整体架构图，如下图所示。

![](/images/week04/w04-xiao-mg24-2.png)

> EFR32MG24 芯片的整体架构图
>

其核心组件可分为以下几个主要部分：

核心系统部分（左上）

+ 采用ARM Cortex-M33处理器，支持DSP扩展、FPU浮点运算单元和 TrustZone 安全特性。
+ 包含Flash程序存储器、RAM内存和LDMA直接内存访问控制器。
+ 集成了AI/ML硬件加速器(MVP)用于机器学习加速。
+ 提供ETM和调试接口便于开发调试。

时钟管理系统（中上）  
提供多种时钟源选项：

+ 高频晶振和RC振荡器。
+ 快速启动RC振荡器。
+ 低频晶振和RC振荡器。
+ 超低频RC振荡器。

能源管理系统（右上）

+ 电压调节器。
+ DC-DC转换器。
+ 上电复位。
+ 欠压检测器。

安全功能（最右）

+ 加密加速器。
+ 真随机数生成器。
+ DPA防护措施。
+ 安全调试认证。
+ 安全引擎。

底部外设系统

+ 无线电子系统：包括发射接收前端、数字解调等。
+ 串行接口：USART、EUSART等通信接口。
+ I/O端口：支持键盘扫描、外部中断等。
+ 定时器系统：多种定时器和计数器。
+ 模拟接口：ADC、比较器等模拟外设。

该芯片支持5种能耗模式（底部）：  
EM0（活动）→ EM1（休眠）→ EM2（深度休眠）→ EM3（停止）→ EM4（关断），能耗逐级降低。

所有这些模块通过32位总线和外设反射系统互联，形成了一个完整的低功耗无线系统芯片解决方案。

我重点了解了下面几个部分的内容。

### 芯片概览
EFR32MG24系列芯片的核心是ARM® Cortex®-M33处理器，最高运行频率可达78 MHz。该处理器具备数字信号处理（DSP）指令集、浮点运算单元（FPU）和TrustZone安全技术，确保了高效的数据处理能力和系统安全性。芯片内置高达1536 kB的闪存和256 kB的RAM，为复杂的物联网应用提供了充足的存储空间。

### 无线通信能力
该系列芯片的无线模块支持2.4 GHz频段，具备高性能的无线电收发器，支持多种无线协议，包括Matter、OpenThread、Zigbee、Bluetooth Low Energy等。芯片的无线电部分具备高灵敏度和高输出功率，确保了在各种环境下的稳定通信。例如，其接收灵敏度可达-105.4 dBm（250 kbps O-QPSK），而输出功率最高可达+19.5 dBm。

### 低功耗设计
EFR32MG24系列芯片在设计上特别注重低功耗，具备多种低功耗模式，以适应不同的应用场景。在EM0模式下，芯片的电流消耗仅为33.4 µA/MHz；而在EM4模式下，电流消耗可低至0.25 µA。这种低功耗特性使得芯片非常适合电池供电的设备，延长了设备的使用寿命。

### 安全特性
安全是EFR32MG24系列芯片的一大亮点。芯片内置了Secure Vault安全模块，提供了包括硬件加密加速器、真随机数生成器（TRNG）、安全启动（Secure Boot）和安全调试（Secure Debug）等功能。这些安全特性确保了设备在面对远程和本地网络攻击时的防护能力。

### 外围接口与功能
EFR32MG24系列芯片提供了丰富的外围接口，包括GPIO、USART、EUSART、I2C、定时器等，支持多种通信协议和功能。例如，芯片的GPIO引脚具备多种功能，包括输入、输出、开漏配置和 glitch filtering。此外，芯片还集成了模拟比较器（ACMP）、数模转换器（VDAC）和模数转换器（IADC），满足了各种模拟信号处理的需求。

## XIAO MG24 Sense 的 Wiki 文档
Seeed Studio 随产品提供的 [XIAO MG24 Sense Wiki 文档](https://wiki.seeedstudio.com/xiao_mg24_getting_started/)，可以帮助用户快速了解产品如何使用。

Wiki 文档中的 XIAO MG24 Sense 功能模块示意图如下图所示：

![](/images/week04/mg24sense_pinlist.png)

> XIAO MG24 Sense indication diagram
>

XIAO MG24 Sense 的引脚图如下图所示：

![](/images/week04/modifyMG24.png)

> XIAO MG24(Sense) Pin List
>

## XIAO MG24 Sense 基本功能测试

这个板子对我来说也比较陌生，所以我想测试一下其基本功能，通常会从 Blink 开始（让板子上的 USER LED 闪烁），以此了解和测试整个开发环境可用。另外我还想探究板载的六轴加速度计，想看看如何读取加速度数据到串口监视器。

### XIAO MG24 Sense 的 Blink 教程
XIAO MG24 Sense 的 Wiki 文档提供了 Blink 的教程，我按照自己的操作习惯重新整理这个过程。

#### 一、硬件和软件准备
##### 1.硬件准备
+ **XIAO MG24 Sense** 开发板 × 1
+ **USB Type-C** 数据线 ×1
+ **电脑** ×1

##### 2. 软件准备
+ [**Arduino IDE**](https://www.arduino.cc/)：确保已下载并安装最新版本（推荐使用 v2.0 或以上版本）。

#### 二、设置开发板支持
##### 1.打开 Arduino IDE
启动 Arduino IDE，确保软件已正确安装。

##### 2.添加开发板支持
+ **Step 1：打开开发板管理器**

依次点击 `文件` (File) →`首选项` (Preferences)，在 `额外开发板管理器网址` (Additional Boards Manager URLs) 字段中，粘贴以下网址：

  `https://siliconlabs.github.io/arduino/package_arduinosilabs_index.json`。
  如果你需要经常对多个开发板编程，可以点击网址右侧的 ![](/images/week04/Arduino-IDE-Preferences-3.png)图标，在弹出的窗口中，将开发板管理器网址添加在最下方，如下图所示。
  
  点击 `确定` (OK) 保存设置。

![画板](/images/week04/w04-p1-arduino-1.png)

> 在 Arduino IDE 中添加开发板支持
>

+ **Step 2：安装开发板包**
    - 依次点击 `工具` (Tools) →`开发板` (Board) →`开发板管理器` (Boards Manager)。
    - 在搜索框中输入 **XIAO MG24** 进行搜索。
    - 找到 **Arduino Silicon Labs Boards by Silicon Labs**，点击 `安装` (Install) 按钮进行安装，如下图所示。
    - 安装完成后，关闭开发板管理器。

![](/images/week04/Arduino-IDE-4.png)
> 成功添加开发板支持后，在开发板管理的搜索框输入 XIAO MG24，就可以搜到安装包了
>


##### 3.选择开发板
+ 依次点击：`工具` (Tools) →`开发板` (Board) →`Silicon Labs Boards` →`XIAO MG24`。此时如果直接连接将 XIAO MG24 通过 Type-C 和电脑连接，会自动选择 XIAO MG24 开发板，如下图所示。

![](/images/week04/mg24sense_2.png)

> 成功安装开发包后，连接 XIAO MG24 到电脑，Arduino IDE 会自动识别并选择 XIAO MG24 开发板
>

#### 三、编译和上传 Blink 代码
##### 1.打开 Blink 示例代码
+ 依次点击：`文件` (File) →`示例` (Examples) →`01.基本示例` (01.Basics) →`Blink`。开启路径如下图所示。

![](/images/week04/Arduino-IDE-6.png)

> 打开 Blink 示例程序的路径
>

Blink 程序如下所：

```cpp
/*
  Blink

  Turns an LED on for one second, then off for one second, repeatedly.

  Most Arduinos have an on-board LED you can control. On the UNO, MEGA and ZERO
  it is attached to digital pin 13, on MKR1000 on pin 6. LED_BUILTIN is set to
  the correct LED pin independent of which board is used.
  If you want to know what pin the on-board LED is connected to on your Arduino
  model, check the Technical Specs of your board at:
  https://www.arduino.cc/en/Main/Products

  modified 8 May 2014
  by Scott Fitzgerald
  modified 2 Sep 2016
  by Arturo Guadalupi
  modified 8 Sep 2016
  by Colby Newman

  This example code is in the public domain.

  https://www.arduino.cc/en/Tutorial/BuiltInExamples/Blink
*/

// the setup function runs once when you press reset or power the board
void setup() {
  // initialize digital pin LED_BUILTIN as an output.
  pinMode(LED_BUILTIN, OUTPUT);
}

// the loop function runs over and over again forever
void loop() {
  digitalWrite(LED_BUILTIN, HIGH);  // turn the LED on (HIGH is the voltage level)
  delay(1000);                      // wait for a second
  digitalWrite(LED_BUILTIN, LOW);   // turn the LED off by making the voltage LOW
  delay(1000);                      // wait for a second
}

```

##### 2. 配置端口
+ 确保 XIAO MG24 Sense 已通过 USB Type-C 数据线连接到电脑。
+ 依次点击：`工具` (Tools) →`端口` (Port)，选择与 XIAO MG24 Sense 对应的端口。查看连接端口是否正确，如果不正确，则需手动选择，Windows 系统串行端口显示为“COM+数字”。而在 macOS 或者 Linux 系统中，串口名称一般为“/dev/tty.usbmodem+数字”或“ /dev/cu.usbmodem+数字”。

##### 3. 上传代码
+ 点击![](/images/week04/Arduino-IDE-upload.png) `上传` (Upload) 按钮（右侧的箭头图标）或按下快捷键 `Ctrl+U`，将代码上传到 XIAO MG24 Sense。

#### 四、观察结果
+ 上传成功后，XIAO MG24 Sense 板子上的 `LED 指示灯` 会开始闪烁，通常是以 1 秒为周期闪烁。如果看到指示灯闪烁，就表示成功点亮了！

![](/images/week04/blink-2-ok.gif)
> XIAO MG24 Sense 的 Blink 效果
>

+ 如果没有看到指示灯闪烁，可以检查以下几点：
    - 确保开发板已正确连接到电脑，并且选择了正确的端口。
    - 确保安装了正确的开发板支持包。
    - 尝试重新上传代码。

### XIAO MG24 Sense 的 IMU 测试教程
[Marcelo Rovai](https://github.com/Mjrovai) 教授在 [Anomaly Detection & Motion Classification](https://mjrovai.github.io/XIAO_Big_Power_Small_Board-ebook/chapter_4-2.html) 文档介绍了一个类似的开发板 [Seeed Studio XIAO nRF52840 Sense](https://www.seeedstudio.com/Seeed-XIAO-BLE-Sense-nRF52840-p-5253.html) 如何进行 IMU 测试，并提供了一个测试代码：[https://github.com/Mjrovai/Seeed-XIAO-BLE-Sense/blob/main/xiao_test_IMU/xiao_test_IMU.ino](https://github.com/Mjrovai/Seeed-XIAO-BLE-Sense/blob/main/xiao_test_IMU/xiao_test_IMU.ino)。

#### 修改 IMU 测试程序
这个代码对于 XIAO MG24 Sense 的 IMU 并不合适，主要原因是：

IMU 型号差异： 

+ XIAO nRF52840 Sense 使用的是 LSM9DS1 传感器
+ XIAO MG24 Sense 使用的是 LSM6DS3TR-C 传感器
+ 这两种传感器虽然都属于 LSM 系列，但硬件特性和接口不同

软件库差异： 

+ XIAO nRF52840 Sense 使用 Seeed Arduino LSM9DS1 库
+ XIAO MG24 Sense 需要使用 Seeed_Arduino_LSM6DS3 库
+ 两个库的 API 接口和初始化方式有所不同

功能支持范围： 

+ LSM9DS1 支持加速度计、陀螺仪和温度测量
+ LSM6DS3TR-C 主要支持加速度计和陀螺仪功能
+ 因此需要移除温度相关的测试代码

根据这些差异，我修改了测试程序如下：

```cpp
#include "LSM6DS3.h"
#include "Wire.h"

LSM6DS3 myIMU(I2C_MODE, 0x6A);  //I2C device address 0x6A

char cmd = ' ';  // 使用单个字符来存储命令

void setup() {
  Wire.begin();
  Serial.begin(115200);
  delay(1000);
  
  if (myIMU.begin() != 0) {
    Serial.println("Device error");
  } else {
    Serial.println("Device OK!");
  }

  Serial.println("\nAvailable commands:");
  Serial.println("a - display accelerometer readings");
  Serial.println("g - display gyroscope readings");
  Serial.println("s - stop readings");
}

void loop() {
  if (Serial.available()) {
    cmd = Serial.read();
  }
  
  if (cmd == 'a') {
    // 加速度计数据
    float x = myIMU.readFloatAccelX();
    float y = myIMU.readFloatAccelY();
    float z = myIMU.readFloatAccelZ();
    Serial.print(x);
    Serial.print("\t");  // 使用制表符分隔
    Serial.print(y);
    Serial.print("\t");
    Serial.println(z);
  }
  else if (cmd == 'g') {
    // 陀螺仪数据
    float x = myIMU.readFloatGyroX();
    float y = myIMU.readFloatGyroY();
    float z = myIMU.readFloatGyroZ();
    Serial.print(x);
    Serial.print("\t");
    Serial.print(y);
    Serial.print("\t");
    Serial.println(z);
  }
  else if (cmd == 's') {
    cmd = ' ';  // 停止输出
  }
  
  delay(100);  // 100ms 采样间隔
}
```

#### 安装 Seeed_Arduino_LSM6DS3 库
要运行上面的测试程序，需要安装 Seeed_Arduino_LSM6DS3 库，安装库有两种方法：

1. 通过 Arduino IDE 库管理器安装： 
    - 打开 Arduino IDE
    - 点击菜单栏：工具 -> 管理库
    - 在搜索框中输入 "Seeed LSM6DS3"
    - 找到 "Seeed Arduino LSM6DS3" 库
    - 点击 "安装" 按钮

下图是安装成功后的状态。

![](/images/week04/Seeed-lsm6ds3-install.png)

> 通过库管理搜索并安装 Seeed LSM6DS3 库
>

1. 通过 GitHub 下载安装： 
    - 访问 [https://github.com/Seeed-Studio/Seeed_Arduino_LSM6DS3](https://github.com/Seeed-Studio/Seeed_Arduino_LSM6DS3)
    - 点击 "Code" 按钮，选择 "Download ZIP"，如下图所示
    - 下载完成后，在 Arduino IDE 中
    - 点击菜单栏：项目 -> 加载库 -> 添加 .ZIP 库
    - 选择下载的 ZIP 文件
    - 等待安装完成

![](/images/week04/Seeed_Arduino_LSM6DS3-github.png)

> 通过 Github 下载 ZIP 库
>

安装完成后，你可以：

1. 通过菜单栏：文件 -> 示例 -> Seeed_Arduino_LSM6DS3 查看示例代码
2. 在代码中使用 `#include "LSM6DS3.h"` 来包含库文件

注意：安装库后需要重启 Arduino IDE 才能确保库被正确加载。

#### 上传 IMU 测试程序并观察结果
所需库安装成功后，在 Arduino IDE 中上传 IMU 测试代码，打开串口监视器，将波特率设置为：115200，可以看到下图所示的提示信息。

![](/images/week04/Arduino-IDE-6-2.png)

> 运行 IMU 测试程序后显示提示命令，等待输入
>

根据提示输入所需命令：

+ 输入 'a' 显示加速度计数据
+ 输入 'g' 显示陀螺仪数据
+ 输入 's' 停止数据显示

输入“a”可以看到 3 个轴的加速度数据在不断的输出。打开串口绘图仪，如下图所示，可以看到 3 轴加速度计数值的曲线图。

![画板](/images/week04/Arduino-IDE-7.png)

> 在 Arduino 中打开串口绘图仪
>

现在我们晃动 XIAO MG24，可以看到串口绘图仪会呈现 3 轴加速度的变化曲线。

![](/images/week04/IMU-test-2.gif)

> Arduino IDE 的串口绘图仪，展示晃动的 XIAO MG24 的 IMU 的 3 轴加速度的变化曲线
>

更详细的教程文档可以查看：[https://wiki.seeedstudio.com/xiao_mg24_sense_built_in_sensor/](https://wiki.seeedstudio.com/xiao_mg24_sense_built_in_sensor/)


## 使用 Wokwi 模拟嵌入式程序运行

### 一、模拟环境介绍
嵌入式系统的开发周期中，模拟是一个重要环节，它可以帮助我们在没有实际硬件的情况下验证程序逻辑，节省时间和成本。我将使用 [Wokwi](https://wokwi.com/) 针对XIAO MG24 Sense开发板进行加速度计模拟，由于 Wokwi 目前不直接支持该型号，我使用 XIAO ESP32C3 加外接加速度计作为替代方案进行模拟。

![](/images/week04/w4-wokwi-1.png)

> [Wokwi](https://wokwi.com/) 在线模拟平台
>

### 二、模拟环境设置
#### 创建Wokwi项目
1. 访问[Wokwi](https://wokwi.com/)在线模拟平台
2. 点击"ESP32"
3. 在“Starter Templates”中可以看到"XIAO ESP32-C3"
4. 如下图所示，点击"XIAO ESP32-C3"从示例项目开始

![](/images/week04/w4-wokwi-2.png)

> 在“Starter Templates”中可以看到"XIAO ESP32-C3"
>

进入 XIAO ESP32-C3 的示例项目后，可以直接点击仿真标签下的启动仿真按钮，可以看到 3 个 LED 轮流点亮的效果，如下图所示。

![](/images/week04/w4-wokwi-3.png)

> 运行轮流点亮 3 个 LED 的示例程序
>

由于我们需要模拟加速度计，可以点击选中这些不需要的元件，按删除键删除，如下图所示。

![](/images/week04/w4-wokwi-4.png)

> 逐个删除不需要的元件
>

#### 添加MPU6050加速度计
由于我们的主要目的是模拟IMU传感器的功能，我们需要添加MPU6050加速度计：

1. 在项目界面右侧，点击仿真标签的"+"按钮。
2. 在传感器栏中找到"MPU6050"。
3. 添加 MPU6050 元件到模拟项目，如下图所示。

![](/images/week04/w4-wokwi-5.png)

> 添加MPU6050元件到模拟项目
>

#### 连接硬件
根据XIAO ESP32C3和MPU6050的引脚定义，我们需要正确连接这两个组件：

| MPU6050引脚 | XIAO ESP32C3引脚 | 连接说明 |
| --- | --- | --- |
| VCC | 3V3 | 电源 |
| GND | GND | 接地 |
| SCL | D5 (GPIO7) | I2C时钟线 |
| SDA | D4 (GPIO6) | I2C数据线 |
| AD0 | GND | 设置I2C地址为0x68 |
| INT | 不连接 | 中断线（本例不使用） |


使用虚拟连线工具连接组件，如下图所示。

![](/images/week04/w4-wokwi-6.png)

> MPU6050与XIAO ESP32C3的连接示意图
>

### 三、调整代码以适应模拟环境
由于我们使用的是不同的硬件平台和传感器，需要调整代码以适应模拟环境。主要区别在于：

1. LSM6DS3TR-C 传感器替换为 MPU6050
2. 使用适合 MPU6050 的库和 API

#### IMU 测试程序的模拟版本
我修改了 IMU 测试程序如下所示：

```cpp
#include <Wire.h>
#include <MPU6050.h>

MPU6050 mpu;  // 创建MPU6050对象

char cmd = ' ';  // 使用单个字符来存储命令

void setup() {
  Wire.begin();
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("初始化MPU6050...");
  // 初始化MPU6050，设置陀螺仪和加速度计量程
  while(!mpu.begin(MPU6050_SCALE_2000DPS, MPU6050_RANGE_2G)) {
    Serial.println("无法找到有效的MPU6050传感器，请检查连接！");
    delay(500);
  }
  
  Serial.println("设备就绪!");
  Serial.println("\n可用命令:");
  Serial.println("a - 显示加速度计读数");
  Serial.println("g - 显示陀螺仪读数");
  Serial.println("s - 停止读数");
}

void loop() {
  if (Serial.available()) {
    cmd = Serial.read();
  }
  
  if (cmd == 'a') {
    // 读取加速度计数据
    Vector normAccel = mpu.readNormalizeAccel();
    
    Serial.print(normAccel.XAxis);
    Serial.print("\t");  // 使用制表符分隔
    Serial.print(normAccel.YAxis);
    Serial.print("\t");
    Serial.println(normAccel.ZAxis);
  }
  else if (cmd == 'g') {
    // 读取陀螺仪数据
    Vector normGyro = mpu.readNormalizeGyro();
    
    Serial.print(normGyro.XAxis);
    Serial.print("\t");
    Serial.print(normGyro.YAxis);
    Serial.print("\t");
    Serial.println(normGyro.ZAxis);
  }
  else if (cmd == 's') {
    cmd = ' ';  // 停止输出
  }
  
  delay(100);  // 100ms 采样间隔
}
```

### 四、运行模拟
将程序复制到左侧的程序区，点击顶部的"Start Simulation"按钮开始模拟。结果显示构建失败，如下图所示。

![](/images/week04/w4-wokwi-7.png)

> 初次运行模拟出现错误提示
>

#### 使用 Claude 3.7 解决问题
我尝试向 Claude 3.7 反馈了目前的状况和错误，并提供了当前的程序、diagram.json，错误提示文本，[MPU6050 的文档链接](https://docs.wokwi.com/parts/wokwi-mpu6050)，Claude 给出了修改后的程序。  
![](/images/week04/w4-wokwi-8.png)

> 向 Claude 3.7 提交问题的背景资料
>

Claude 给出的修改后的程序如下。

```cpp
#include <Wire.h>
#include <MPU6050.h>

MPU6050 mpu;
char cmd = ' ';

void setup() {
  Wire.begin();
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("初始化MPU6050...");
  
  // 初始化MPU6050
  mpu.initialize();
  
  if (mpu.testConnection()) {
    Serial.println("MPU6050连接成功!");
  } else {
    Serial.println("MPU6050连接失败，请检查接线!");
    while (1);
  }
  
  Serial.println("\n可用命令:");
  Serial.println("a - 显示加速度计读数");
  Serial.println("g - 显示陀螺仪读数");
  Serial.println("s - 停止读数");
}

void loop() {
  if (Serial.available()) {
    cmd = Serial.read();
  }
  
  if (cmd == 'a') {
    // 读取加速度计数据
    int16_t ax, ay, az;
    mpu.getAcceleration(&ax, &ay, &az);
    
    // 转换为g值 (1g = 16384 LSB)
    float x = ax / 16384.0;
    float y = ay / 16384.0;
    float z = az / 16384.0;
    
    Serial.print(x);
    Serial.print("\t");
    Serial.print(y);
    Serial.print("\t");
    Serial.println(z);
  }
  else if (cmd == 'g') {
    // 读取陀螺仪数据
    int16_t gx, gy, gz;
    mpu.getRotation(&gx, &gy, &gz);
    
    // 转换为度/秒 (1 度/秒 = 131 LSB)
    float x = gx / 131.0;
    float y = gy / 131.0;
    float z = gz / 131.0;
    
    Serial.print(x);
    Serial.print("\t");
    Serial.print(y);
    Serial.print("\t");
    Serial.println(z);
  }
  else if (cmd == 's') {
    cmd = ' ';  // 停止输出
  }
  
  delay(100);  // 100ms 采样间隔
}
```

将修改后的程序复制替换左侧的程序，再次尝试运行仿真，这次程序能够正常运行，在模拟的串口监视器上给出命令提示。可以通过串口监视器输入'a'来查看加速度数据，输入'g'来查看陀螺仪数据，输入 S-停止读数。

![](/images/week04/w4-wokwi-9.png)

> 顺利运行 AI 修改后的程序
>

#### 模拟传感器输入
Wokwi 提供了可视化的方式来模拟 MPU6050 的运动：

1. 点击模拟器中的 MPU6050 元件。
2. 使用出现的控制面板调整加速度和角度。
3. 观察串口监视器中数值的变化。

![](/images/week04/w4-wokwi-10.png)

MPU6050 传感器控制面板

### 五、模拟结果分析
#### 实际硬件与模拟环境的比较
| 方面 | 实际硬件 (XIAO MG24 Sense) | 模拟环境 (XIAO ESP32C3 + MPU6050) |
| --- | --- | --- |
| 处理器 | EFR32MG24 (ARM Cortex-M33) | ESP32-C3 (RISC-V) |
| IMU传感器 | LSM6DS3TR-C | MPU6050 |
| 传感器接口 | I2C | I2C |
| API差异 | LSM6DS3::readFloatAccelX() | MPU6050::readNormalizeAccel() |
| LED控制 | 直接支持板载LED | 需要使用外部LED或GPIO模拟 |


#### 功能等效性分析
尽管硬件平台存在差异，但模拟环境能够实现以下核心功能：

1. 基本的I2C通信
2. 加速度和陀螺仪数据的读取
3. 实时数据显示和分析

这些功能足以验证我们的程序逻辑是否正确，在实际硬件上可能只需要小幅调整API调用方式。

### 六、模拟的优势与局限性
#### 优势
+ 无需实际硬件即可测试代码逻辑
+ 提供可视化的传感器数据输入
+ 便于调试和优化
+ 快速验证算法正确性

#### 局限性
+ 硬件平台差异导致部分功能无法完全模拟
+ 无法测试特定硬件的性能特性
+ 某些中断和时序敏感操作可能与实际硬件有差异
+ 对于低功耗特性无法准确模拟

#### 参考链接
+ [Wokwi 模拟器官方文档](https://docs.wokwi.com/)
+ [MPU6050 传感器文档](https://docs.wokwi.com/parts/wokwi-mpu6050)
+ [XIAO ESP32C3 Wiki 文档](https://wiki.seeedstudio.com/XIAO_ESP32C3_Getting_Started/)

