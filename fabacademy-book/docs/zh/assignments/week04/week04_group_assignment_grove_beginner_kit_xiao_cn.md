---
layout: doc
title: "第4周：Grove入门工具包与XIAO系列开发板 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第四周 Grove入门工具包与XIAO系列开发板使用指南"
head:
  - - meta
    - name: keywords
      content: fab academy, Grove套件, XIAO开发板, 图形化编程, Arduino IDE, 嵌入式开发
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第4周：嵌入式编程'
  link: '/zh/assignments/week04/week04_embedded_programming_cn'
next:
  text: '第4周：基于XIAO MG24的嵌入式开发实践'
  link: '/zh/assignments/week04/week04_individual_assignment_xiao_mg24_sense_embedded_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 小组作业：Grove Beginner Kit 和 XIAO 系列课程

本周的小组作业见：[https://fabacademy.org/2025/labs/chaihuo/docs/week4/chaihuo/week4_group_assignment](https://fabacademy.org/2025/labs/chaihuo/docs/week4/chaihuo/week4_group_assignment)

柴火创客空间的小组作业主要针对嵌入式编程进行了实践探索。小组成员比较了不同的微控制器架构和开发工具，特别关注了XIAO系列开发板的应用与性能对比。刘鸿泰同学的部分工作提供了XIAO ESP32C3、XIAO ESP32S3和XIAO ESP32C6等不同型号的详细对比，涵盖了处理器性能、无线连接能力和功耗特性等关键参数，为选择合适的开发板提供了参考依据。

作为 [Seeed Studio](https://www.seeedstudio.com/) 的员工，我写过一些嵌入式开发入门的课程和书籍，在这里介绍给初学者。

## 图形化编程 Grove Beginner Kit For Arduino

对于完全没有代码编程经验的学习者，可以尝试从图形化编程开始，比如下面这个课程：[Grove Beginner Kit For Arduino : Codecraft Graphical Programming Course](https://files.seeedstudio.com/wiki/Grove-Beginner-Kit-For-Arduino/res/Grove-Beginner-Kit-For-Arduino-Codecraft-Graphical-Programming-Course-web-v7.pdf)。

![](/images/week04/w04-beginner-kit-course.png)

此课程旨在通过 [Codecraft](https://ide.tinkergen.com/) 图形化编程环境，帮助初学者掌握 [Grove Beginner Kit for Arduino](https://www.seeedstudio.com/Grove-Beginner-Kit-for-Arduino-p-4549.html) 的使用。该套件包含一个兼容 Arduino 的主板（使用 ATmega328P-MU）以及 10 个输入与输出模块，所有模块都通过 PCB 设计连接在一起，无需额外的 Grove 线缆。课程从基础开始，逐步引导学习者完成多个项目，涵盖从简单的 LED 控制到复杂的传感器应用。

![](/images/week04/w04-beginner-kit-course-2.png)

> Grove Beginner Kit for Arduino 的 10 个输入与输出模块

![画板](/images/week04/codecraft-1.jpg)

> 图形化编程工具 Codecraft 的首页

图形化编程的好处，你不需要写任何程序，编程过程像搭积木。让我们编写一个 Blink 程序，让 Grove Beginner Kit for Arduino 的的 LED 模块闪烁。

如下图，在 Start 栏目拖拽 setup （板子上电后执行一次的程序）和 loop（完成 setup 后重复执行的程序）块到编程区。

![](/images/week04/grove-beginner-kit-2.jpg)

> 拖拽 setup、loop 积木到编程区

然后分别添加 2 个 LED 积木，设置引脚为 D4，以及 ON、OFF 状态，并添加 2 个 Delay 积木，设置延时长度为 1 秒，程序如下图所示。

![](/images/week04/codecraft-2.png)

> Codecraft 的 Blink 程序

完成后，你只需要将 [Grove Beginner Kit for Arduino](https://www.seeedstudio.com/Grove-Beginner-Kit-for-Arduino-p-4549.html)  连接到电脑，然后点击 Upload 按钮，程序上传完成后，可以看到 LED 灯开始闪烁，如下图所示。

![](/images/week04/w04-beginner-kit-course-3.jpg)

> LED 灯被点亮

Codecraft 还有一个好处，就是可以将程序积木转换为 C 代码，如下图所示。如果刚开始学代码编程，可以先用积木完成编程，然后转为代码了解程序是如何编写的，这些代码可以直接复制到 Arduino IDE 这样的环境中去运行。

![画板](/images/week04/w04-beginner-kit-course-4.jpg)

> Codecraft 里将 Blink 积木块程序转为 C 代码

## 基于 Arduino IDE 的 Seeed Studio XIAO Series 系列课程

如果想尝试各种不同的 MCU 开发板，可以看看 [Seeed Studio XIAO Series](https://www.seeedstudio.com/xiao-series-page) 系列。XIAO 是一款超小型、高性能的开发板，尺寸仅为拇指大小，专为物联网和人工智能应用设计。该系列兼容Arduino，支持快速原型制作，适用于空间有限的项目。XIAO系列提供多种型号，满足不同需求，包括支持Wi-Fi、蓝牙、Zigbee等无线连接的版本，以及集成传感器和AI功能的版本。此外，XIAO系列还提供丰富的扩展板和配件，方便开发者进行各种创意项目。

柴火创客空间的刘鸿泰的小组作业，提供了这个系列[不同 XIAO 的对比](https://fabacademy.org/2025/labs/chaihuo/students/hongtai-liu/docs/assignments/week04/00-group-assignment/)。

我也为 XIAO 写过基于 Arduino IDE 的课程，如下图这个和 [Marcelo Rovai](https://github.com/Mjrovai) 教授合作撰写的：[XIAO: Big Power, Small Board，Mastering Arduino and TinyML](https://mjrovai.github.io/XIAO_Big_Power_Small_Board-ebook/)。

![](/images/week04/w04-xiao-book-1.jpg)

如果不想看那么长的教程，可以看我的个人作业部分：

[第 4 周个人作业文档：基于 XIAO MG24 Sense 的嵌入式开发实践](/zh/assignments/week04/week04_individual_assignment_xiao_mg24_sense_embedded_cn)
