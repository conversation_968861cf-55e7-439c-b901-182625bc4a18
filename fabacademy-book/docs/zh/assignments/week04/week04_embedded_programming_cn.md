---
layout: doc
title: "第4周：嵌入式编程 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第四周 嵌入式编程工具与方法学习指南"
head:
  - - meta
    - name: keywords
      content: fab academy, 嵌入式编程, 微控制器, <PERSON><PERSON><PERSON><PERSON>, 单片机, 硬件开发
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第3周个人作业：乙烯基切割'
  link: '/zh/assignments/week03/week03_vinyl_cutting_assignment_cn'
next:
  text: '第4周：Grove入门工具包与XIAO系列开发板'
  link: '/zh/assignments/week04/week04_group_assignment_grove_beginner_kit_xiao_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第4周：嵌入式编程

## 课程概述
欢迎来到嵌入式编程的世界！本课程将带你步入一个神奇的微型计算世界。想象一下，在你的指尖上放置一个只有指甲盖大小的芯片，这个小小的芯片里却蕴含着一个完整的计算机系统 - 这就是我们将要探索的微控制器。

本课程是电子设计和制造系列课程的基础，将为你打开嵌入式系统的大门。通过本课程，你将学习：

1. 如何选择合适的微控制器
2. 理解处理器架构和内存系统
3. 掌握基础编程技能
4. 学习调试技巧
5. 动手实践项目开发

### 为什么学习嵌入式编程？
在我们的日常生活中，嵌入式系统无处不在：

+ 智能家电中控制温度和时间的处理器
+ 可穿戴设备中监测健康数据的芯片
+ 工业控制系统中的自动化控制器
+ 甚至是简单的LED灯控制器

通过本课程，你将能够设计和开发这些令人着迷的系统。

## 第一部分：微控制器基础
### 认识微控制器
#### 什么是微控制器？
微控制器是一个集成了处理器核心、内存和输入输出接口的完整计算机系统。以一个售价仅50美分的ATtiny412微控制器为例，它包含：

+ 8位CPU核心
+ 程序存储器(Flash)
+ 数据内存(RAM)
+ 多个输入输出接口
+ 定时器
+ 模数转换器

这个小小的芯片就像一个微型工厂，可以感知环境、处理数据并控制其他设备。

### 处理器架构
#### 冯·诺依曼架构 vs 哈佛架构
这两种架构代表了计算机系统的两种基本设计理念：

**冯·诺依曼架构：**

+ 程序和数据存储在同一个内存空间
+ 优势：结构简单，硬件成本低
+ 缺点：可能出现程序和数据访问冲突
+ 应用：主要用于个人计算机

**哈佛架构：**

+ 程序和数据使用独立的存储空间和总线
+ 优势：可以同时访问指令和数据，效率更高
+ 应用：大多数微控制器采用此架构
+ 历史：源自哈佛大学早期计算机设计

#### RISC vs CISC
处理器的指令集架构决定了它如何执行命令：

**RISC (精简指令集计算机)：**

+ 指令数量少，简单直接
+ 大多数指令在一个时钟周期内完成
+ 功耗低，执行速度快
+ 代表：ARM处理器、RISC-V架构

**CISC (复杂指令集计算机)：**

+ 指令数量多，单条指令可完成复杂操作
+ 代码密度高，编程灵活
+ 功耗相对较高
+ 代表：x86处理器

## 第二部分：主要处理器系列
### 1. AVR系列 - 入门者的最佳选择
就像选择一辆入门级汽车，AVR系列提供了可靠且易于使用的特性：

**核心特点：**

+ 8位处理器，简单易用
+ 价格实惠（约50美分）
+ 工作电压范围广（1.8-5.5V）
+ 运行速度可观（20MHz）

**优势：**

+ 单引脚编程，电路设计简单
+ 资源丰富，入门门槛低
+ 功耗低，适合电池供电
+ 可靠性高，工业级质量

**常用型号：**

+ ATtiny412：小巧精致，适合空间受限场景
+ ATtiny44：引脚较多，功能更丰富
+ ATtiny1614：增强型模拟功能

### 2. ARM系列 - 性能的代表
如果AVR是入门级汽车，ARM就像是性能车，提供更强大的功能：

**核心特点：**

+ 32位处理器架构
+ 时钟频率高（48MHz以上）
+ 丰富的外设接口
+ 支持更复杂的功能

**优势：**

+ 强大的计算能力
+ 支持USB等高级接口
+ 内存容量更大
+ 生态系统成熟

**常用型号：**

+ SAMD11：小型但功能全面
+ SAMD21：性能更强，适合复杂应用
+ SAM51：高端型号，支持高速运算

### 3. RP2040（树莓派）- 创新的选择
这是一款极具特色的处理器，带来了独特的功能：

**核心特点：**

+ 双核32位处理器
+ 基础时钟频率133MHz
+ 可超频至250MHz以上
+ 创新的可编程IO系统

**优势：**

+ 价格实惠（约$1）
+ 独特的PIO功能
+ 强大的开发生态
+ 优秀的性价比

### 4. ESP32/ESP8266 - 无线通信的专家
适合需要网络连接的项目：

**核心特点：**

+ 集成WiFi和蓝牙功能
+ 32位处理器
+ 丰富的外设接口
+ 多核心设计（ESP32）

**优势：**

+ 无线通信能力强
+ 集成度高
+ 性价比出色
+ 开发生态丰富

## 第三部分：开发环境与工具
### 编程语言选择
#### 1. C/C++
最基础和常用的嵌入式开发语言：

**优势：**

+ 执行效率高
+ 内存占用小
+ 可直接操作硬件
+ 编译器支持完善

**适用场景：**

+ 对性能要求高的项目
+ 资源受限的设备
+ 需要直接硬件控制的场景

#### 2. Python (MicroPython/CircuitPython)
适合快速开发和学习：

**优势：**

+ 代码简洁易读
+ 开发速度快
+ 调试方便
+ 丰富的库支持

**注意事项：**

+ 执行速度较慢（约为C的1/100）
+ 需要更多系统资源
+ 不适合实时控制

### 开发环境推荐
#### 1. Arduino IDE
最受欢迎的入门级开发环境：

**特点：**

+ 安装简单
+ 内置常用库
+ 一键编译上传
+ 串口监视器

**优势：**

+ 学习曲线平缓
+ 社区支持强大
+ 示例丰富
+ 适合教育

#### 2. VS Code + PlatformIO
专业级的开发环境组合：

**特点：**

+ 智能代码补全
+ 语法检查
+ 代码重构
+ 版本控制

**优势：**

+ 功能全面
+ 扩展丰富
+ 支持团队协作
+ 跨平台

## 第四部分：调试方法与技巧
### 基础调试方法 Arduino (C/C++)
#### LED状态指示
最基本也最直观的调试方法：

```c
// 示例代码
void debugBlink(int times) {
    for(int i = 0; i < times; i++) {
        digitalWrite(LED_PIN, HIGH);
        delay(100);
        digitalWrite(LED_PIN, LOW);
        delay(100);
    }
}
```

#### 串口打印调试
可输出详细的调试信息：

```c
void debugPrint(const char* msg, int value) {
    Serial.print("Debug: ");
    Serial.print(msg);
    Serial.print(" = ");
    Serial.println(value);
}
```

### 常见问题与解决方案
#### 程序无法下载
可能原因：

+ 接线错误
+ 芯片损坏
+ 熔丝位设置错误
+ 供电问题

解决步骤：

1. 检查接线
2. 验证供电电压
3. 尝试降低编程速度
4. 检查芯片型号

## 实践作业
### 小组作业
1. 比较不同的嵌入式架构
2. 测试不同处理器的开发环境
3. 记录并分享对比结果

### 个人作业
1. 选择一个微控制器并阅读其数据手册
2. 编写简单的程序实现：
    - LED控制
    - 按键输入
    - 串口通信
3. 使用模拟器验证程序功能
4. 选做：在实际硬件上测试程序

## 学习资源
### 官方文档
+ 处理器数据手册
+ 编程指南
+ 示例代码

### 在线资源
+ 本课大纲：[Embedded Programming](http://academy.cba.mit.edu/classes/embedded_programming/index.html)
+ [Arduino官方文档](https://www.arduino.cc/reference/en/)
+ [MicroPython文档](https://docs.micropython.org/)
+ [ESP32文档](https://docs.espressif.com/)
+ [树莓派 Pico文档](https://www.raspberrypi.com/documentation/microcontrollers/)

## 注意事项
### 安全提示
1. 始终注意电压电流匹配
2. LED必须使用限流电阻
3. 避免电路短路

### 开发建议
1. 从简单示例开始
2. 循序渐进增加功能
3. 及时保存和备份代码
4. 建立良好的调试习惯

### 文档整理
1. 及时记录开发过程
2. 保存重要代码片段
3. 记录问题和解决方案

