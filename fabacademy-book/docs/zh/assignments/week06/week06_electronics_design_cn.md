---
layout: doc
title: "第6周：电子设计与生产 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第六周：学习电子设计基础，了解各种电子元器件和PCB设计工具"
head:
  - - meta
    - name: keywords
      content: fab academy, 电子设计, PCB, KiCad, 电子元器件, 微控制器, 原理图设计, 电路板设计
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第5周：设计3D打印笔筒'
  link: '/zh/assignments/week05/week05_3d-pen-holder-assignment_cn'
next:
  text: '第6周：个人作业：第一次尝试设计PCB'
  link: '/zh/assignments/week06/week06_individual_assignment_pcb_design_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 第 6 周：电子设计

> 本文档内容由我向 Claude 3.7 Sonnet 提供[课程大纲](http://academy.cba.mit.edu/classes/electronics_design/index.html)内容，以及本课程视频会议课程的字幕后，由 AI 提炼生成。

## 课程概要

本节课介绍电子设计的基础知识和工具，重点讲解了常用电子元器件、电路设计软件（EDA工具）以及测试设备的使用。课程分为两大部分：首先介绍常见电子元器件及其功能；其次详细讲解使用EDA工具设计电路板的流程。本周任务是设计一个基于微控制器的开发板，为后续的电子制作课程做准备。

## 详细的课程内容介绍

### 一、课程导入

两周前我们学习了嵌入式编程，本周我们将学习电子设计，两周后我们将进行电子生产。本课程的重点是学习电子元器件与电路设计工具，最终目标是设计一个围绕微控制器的开发板，并在两周后制作它。

### 二、设计灵感

首先，我们来看一些优秀的设计案例：

1. [Adrián Torres 的 fab xiao](https://fabacademy.org/2020/labs/leon/students/adrian-torres/fabxiao.html)：一个功能丰富的开发板，具有多种接口协议，可连接到各种设备。
2. [Ani Liu 艺术化的电路设计](https://fab.cba.mit.edu/classes/863.15/section.CBA/people/Liu/)：电路板设计不仅仅是功能性的，也可以是艺术性的。例如一位学生将讲师的头像设计成了电路板。
3. [Kai Zhang 编程辅助设计](https://fab.cba.mit.edu/classes/863.23/Harvard/people/Kai/index.html#week_6)：一位编程能力强的学生编写了自己的软件来辅助设计，创造出美观的电路。

这些例子说明电路设计既可以是功能性的，也可以是创造性和艺术性的。

### 三、电子元器件介绍

#### 1. 导线（Wire）

+ 使用排线（ribbon cable）更加方便，既可以保持多根线一起走线，也可以单独拆分使用
+ 导线规格用AWG表示，根据电流大小选择合适粗细的导线

#### 2. 连接器（Connectors）

+ 连接器用于可拆卸的连接，区别于固定连接
+ IDC（绝缘位移连接器）允许同时连接多根线，无需单独处理每一根
+ 使用时将排线放入连接器，挤压使尖角穿透绝缘层接触导线

#### 3. 按钮与开关（Buttons & Switches）

+ 按钮：通常为常开型，按下时连接两侧
+ 开关：可以固定在某个位置，用于开/关或配置
+ 这些都是表面贴装（SMD）元件，而非通孔元件

#### 4. 电阻器（Resistor）

+ 欧姆定律：I = V/R
+ 类比为水流中的狭窄通道，限制流量
+ 常用于限流，如LED电路中
+ 规格说明：
  - 封装大小（如1206，表示12/1000 × 6/1000英寸）
  - 功率等级（如1/4瓦）
  - 电阻值和精度
+ 使用1206封装主要是为了方便在使用1/64英寸铣刀加工电路板时能够在电阻下方走线

#### 5. 电容器（Capacitor）

+ 电容定律：C = Q/V，电流I = C·dV/dt
+ 类比为水库储水
+ 功能：
  - 用作滤波器
  - 设定响应时间
  - 大电容（超级电容）可用于短期能量存储，替代电池
  - 用于滤波不稳定的电源

#### 6. 晶振（Crystal）

+ 用于精确计时
+ 结构类似带有特殊材料的电容器
+ 当施加电压时，材料弯曲并产生电压，可以非常精确地重复
+ 在复杂处理器中需要晶振，但一些微控制器（如 XIAO）已内置

#### 7. 电感器（Inductor）

+ 与电容器相反，电压与电流变化率成正比
+ 用作高频滤波器，只允许低频通过

#### 8. 二极管（Diode）

+ 结构：阳极(Anode)和阴极(Cathode)
+ 电流只能从阳极流向阴极
+ 用途：
  - 保护电路不受反向连接电池的损害
  - 整流
+ 特殊类型：
  - 齐纳二极管（用于精确电压参考）
  - LED（发光二极管，需要限流电阻）

#### 9. 晶体管（Transistor）

+ 两种类型：
  - 双极型（一般不使用）
  - MOSFET（主要使用）
+ MOSFET特点：
  - 分为N型（电流汇）和P型（电流源）
  - 有栅极(Gate)、源极(Source)和漏极(Drain)
  - 可控制电阻，用于开关负载
  - RDS（漏源电阻）越小越好，减少功耗和发热
  - 选择逻辑电平晶体管便于控制

#### 10. 稳压器（Regulator）

+ 将高电压（如9V电池）转换为稳定的低电压（如3.3V）
+ 需要输出电容作为缓冲
+ 简单稳压器效率低，也可使用DC-DC转换器提高效率

#### 11. 放大器（Amplifier）

+ 用于测量敏感信号或产生功率信号
+ 应用：麦克风信号放大、扬声器驱动
+ 现代微控制器和模块通常集成了放大器功能

#### 12. 微控制器（Microcontroller）

+ 包含各种功能引脚：
  - 电源和地
  - 模拟引脚（读取电压）
  - 数字引脚（输出或读取逻辑信号）
  - 通信协议：I2C, UART, SPI等
+ 选择合适的微控制器：
  - RP 2040：功能强大，可定制处理器
  - ESP32：适合无线通信（WiFi、蓝牙）
  - ARM：支持USB通信
  - AVR：小型低成本处理器

### 四、电路设计基础

#### 1. 电路基本原理

+ 电流（安培）和电压（伏特）
+ 功率：P = I²R = IV
+ 基尔霍夫定律：
  - 电流定律：节点电流之和为零
  - 电压定律：回路电压之和为零

#### 2. 电子设计自动化（EDA）工具

EDA工具学习曲线陡峭，但对复杂电路设计必不可少。主要流程包括：

1. 在纸上或平板上草图设计
2. 输入原理图（连接元件）
3. 元件放置（在PCB上）
4. 布线（连接导线）
5. 仿真（可选）
6. 制造

需要考虑的因素：

+ 层数（单层、双层等）
+ 电源层与地线层
+ 元件封装（Footprint）
+ 3D模型（便于机械设计）
+ 设计规则（线宽、间距等）

#### 3. 主要EDA工具介绍

##### [KiCad](https://www.kicad.org/)

+ 免费开源、跨平台
+ 功能强大，但各模块之间集成度较低
+ 特点：
  - 最近发布KiCad 9，性能大幅更新
  - 有丰富的库资源
  - 支持推挤布线、自动布线辅助

使用流程演示：

1. 创建项目
2. 设计原理图
3. 元件放置
4. PCB布线
5. 设置边界
6. 3D查看
7. 导出制造文件

##### [Eagle](https://www.autodesk.com/products/eagle/overview)（集成在Fusion 360中）

+ 商业软件
+ 与Fusion 360完全集成，电子设计和机械设计可以无缝切换
+ 适合已经在使用Fusion 360的用户

##### 其他工具

+ [LibrePCB](https://librepcb.org/)、[EasyEDA](https://easyeda.com/)
+ [OrCAD](https://www.ema-eda.com/products/cadence-orcad/why-orcad)、[Cadence](https://www.cadence.com/en_US/home.html)（工业级工具）
+ 集成电路设计工具如 [Magic](http://opencircuitdesign.com/)、[KLayout](https://www.klayout.de/) 等

#### 4. 库资源（Libraries）

+ 元件库是EDA工具的重要组成部分
+ 包含：原理图符号、PCB封装、3D模型
+ 资源推荐：
  - [Fab库](https://gitlab.fabcloud.org/pub/libraries/electronics)（由Chris维护，包含课程用到的大部分元件）
  - [Component Search Engine](https://componentsearchengine.com/)
  - [Ultra Librarian](https://www.ultralibrarian.com/)
  - [SnapEDA](https://www.snapeda.com/)

#### 5. 硬件描述语言

+ 用于程序化描述电路
+ 对于非常复杂的电路（如十亿晶体管处理器）必不可少
+ 类型：
  - [Verilog](https://www.verilog.com/)、[VHDL](http://valhalla.altium.com/Learning-Guides/TR0114%20VHDL%20Language%20Reference.pdf)（传统HDL）
  - [pcb.py](https://gitlab.cba.mit.edu/pub/libraries/-/tree/master/python)、SVG-PCB、JSON-PCB（更现代的工具）
+ 可以描述元件之间的关系，而不是点击拖拽

#### 6. 电路仿真

+ 在实际制作前验证设计
+ 仿真类型：
  - 数字仿真（Wokwi）
  - 模拟仿真（Falstad）
  - 混合信号仿真
+ SPICE框架：
  - LTspice、ngspice
  - KiCad和Eagle都有SPICE接口

### 五、测试设备

当电路设计完成并制作后，通常需要测试和调试。常用测试工具包括：

1. 台式电源
   - 可调节电压和电流
   - 对电机控制等应用非常有用
2. 万用表
   - 测量电压、电流、电阻
   - 首先用于检查电路中的电压是否正常
3. 示波器
   - 显示随时间变化的信号
   - 查看信号波形而非仅仅平均值
4. 逻辑分析仪
   - 不仅显示信号，还能解释信号含义
   - 用于调试通信协议（如I2C）
5. 混合信号分析仪
   - 结合了示波器和逻辑分析仪功能
   - 可监测多路信号并解码协议

### 六、设计建议

1. 先从简单电路开始，熟悉工具
2. 使用现有库资源，避免重复造轮子
3. 布线时将其视为一个有趣的拼图挑战
4. 在复杂的项目中使用仿真验证设计
5. 学会使用测试设备调试电路问题

## 作业要求

### 小组作业

使用实验室中的测试设备观察微控制器电路板的工作情况：

+ 使用数字万用表检测电路板上的电压
+ 使用示波器查看电源噪声
+ 使用逻辑分析仪解码通信协议（如USB命令）

### 个人作业

使用EDA工具设计一个开发板：

1. 选择一款微控制器（如RP2040、ESP32、ARM或AVR系列）
2. 设计围绕此微控制器的开发板
3. 考虑将来需要连接的输入/输出设备接口
4. 设计通信方式（如USB、WiFi等，取决于所选微控制器）

### 加分项目

1. 仿真验证设计
2. 尝试不同的设计工作流程并比较
3. 为开发板设计外壳（需要将电子设计导出到CAD环境中）

## 学习资源

1. 电子元器件
   - [Digikey电子元器件目录](https://www.digikey.com/)
   - [电阻值标准系列](https://www.electronics-notes.com/articles/electronic_components/resistors/standard-resistor-values-e-series-e3-e6-e12-e24-e48-e96.php)
   - [The Art of Electronics](https://artofelectronics.net/) - 电子学经典参考书
2. EDA工具
   - [KiCad官方网站](http://kicad.org/)
   - [Eagle/Fusion 360](https://www.autodesk.com/products/eagle/overview)
   - [KiCad设计规则](https://docs.oshpark.com/design-tools/kicad/kicad-design-rules/)
   - [KiCad Fab库](https://gitlab.fabcloud.org/pub/libraries/electronics)
3. 仿真工具
   - [Falstad在线电路仿真](https://www.falstad.com/circuit/)
   - [Wokwi](https://wokwi.com/)
   - [LTspice](https://www.analog.com/en/design-center/design-tools-and-calculators/ltspice-simulator.html)
4. 参考设计
   - [Adrian的Fab Shell](https://fabacademy.org/2020/labs/leon/students/adrian-torres/fabxiao.html)
   - [Component Search Engine](https://componentsearchengine.com/)
5. 测试设备
   - [Saleae逻辑分析仪](https://www.saleae.com/)
   - [SainSmart测试设备](https://www.sainsmart.com/products/)
