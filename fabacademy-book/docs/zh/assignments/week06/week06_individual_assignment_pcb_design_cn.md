---
layout: doc
title: "第6周：第一次尝试设计PCB | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第六周个人作业：学习KiCad，为XIAO ESP32C3设计扩展板"
head:
  - - meta
    - name: keywords
      content: fab academy, PCB设计, KiCad, XIAO ESP32C3, 扩展板, 原理图设计, LED控制, 电子设计
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第6周：电子设计与生产'
  link: '/zh/assignments/week06/week06_electronics_design_cn'
next:
  text: '第7周：计算机控制机械加工'
  link: '/zh/assignments/week07/week07_computer_controlled_machining_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第 6 周小组作业：电子测量工具

[[查看小组作业完整内容]](https://fabacademy.org/2025/labs/chaihuo/docs/week6/week6_group_assignment)

本周的小组作业重点是熟悉 FabLab 中常用的电子测量工具，包括万用表、逻辑分析仪和示波器。有效使用这些工具对于调试和测试电子电路至关重要。

我们进行了以下实验和测量：

1. **万用表**：
   - 电压测量：测量 ESP32C3 微控制器的 5V 引脚输出（实际为 5.11V）和 LED 闪烁电路的数字信号变化（高电平 3.25V，低电平 0V）
   - 导通性检查：在面包板上测试电路连接的正确性
   - 电阻测量：测量标称 1K 电阻的实际阻值（0.992kΩ）

2. **逻辑分析仪**：
   - 使用 DSLogicPlus 捕获和分析 I2C 通信信号
   - 运行了 Arduino I2C 扫描演示，检测连接的 I2C 设备
   - 发现 SCL 信号在地址 0x80 处响应，测得 I2C 频率为 44 kHz

3. **示波器**：
   - 捕获差分频率信号并检查波形特性
   - 使用了包括 RTM3004 在内的多种示波器型号（53M、13M 和 26M）
   - 分析了 I2C 时钟信号(SCL)，频率同样测得为 44 kHz

这些电子测量工具对于电路调试和验证是不可或缺的，对任何电子相关项目都至关重要。

---

# 第 6 周个人作业：第一次尝试设计 PCB
这周作业对我来说挑战很大，虽然之前写过几本 Arduino 有关的编程入门书籍，但还未涉足电子学和电路板设计。所以这周的课程对我而言，几乎是从头开始。

## 软件学习
电子学的部分，我尝试开始学习 Forrest M. Mims, III 的 [Getting Started in Electronics](https://archive.org/details/getting-started-in-electronics/mode/2up)，我觉得这本书是能够让我真正看懂和理解的电子学入门读物。但这个学习过程比较缓慢，我估计我需要花几个月的时间才能完全掌握这门学科。

### 嘉立创 EDA 
我尝试了中国的 PCB 设计工具[嘉立创 EDA](https://lceda.cn/)（免费软件），软件的开发者是嘉立创 PCB，他们期望用户使用这个免费的软件，设计好 PCB 电路板后直接可以使用他们的 PCB 制作服务，以此来获得收益。软件还支持导入 Altium、KiCad 等项目。而且软件还支持团队协作。

这个工具的文档和操作对中文用户很友好，文档介绍了 PCB 设计到 PCB 下单的整个过程，如下图所示。

![](/images/week06/w06-p-1.png)

> 嘉立创 EDA 文档里的 PCB 设计工作流程
>

我询问了 Seeed Studio 的电子工程师，他们推荐我使用 KiCad，所以我调整了学习目标。

### KiCad EDA
在开始电子学课程学习的同时，我也开始尝试使用 [KiCad EDA](https://www.kicad.org/) 软件（开源软件），借助中文快速入门的视频教程：[https://www.bilibili.com/video/BV12J411z7j7](https://www.bilibili.com/video/BV12J411z7j7)

我绘制了一个原理图，教程作者直接从一个现成的项目原理图开始，教你如何使用 KiCad 绘制，初始的参考图纸如下图所示。

![](/images/week06/w06-p-2.jpg)

> 视频教程开始的参考原理图
>

在教程作者的指导下，我成功的用 KiCad 9 绘制了我的第一张原理图，如下图所示。

![](/images/week06/w06-p-3.png)

> 跟随教程绘制的原理图
>

然后又跟着教程指示，依据原理图进行 PCB 的编辑，尝试画出了下面的设计图，发现时间已经过去了半周多。

![](/images/week06/w06-p-4.png)

> 跟随教程绘制的 PCB 设计图
>

这一步，算是对如何绘制原理图和电路板有了一个基本概念。但我觉得还是有点懵，因为电子学的技能点还没点够。要弄明白在自己需要的功能元件中，添加什么型号的电阻、电容等元器件，感觉自己离独立设计 PCB 还有相当长的距离。

## 使用 AI 设计 XIAO ESP32C3 扩展板
鉴于当前的知识储备，我对自己设计 PCB 还缺乏自信，所以决定尝试用 AI 指导我，为 XIAO ESP32C3 设计制作一个扩展板，AI 我选择了 [Claude](https://claude.ai/chat/)，目前我在用的版本是 Claude 3.7 Sonnet。

### 提示词准备
我编写了一个简单的提示词，并提供了 XIAO ESP32C3 Wiki 的文本内容和引脚图，如下图所示。

![](/images/week06/w06-p-5.png)

> 向 Claude 提供 XIAO ESP32C3 Wiki 的文本内容和引脚图，以及给出提示词：  
我想请你作为一名电子工程师，协助我设计一个 PCB，这个 PCB我想作为 XIAO ESP32C3的扩展板（提供可以插 XIAO ESP32C3的插槽），板子上面安置6个 可以编程控制的LED 灯，一个可编程按钮。 指导我使用 KiCad 绘制原理图和 PCB 设计
>

以下被分割线包裹的内容是 Claude 的完整输出。

---

# XIAO ESP32C3 扩展板设计指南
很高兴为您提供XIAO ESP32C3扩展板的设计指导。根据您的需求，我将详细介绍如何使用KiCad设计一个包含6个可编程LED和1个按钮的扩展板。

## 一、设计概述
根据您提供的信息和图片，我们需要设计的扩展板将包括：

+ XIAO ESP32C3插槽
+ 6个可编程LED
+ 1个可编程按钮
+ 必要的连接器和电源管理

## 二、KiCad原理图设计步骤
### 1. 创建新项目
1. 打开KiCad并创建新项目
2. 命名为"XIAO_ESP32C3_Extension"
3. 点击原理图编辑器图标打开Eeschema

### 2. 添加XIAO ESP32C3符号
由于KiCad可能没有XIAO ESP32C3的标准库，我们需要创建一个自定义符号：

1. 在Eeschema中，点击"工具" → "符号编辑器"
2. 创建新库，命名为"XIAO_ESP32C3"
3. 创建新符号，设置引脚如下（基于图片中的引脚排列）：

```plain
左侧引脚:
GPIO2/D0 - 数字引脚
GPIO3/D1 - 数字引脚
GPIO4/D2 - 数字引脚
GPIO5/D3 - 数字引脚
GPIO6/D4 - 数字引脚
GPIO7/D5 - 数字引脚
GPIO21/D6 - 数字引脚

右侧引脚:
5V - 电源输入
GND - 电源接地
3V3 - 电源输出
D10/GPIO10 - 数字引脚
D9/GPIO9 - 数字引脚
D8/GPIO8 - 数字引脚
D7/GPIO20 - 数字引脚

顶部引脚:
TX, RX, SCK, MISO, MOSI - 通信接口
```

### 3. 添加LED电路
为6个LED设计电路：

1. 从库中添加LED元件（6个）
2. 为每个LED添加限流电阻（推荐220Ω-1kΩ，取决于LED类型和亮度要求）
3. 将LED阴极连接到GND
4. 将LED阳极通过限流电阻连接到不同的GPIO引脚（可以使用D0-D5）

### 4. 添加按钮电路
1. 从库中添加按钮元件（例如SW_Push）
2. 添加一个10kΩ的上拉电阻
3. 将按钮一端连接到一个GPIO引脚（如D6）
4. 将按钮另一端连接到GND
5. 将上拉电阻连接到GPIO引脚和3.3V之间

### 5. 完成原理图
以下是建议的连接方案：

```plain
LED1: D0/GPIO2 -> 220Ω电阻 -> LED -> GND
LED2: D1/GPIO3 -> 220Ω电阻 -> LED -> GND
LED3: D2/GPIO4 -> 220Ω电阻 -> LED -> GND
LED4: D3/GPIO5 -> 220Ω电阻 -> LED -> GND
LED5: D4/GPIO6 -> 220Ω电阻 -> LED -> GND
LED6: D5/GPIO7 -> 220Ω电阻 -> LED -> GND
按钮: D6/GPIO21 -> 10kΩ电阻 -> 3.3V
              D6/GPIO21 -> 按钮 -> GND
```

## 三、PCB设计步骤
### 1. 设置PCB布局
1. 完成原理图后，点击"工具" → "更新PCB与原理图"
2. 在PCB编辑器中，设置板子尺寸（建议比XIAO ESP32C3略大，约30mm x 25mm）
3. 设置网格大小为0.5mm或0.25mm以便精确定位

### 2. 设计插槽
为XIAO ESP32C3创建插槽有两种方法：

#### 方法一：使用插针排连接器
1. 在PCB编辑器的库中找到1x7针插针排，放置两排
2. 按XIAO ESP32C3的引脚间距（2.54mm）排列

#### 方法二：设计卡槽式连接器
1. 创建自定义封装，使板子可以直接插入
2. 添加14个焊盘，按XIAO ESP32C3引脚间距排列

### 3. 放置元件
1. 在PCB上放置所有元件（LED、电阻、按钮）
2. LED建议排列在板子的一侧，便于查看
3. 按钮放置在便于操作的位置

### 4. 布线
1. 使用两层PCB设计（顶层和底层）
2. 首先连接电源和地线（GND和3.3V）
3. 然后连接LED和按钮线路
4. 使用0.25mm线宽连接信号线
5. 使用0.5mm或更宽线宽连接电源线
6. 添加接地平面，用铺铜填充未使用区域

### 5. 设计检查
完成设计后，运行以下检查：

1. DRC（设计规则检查）确保没有违反制造规则
2. ERC（电气规则检查）确保电气连接正确
3. 3D查看确认元件位置合理

## 四、元件清单
您需要准备以下元件：

+ 1x 插针排（2x7针，2.54mm间距）用于XIAO ESP32C3插座
+ 6x LED（建议使用3mm或5mm直径，可选不同颜色）
+ 6x 220Ω-1kΩ电阻（取决于LED规格）
+ 1x 轻触按钮
+ 1x 10kΩ电阻（用于按钮上拉）

## 五、代码示例
完成硬件设计后，您可以使用以下Arduino代码测试您的扩展板：

```c
// XIAO ESP32C3 扩展板测试代码
// 控制6个LED和1个按钮

// 定义引脚
const int LED_PINS[6] = {D0, D1, D2, D3, D4, D5}; // LED连接的引脚
const int BUTTON_PIN = D6;                        // 按钮连接的引脚

// 记录按钮状态
int lastButtonState = HIGH;  // 初始状态为高电平（未按下）
int currentPattern = 0;      // 当前LED显示模式

void setup() {
    Serial.begin(115200);
    Serial.println("XIAO ESP32C3 扩展板测试");

    // 设置LED引脚为输出
    for (int i = 0; i < 6; i++) {
        pinMode(LED_PINS[i], OUTPUT);
        digitalWrite(LED_PINS[i], LOW); // 初始状态为关闭
    }

    // 设置按钮引脚为输入，启用内部上拉电阻
    pinMode(BUTTON_PIN, INPUT_PULLUP);
}

void loop() {
    // 读取按钮状态
    int buttonState = digitalRead(BUTTON_PIN);

    // 按钮状态变化检测（从未按下到按下）
    if (buttonState == LOW && lastButtonState == HIGH) {
        delay(50); // 简单的消抖

        // 再次检查以确认不是噪声
        if (digitalRead(BUTTON_PIN) == LOW) {
            // 切换到下一个显示模式
            currentPattern = (currentPattern + 1) % 4;
            Serial.print("切换到模式: ");
            Serial.println(currentPattern);
        }
    }

    // 更新上一次的按钮状态
    lastButtonState = buttonState;

    // 根据当前模式显示LED效果
    switch (currentPattern) {
        case 0:
            // 模式0: 所有LED关闭
            allOff();
            break;
        case 1:
            // 模式1: 所有LED点亮
            allOn();
            break;
        case 2:
            // 模式2: LED依次闪烁
            chaseEffect();
            break;
        case 3:
            // 模式3: LED呼吸效果
            breatheEffect();
            break;
    }
}

// 关闭所有LED
void allOff() {
    for (int i = 0; i < 6; i++) {
        digitalWrite(LED_PINS[i], LOW);
    }
}

// 打开所有LED
void allOn() {
    for (int i = 0; i < 6; i++) {
        digitalWrite(LED_PINS[i], HIGH);
    }
}

// LED依次闪烁效果
void chaseEffect() {
    for (int i = 0; i < 6; i++) {
        allOff();
        digitalWrite(LED_PINS[i], HIGH);
        delay(100);
    }
}

// LED呼吸效果（使用PWM）
void breatheEffect() {
    // 逐渐变亮
    for (int brightness = 0; brightness <= 255; brightness += 5) {
        for (int i = 0; i < 6; i++) {
            analogWrite(LED_PINS[i], brightness);
        }
        delay(30);
    }

    // 逐渐变暗
    for (int brightness = 255; brightness >= 0; brightness -= 5) {
        for (int i = 0; i < 6; i++) {
            analogWrite(LED_PINS[i], brightness);
        }
        delay(30);
    }
}
```

## 六、实用提示
1. **设计注意事项**:
    - 注意GPIO2、GPIO8和GPIO9是Strapping引脚，按文档所述，这些引脚的电平可能会影响板子的启动模式。在设计时注意这一点。
    - 避免使用A3(GPIO5)进行模拟读取，因为它容易受假采样信号的影响。
2. **电源管理**:
    - 如果需要通过电池供电，可考虑在设计中添加电池连接器。
    - XIAO ESP32C3内置电池充放电管理芯片，但您需要添加合适的连接器。
3. **PCB制造考虑**:
    - 使用标准PCB厚度（1.6mm）以确保与插针排兼容。
    - 设置适当的孔径和焊盘尺寸，推荐孔径0.8-1.0mm。

希望这个指南能帮助您设计XIAO ESP32C3的扩展板。如果您需要进一步了解KiCad的具体操作，或者对设计有其他问题，请随时告诉我。

---

# 根据 AI 输出的内容设计 PCB
接下来，我按照 Claude 的建议，尝试开始在 KiCad 绘制原理图。

## 创建新项目
我在 KiCad 里创建了一个新的工程文件：XIAO_ESP32C3_Extension，如下图所示。

![](/images/week06/w06-p-6.png)

然后点击原理图编辑器，进入原理图编辑，如下图所示。

![](/images/week06/w06-p-7.png)

## 添加XIAO ESP32C3符号
Claude 指导我手动添加 XIAO ESP32C3 的符号，但这里我没有遵循其建议。因为在 Seeed 的 Wiki 文档： Getting Started with Seeed Studio XIAO ESP32C3 的 [Resources](https://wiki.seeedstudio.com/XIAO_ESP32C3_Getting_Started/#resources) 列表中，提供了 [Seeed Studio XIAO ESP32C3 KiCad Libraries](https://files.seeedstudio.com/wiki/XIAO_WiFi/Resources/Seeeduino-XIAO-ESP32C3-KiCAD-Library.zip) 的 ZIP 包，如下图所示。

![](/images/week06/w06-p-8.png)

下载并解压后，可以获得以下文件：

![](/images/week06/w06-p-9.png)

+ `MOUDLE-SEEEDUINO-XIAO-ESP32C3.KiCad_sym` 是我们需要的符号文件。
+ `MOUDLE14P-SMD-2.54-21X17.8MM.KiCad_mod` 是对应的封装文件。

在 KiCad 里我创建了一个库，命名为 `XIAO`，然后用导入库的方式，在符号编辑器，使用顶部菜单的 文件 > 导入> 符号功能，导入 `MOUDLE-SEEEDUINO-XIAO-ESP32C3.KiCad_sym` 符号文件，这样可以直接在原理图设计中，使用 XIAO ESP32C3 的符号。

![](/images/week06/w06-p-10.png)

> 将 MOUDLE-SEEEDUINO-XIAO-ESP32C3.KiCad_sym 添加到 KiCad 的符号库
>

现在可以直接将 XIAO ESP32C3 的符号添加到原理图。

![](/images/week06/w06-p-11.png)

用同样的方式，在封装编辑器下，导入`MOUDLE14P-SMD-2.54-21X17.8MM.KiCad_mod`封装，如下图所示。

![](/images/week06/w06-p-12.png)

> 导入 XIAO 的封装
>

因为 XIAO 系列多个型号的封装尺寸规格一样，所以我还从 XIAO RP2040 提供的 [KiCad Libraries](https://files.seeedstudio.com/wiki/XIAO-RP2040/res/Seeeduino-xiao-rp2040-KiCAD-Library.zip) 文件，导入了另一封装，如下图所示，这个封装比较适合接带针脚的 XIAO。

![](/images/week06/w06-p-13.png)

> 从 XIAO RP2040 导入 XIAO 的带针脚孔的封装
>

## 添加 LED 和按钮电路
接下来根据 Claude 的建议我向编辑区添加了 6 个 LED 和对应的限流电阻，以及按钮。根据如下所示的 AI 提供的符号连接建议，通过添加网络标签来连接符号。

```plain
LED1: D0/GPIO2 -> 220Ω电阻 -> LED -> GND
LED2: D1/GPIO3 -> 220Ω电阻 -> LED -> GND
LED3: D2/GPIO4 -> 220Ω电阻 -> LED -> GND
LED4: D3/GPIO5 -> 220Ω电阻 -> LED -> GND
LED5: D4/GPIO6 -> 220Ω电阻 -> LED -> GND
LED6: D5/GPIO7 -> 220Ω电阻 -> LED -> GND
按钮: D6/GPIO21 -> 10kΩ电阻 -> 3.3V
              D6/GPIO21 -> 按钮 -> GND
```

![](/images/week06/w06-p-14.png)

> 原理图中通过网络标签连接的符号
>

可以选择一个网络标签，按键盘左上角的“～”键，查看相同标签的连接。这种方式能让原理图变得非常简洁。

![](/images/week06/w06-p-15.png)

> 在 KiCad 编辑原理图时，选中网络标签（GND），并按下键盘左上角的“～”键，可以看到所有相同网络标签被高亮
>

## 添加单一 8 引脚连接器扩展排针
完成初步的连接后，发现 XIAO 右侧一些引脚没有使用，所以我导出了符号字段表，并将当前原理图的截图，在 Claude 中继续提出了新的要求：

> 我想选择一个`Conn_01x08`作为所有未使用引脚的单一连接器。
>

以下被分割线包裹的内容是 Claude 的完整输出。

---

# 使用单一8引脚连接器的扩展排针设计
使用单一的`Conn_01x08`连接器是一个很好的选择，可以让设计更加简洁，同时提供足够的扩展性。下面我将详细介绍如何在KiCAD中实现这一设计。

## 8引脚连接器设计方案
### 1. 引脚分配建议
为您的`Conn_01x08`连接器的推荐引脚分配:

```plain
1. 3.3V       - 电源
2. GND        - 接地
3. D7/RX      - 串口接收
4. D6/TX      - 串口发送(如果按钮改用其他引脚)
5. D8/SCK     - SPI时钟(Strapping引脚)
6. D9/MISO    - SPI主机输入(Strapping引脚)
7. D10/MOSI   - SPI主机输出
8. RST        - 复位信号(可选)或其他可用引脚
```

### 2. KiCAD中添加连接器的步骤
1. 在原理图编辑器中，按快捷键"A"或点击"放置符号"按钮
2. 在搜索框中输入"Connector"
3. 选择"Connector_Generic:Conn_01x08"
4. 将连接器放置在原理图的合适位置(建议放在右侧)
5. 按"E"键编辑连接器属性，设置参考标识符为"J1"并添加描述"扩展接口"

![](/images/week06/w06-p-16.png)

> Claude 甚至还用文本绘图语言，给出了扩展接口的接线图，不过不太具备可读性
>

## 3. 详细的KiCAD实现步骤
### 第1步: 添加8引脚连接器
```plain
1. 打开原理图编辑器
2. 按"A"键打开"添加符号"对话框
3. 搜索"Conn_01x08"
4. 选择"Connector_Generic:Conn_01x08"
5. 点击放置在原理图右侧
6. 按"E"键编辑属性:
   - Reference: J1
   - Value: Expansion_Header
   - 添加描述: "8-pin expansion header"
```

### 第2步: 连接ESP32C3引脚到扩展排针
```plain
1. 用导线工具("W"键)连接:
   - 3.3V 到 J1引脚1
   - GND 到 J1引脚2
   - D7/RX 到 J1引脚3
   - D6/TX 到 J1引脚4(如果可用，否则标记为NC)
   - D8/SCK 到 J1引脚5
   - D9/MISO 到 J1引脚6
   - D10/MOSI 到 J1引脚7
   - RST或其他可用引脚到 J1引脚8
```

### 第3步: 添加上拉电阻(关键步骤!)
```plain
1. 按"A"键，搜索"R"
2. 选择"Device:R"并放置2个
3. 按"E"键将电阻值设为"10K"
4. 将R8连接到D8和3.3V之间
5. 将R9连接到D9和3.3V之间
```

### 第4步: 添加适当的标签
```plain
1. 按"T"键添加文本标签
2. 在连接器附近添加:
   "J1 - 扩展接口
    1: 3.3V 电源
    2: GND 接地
    3: RX 串口接收
    4: TX 串口发送
    5: SCK* SPI时钟 (*Strapping)
    6: MISO* SPI数据输入 (*Strapping)
    7: MOSI SPI数据输出
    8: RST 复位或扩展"
3. 添加警告标签: "注意: 带*的是Strapping引脚"
```

## 4. PCB布局建议
在PCB设计中:

1. **位置放置**:
    - 将J1连接器放在PCB边缘，方便连接外部设备
    - 标准2.54mm(0.1英寸)间距排针
2. **丝印标记**:
    - 在PCB上清晰标注每个引脚功能:"3V3", "GND", "RX", "TX", "SCK", "MISO", "MOSI", "RST"
    - 特别标记出Strapping引脚:"SCK*", "MISO*"
3. **布线考虑**:
    - 为敏感信号(如SPI线)保持尽可能短的走线
    - 添加去耦电容在3.3V和GND引脚附近
4. **上拉电阻布局**:
    - 将10K上拉电阻放置在D8和D9附近
    - 尽量靠近ESP32C3芯片

## 5. 使用提示
设计完成后，需要在软件中正确初始化这些引脚:

```cpp
void setup() {
  // 初始化串口
  Serial.begin(115200);
  
  // 配置Strapping引脚
  pinMode(D8, INPUT_PULLUP); // 或根据需要配置为其他模式
  pinMode(D9, INPUT_PULLUP);
  
  // 配置其他扩展引脚
  pinMode(D10, INPUT); // 或根据应用需要配置
  pinMode(D7, INPUT);
  
  Serial.println("扩展接口初始化完成");
}
```

## 6. 扩展接口的功能应用
有了这个8引脚扩展接口，您可以连接:

1. 显示器: SPI LCD/OLED屏幕
2. 传感器: SPI接口传感器如温度计、加速度计等
3. 存储器: SPI Flash或SD卡
4. 通信模块: 串口蓝牙模块、LoRa模块等
5. 调试接口: 通过UART连接调试器

这种单一连接器的设计既简化了PCB布局，又保留了全部功能扩展性，是一个非常实用的方案。

希望这个详细的设计指南对您有所帮助！如果您有任何疑问或需要更详细的说明，请随时告诉我。

---

## 完成原理图
根据上面 Claude 的建议，我重新调整了原理图，现在看上去如下图所示，并做了电器规则检查（ERC）。

![](/images/week06/w06-p-17.png)

> 完成的原理图，并做了电器规则检查（ERC）
>

然后在符号字段表，统一为不同类型的符号选择了封装，如下图所示。

![](/images/week06/w06-p-18.png)

> 为符号选择对应的封装
>

完成这些工作后，就可以进入 PCB 编辑器进行 PCB 设计了。

## PCB 设计
根据前面 Claude 给出的布线建议

> 1. 使用两层PCB设计（顶层和底层）
> 2. 首先连接电源和地线（GND和3.3V）
> 3. 然后连接LED和按钮线路
> 4. 使用0.25mm线宽连接信号线
> 5. 使用0.5mm或更宽线宽连接电源线
> 6. 添加接地平面，用铺铜填充未使用区域
>

我设置了 3 种走线宽度：0.25mm，0.5mm，1mm，如下图所示。

![](/images/week06/w06-p-19.png)

然后选择从原理图更新 PCB，经过漫长的编辑工作后，终于看到了人生自己画的第一个电路板，并在 Edge.Cuts 添加了一个切割边界。

![](/images/week06/w06-p-20.png)

> 人生绘制的第一块 PCB
>

按 Option+3（Windows 为 Alt+3），可以查看 PCB 的 3D 视图，如下所示。

![](/images/week06/w06-p-21.jpg)

在同事的建议下，我尝试了覆铜和添加泪滴的操作。

### 覆铜
> 以下为 DeepSeek 提供的对覆铜作用的解释。
>
> PCB覆铜操作是通过在电路板的闲置区域填充铜层（通常连接到地线或电源层）来优化电路性能的关键工艺。其主要作用可概括如下：
>
> #### 1. 降低地线阻抗，增强抗干扰能力
> 覆铜通过大面积铜箔与地线连接，显著减小地线阻抗，从而降低信号回路中的电压降和噪声干扰，尤其在高频电路中能有效抑制电磁干扰（EMI）。例如，数字电路和模拟电路分区覆铜可避免信号串扰。
>
> #### 2. 提升散热效率
> 铜的高导热性使覆铜层成为散热通道，尤其适用于高功率元件（如处理器、功放器），通过分散热量防止局部过热，延长器件寿命。
>
> #### 3. 增强机械稳定性
> 覆铜层可增加PCB板的刚性，减少装配或使用过程中的弯曲变形风险，尤其对多层板或薄型基板至关重要。
>
> #### 4. 优化电源效率与电流承载
> 大面积的电源层覆铜能降低压降，提高电源分配效率，同时支持大电流传输，适用于电机驱动或电源模块。
>
> #### 5. 电磁屏蔽与信号完整性
> 高频电路中，覆铜层作为接地平面可减少信号线间的串扰，并屏蔽外部电磁干扰，确保信号传输的稳定性。
>
> #### 补充：覆铜方式的选择
> + 大面积覆铜：适用于低频大电流场景，需开槽防止热胀冷缩导致的翘曲。  
> + 网格覆铜：多用于高频电路，在散热与屏蔽间取得平衡，减少铜箔重量和应力。
>
> 通过合理设计覆铜策略，可显著提升PCB的电气性能、可靠性和使用寿命。
>

打开覆铜窗口，通常选择 F.Cu 层（铜层），并在网络中选中 /GND。如下图所示。

![](/images/week06/w06-p-22.png)

> 注意要选中 /GND
>

然后使用绘制填充区域包裹住需要进行覆铜的区域，如下图所示。

![](/images/week06/w06-p-23.png)

> 绘制需要覆铜的区域
>

闭合区域后会看到边框出现斜线，如下图所示。

![](/images/week06/w06-p-24.png)

> 确定要覆铜的区域会出现斜线边框
>

要进一步看到覆铜效果，需要在顶部菜单 编辑 > 填充所有填充区（或按下快捷键 `B`），如下图所示。 

![](/images/week06/w06-p-25.jpg)

> 菜单 编辑 > 填充所有填充区
>

现在可以看到成功覆铜后的效果了。

![](/images/week06/w06-p-26.png)

### 添加泪滴
> 以下为 Claude 提供的对泪滴作用的解释。
>
> PCB 设计中添加泪滴（Teardrop）有以下几个重要作用：
>
> 1. **提高机械强度**：泪滴增强了焊盘与走线连接处的机械强度，使其更能承受振动和热膨胀引起的应力，减少了因物理冲击导致的断裂风险。
> 2. **改善信号传输**：泪滴可以平滑高频信号传输路径，减少拐角处的阻抗不连续性，有助于提高高速电路的信号完整性。
> 3. **优化制造工艺**： 
>     - 增加蚀刻容限，减少过蚀刻风险
>     - 提高钻孔精度，减少钻偏导致的焊盘破坏
>     - 提高 PCB 组装过程中的可靠性
> 4. **延长使用寿命**：通过降低应力集中点，泪滴可以延长 PCB 在热循环和机械冲击环境下的使用寿命。
> 5. **改善涂层覆盖**：泪滴设计有助于阻焊膜和其他涂层更均匀地覆盖拐角处，防止裸露和腐蚀。
>
> 泪滴主要应用于以下场景：焊盘与过孔连接处、高速信号线路、经常弯折的柔性电路板，以及需要承受机械应力的区域。虽然增加设计时间，但通常会显著提高 PCB 的可靠性和性能。
>

完成覆铜操作后，我又尝试了添加泪滴。需要在顶部菜单 编辑 > 添加泪滴，在弹出的“设置泪滴”窗口，按下“应用”按钮，就可以看到泪滴效果，如下图所示。

![](/images/week06/w06-p-27.png)

> 添加泪滴后电路板
>

最终版本的 PCB 3D 效果图如下图所示。

![](/images/week06/w06-p-28.png)

> 特别感谢钱嘉涛、周志怀，在 PCB 设计的学习中，给与了大量的指导和帮助。
>


# 为数控铣削法（CNC Milling）法电路板制作改进设计
因为第 8 周我们将会用数控铣削法制作电路板，目前这个方案使用了带过孔的封装，所以我准备修订一个版本，让这个设计能够满足后期作业的要求。

### 原项目建立副本
首先，为了保留原设计，我创建了一个项目副本：

1. 在 KiCad 文件管理器中，选择原项目文件夹。
2. 复制并重命名为"XIAO_ESP32C3_Extension_CNC2"。
3. 新项目的工程文件如下图所示。

![](/images/week06/w06-p3-1.png)

> 工程副本：XIAO_ESP32C3_Extension_CNC2
>

### 下载 Fab 元件库
使用 Fab Academy 提供的元件库，这些元件更适合 CNC 铣削法加工：

Fab Electronics Library for KiCad：[https://gitlab.fabcloud.org/pub/libraries/electronics/kicad](https://gitlab.fabcloud.org/pub/libraries/electronics/kicad)

资源库主页如下所示，有多种方式可以获取资源库，下载 zip 文件是方式之一，如下图所示。

![](/images/week06/w06-p3-2.png)

> 可以通过下载 zip 包的方式获取元件库
>

也可以使用 git 命令下载 Fab 元件库： 

```plain
git clone https://gitlab.fabcloud.org/pub/libraries/electronics/kicad.git
```

下载后将库放在一个自己找得到的文件目录下，我建立了一个 `/kicad/libraries/fab` 的目录，来存放元件库，如下图所示。

![](/images/week06/w06-p3-3.png)

> 下载并解包后的 fab 元件库
>

### 配置元件库路径： 
+ 在主项目界面下，进入"设置" > "配置路径"。
+ 添加新路径，命名为 `FAB` ，路径设置到下载的库文件夹：`/Users/<USER>/kicad/libraries/fab/`。如下图所示。
+ 这一步非常重要，否则3D视图将无法正确显示。

![](/images/week06/w06-p3-4.png)

> 为元件库配置路径
>

### 在 KiCad 中添加符号库： 
+ 打开"首选项" > "管理符号库"。
+ 点击"浏览"按钮，导航到下载的库文件夹。
+ 添加 `fab.kicad_sym` 符号库文件，如下图所示。

![](/images/week06/w06-p3-5.png)

> 添加 Fab 符号库
>

### 添加封装库： 
+ 打开"首选项" > "管理封装库"。
+ 点击"浏览"按钮，导航到下载的库文件夹。
    - 添加 `fab.pretty` 封装库文件夹。

![](/images/week06/w06-p3-6.png)

> 添加 fab.pretty 封装库
> 

### 使用 Fab 元件库替换现有元件符号与封装
对原理图进行修改，用贴片元件替换通孔元件：

1. 打开原理图编辑器。

![](/images/week06/w06-p3-7.png)

> 准备在原理图编辑器里替换 Fab 元件库的元件
>

2. 选中要替换的元件符号，点鼠标右键打开辅助菜单，选择“替换符号”，然后搜索 xiao，选择 Fab 目录下的 Module_XIAO-ESP32C3，注意这里提供的引脚比 Seeed 提供的元件符号的引脚要多，因为它包含了 XIAO ESP32C3 底部的引脚，如下图所示。

![](/images/week06/w06-p3-8.png)

> 替换 XIAO ESP32C3 为 Fab 库的元件
>

替换后会看到原来设置的 XIAO ESP32C3 的网络标签和新的 XIAO ESP32C3 引脚没有对应上，如下图红框所示，需要重新调整位置。

![](/images/week06/w06-p3-9.png)

> 原来设置的 XIAO ESP32C3 的网络标签和新的 XIAO ESP32C3 引脚没有对应上
>

重新调整网络标签和引脚的对应关系，调整后如下图所示。

![](/images/week06/w06-p3-10.png)

> 重新调整网络标签和引脚的对应关系
>

3. 查找并继续替换以下元件，可以一次多选多个同样规格的元件，进行和上一步类似的操作，批量替换元件。 
+ 批量替换所有 LED 改为"LED-1206"（使用 Fab 库中的贴片LED）。
+ 批量替换所有电阻改为"R-1206"（使用 Fab 库中的贴片电阻）。
+ 按钮替换为"Switch_Tactile_Omron"。
+ 注意在“替换指定符号”要在“新的库标识”栏选 Fab 库的元件，如下图所示。

![](/images/week06/w06-p3-11.jpg)

> 替换 按钮 元件为 Fab 库中的按钮
>

现在所有元件都替换为 Fab 库中的元件，并修改所有连接，如下图所示。

![](/images/week06/w06-p3-12.png)

> 所有元件都被替换为 Fab 库中的元件。
>

4. 点击符号字段表， 在`Footprint`（封装）栏逐个替换元件的封装，注意可以打开底部的 3D 模型显示开关，如下图所示，现在你可以同时看到选中元件的封装和 3D 效果。

![](/images/week06/w06-p3-13.jpg)

> 逐一为新的元件指定对应的封装，并检查 3D 模型是否能正常显示
>

![](/images/week06/w06-p3-14.png)

> 全部重新指定封装后的符号字段表
>

现在可以导出物料清单 

[XIAO_ESP32C3_Extension_CNC2.csv](https://www.yuque.com/attachments/yuque/0/2025/csv/2392200/1741997642191-9e204520-b562-4cec-a341-a6f1a27f9089.csv)

| <font style="color:#000000;">Reference</font> | <font style="color:#000000;">Qty</font> | <font style="color:#000000;">Value</font> | <font style="color:#000000;">DNP</font> | <font style="color:#000000;">Exclude from BOM</font> | <font style="color:#000000;">Exclude from Board</font> | <font style="color:#000000;">Footprint</font> |
| --- | --- | --- | --- | --- | --- | --- |
| <font style="color:#000000;">D1,D2,D3,D4,D5,D6</font> | <font style="color:#000000;">6</font> | <font style="color:#000000;">LED_1206</font> | | | | <font style="color:#000000;">Fab:LED_1206</font> |
| <font style="color:#000000;">J1</font> | <font style="color:#000000;">1</font> | <font style="color:#000000;">PinHeader_01x08_P2.54mm_Horizontal_SMD</font> | | | | <font style="color:#000000;">Fab:PinHeader_01x08_P2.54mm_Horizontal_SMD</font> |
| <font style="color:#000000;">M1</font> | <font style="color:#000000;">1</font> | <font style="color:#000000;">Module_XIAO-ESP32C3</font> | | | | <font style="color:#000000;">Fab:SeeedStudio_XIAO_ESP32C3</font> |
| <font style="color:#000000;">R1,R2,R3,R4,R5,R6,R7,R8,R9</font> | <font style="color:#000000;">9</font> | <font style="color:#000000;">R_1206</font> | | | | <font style="color:#000000;">Fab:R_1206</font> |
| <font style="color:#000000;">SW1</font> | <font style="color:#000000;">1</font> | <font style="color:#000000;">Switch_Tactile_Omron</font> | | | | <font style="color:#000000;">Fab:Button_Omron_B3SN_6.0x6.0mm</font> |


### PCB布局重新设计
由于更换了元件和封装，PCB 需要重新设计布局和布线。我切换到 PCB 编辑器，如下图所示，原来的设计已经不适用了，所以我全选并删除。

![](/images/week06/w06-p3-15.png)

> 原来的 PCB 设计已经不适用了
>

1. 点击“从原理图更新 PCB”（快捷键是 F8），将所有元件放置在编辑区，如下图所示。

![](/images/week06/w06-p3-16.png)

> 从原理图更新并放置所有元件
>

![](/images/week06/w06-p3-17.jpg)

> 按 oputin+3 （ windows 是 Alt+3）键，用 3D 模式可以看到所有模型
>

2. 调整PCB设计规则，适应CNC铣削法工艺限制： 
    - 走线宽度设置为至少0.4mm
    - 间隙设置为至少0.4mm
    - 避免使用过孔(via)设计

电路板配置如下图所示。

![](/images/week06/w06-p3-18.png)

> 电路板配置面板的设置
>

1. 调整元件布局，让线尽量少交叉。

![](/images/week06/w06-p3-19.png)

> 调整元件布局，先做到初步合理
>

2. 用"布线"工具（快捷键是“x”），使用 0.5mm 的线宽重新连接所有走线，布线完成后，元件的位置有所变化，因为是一层板，所以元件位置需要考虑避免线路交叉问题。

![](/images/week06/w06-p3-20.png)

> 完成初步的布线，这个过程蛮花时间，需要做很多尝试
>

3. 确保所有走线都在电路板的顶层(F.Cu)，不要使用底层。
4. 添加文本或表情符号到电路板设计（可选）。
5. 切换到 `Edge.Cuts` 层，绘制电路板边界，如下图所示。

![](/images/week06/w06-p3-21.png)

> 添加了文字和切割
>

6.  在`Edge.Cuts` 层选中边框，打开辅助菜单，为电路板边界设置 3mm 半径的圆角，如下图所示。

![](/images/week06/w06-p3-22.png)

> 为边框设置 3mm 半径的圆角
>

### 重新添加泪滴
1. 在顶部菜单选择"编辑" > "添加泪滴"。
2. 在弹出的"设置泪滴"窗口中，配置适合 CNC 工艺的泪滴参数
3. 点击"应用"按钮查看效果。
4. 确认泪滴设计不会导致走线过窄。

![](/images/week06/w06-p3-23.png)

> 添加泪滴后的电路板
>

### 重新覆铜
1. 选择 F.Cu 层，在右侧选择“绘制填充区域”功能，点击编辑区，在弹出的覆铜区属性窗口中，在网络中选择 `GND`。
2. 绘制覆铜区域，包围整个电路板设计区域。
3. 点击菜单"编辑" > "填充所有填充区"或按快捷键"B"。
4. 确保覆铜区域与信号线保持足够的间距。

完成覆铜操作后的效果如下图所示

![](/images/week06/w06-p3-24.png)

> 完成覆铜操作后的电路板，GNG 部分被连成片
>

### 运行 DRC 检查
在导出文件前进行设计规则检查：

1. 点击顶部菜单"检查" > "运行DRC"
2. 确保没有错误，但我有很多文字和铜链接过窄警告。
3. 特别检查间距和走线宽度是否满足 CNC 加工要求

看上去没什么大问题，不过很多丝印文字的位置有明显的问题，如下图所示，可以调整一下。

![](/images/week06/w06-p3-25.png)

> 运行 DRC 检查，要确保错误为 0
>

适当调整丝印的位置，如下图所示。

![](/images/week06/w06-p3-26.png)

> 调整了部分丝印文字的位置。
>

最后，使用 3D 查看器可以看到效果图。

![](/images/week06/w06-p3-27.png)

> 最终的 PCB 设计稿在 3D 查看器里的效果图
>

### 导出 Gerber 文件
1. 点击"文件" > "为制造输出" > "Gerber"，如下图所示。

![](/images/week06/w06-p3-28.png)

> 准备为制造输出 Gerber 文件
>

2. 确保选择以下层： 
    - F.Cu（顶层铜）
    - Edge.Cuts（切割边缘）
3. 设置适当的导出选项： 
    - 使用4.6格式
    - 单位设置为毫米
    - 导出原点设置为坐标原点

打开“绘制”窗口如下图所示。

![](/images/week06/w06-p3-29.png)

> 绘制窗口的包含层，选择 F.Cu（顶层铜）和 Edge.Cuts（切割边缘）层
>

点击“绘制”窗口的“绘制”按钮后，项目文件夹下会多出 2 个导出的 `.gbr`文件，如下图所示。

![](/images/week06/w06-p3-30.png)

> 项目文件夹下多出 2 个导出的 `.gbr`文件
>

### 使用 Gerber2Png 导出切割用的 PNG
为了使用 MOD 软件进行 CNC 切割，需要将 Gerber 文件转换为 PNG 格式：

1. 访问在线工具 [Gerber2Png](https://gerber2png.fablabkerala.in/)
2. 上传生成的 Gerber文件（F_Cu.gbr和Edge_Cut.gbr），可以同时选中 2 个文件一起上传，上传后如下图所示。

![](/images/week06/w06-p3-31.png)

> 上传两个 .gbr 文件后的界面效果
>

3. 设置导出参数： 
+ 在 Quick Setup 先选择 `Top trace` ，按 `Generate PNG` 按钮，会在右侧看到一个覆铜层的 PNG。
+ 然后在 Quick Setup 先选择 `Top Cut` ，按 `Generate PNG` 按钮，会在右侧增加一个边缘切割层的 PNG，如下图所示。

![](/images/week06/w06-p3-32.png)

> Gerber2Png 的导出界面
>

4. 点击右侧的 `DOWNLOAD ZIP` 按钮，可以获得一个 zip 压缩文件 `gerber_files_2.zip`。
5. 解压后可以看到生成的两个 PNG 文件，如下图所示
+ traces_top_layer_0.png（用于铜层铣削）
+ outline_top_layer_1.png（用于电路板外形切割）

![](/images/week06/w06-p3-33.png)

> 下载解压可以看到 2 个 png 文件。
>

下面是 2 个文件的源文件。

![](/images/week06/traces_top_layer_0.png)

![](/images/week06/outline_top_layer_1.png)

这些 PNG 文件将在第8周使用，届时会通过 MOD 软件生成数控机床的 G 代码，用于制作实体电路板。采用表面贴装工艺和单面 PCB 设计，确保了电路板能够通过数控铣削法成功加工。
