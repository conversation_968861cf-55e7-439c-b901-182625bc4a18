---
layout: doc
title: "第9周：输入设备 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第九周：学习各种输入设备和传感器的工作原理与应用，包括按钮、温湿度传感器、光敏元件、加速度计等各类传感器技术"
head:
  - - meta
    - name: keywords
      content: fab academy, 输入设备, 传感器, 按钮, 温湿度传感器, 加速度计, 步进响应, 电容感应, 信号处理, 示波器
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第8周：个人作业：电子电路板生产'
  link: '/zh/assignments/week08/week08_individual_assignment_pcb_production_cn'
next:
  text: '第9周：小组作业：使用示波器测量 Grove 传感器'
  link: '/zh/assignments/week09/week09_group_sensors_measurement_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第9周：输入设备

> 本文档内容由我向 Claude 3.7 Sonnet 提供[课程大纲](http://academy.cba.mit.edu/classes/input_devices/index.html)内容，以及本课程视频会议课程的字幕后，由 AI 提炼生成。
>

## 课程概要
本周课程专注于输入设备和传感器技术，教授如何将各种传感器连接到微控制器并从中读取数据。课程涵盖了多种输入设备，包括按钮开关、磁场传感器、步进响应测量、温度传感器、光传感器、运动传感器、距离传感器、实时时钟、位置传感器、加速度计、声音传感器、力量传感器和摄像头等。学生将学习如何使用这些传感器测量物理量，并将这些信息输入到数字系统中进行处理。

## 详细课程内容
### 1. 课程简介与背景
在制造电子设备的过程中，输入设备是与外部世界交互的关键环节。本周课程将深入探讨各种传感器技术及其应用。在接下来的几周中，我们将依次学习输出设备（如电机和显示器）、多处理器间的通信以及如何构建完整的机器系统。

### 2. 微控制器输入基础
#### 2.1 数据手册与引脚功能
+ **数据手册参考**：了解 [AVR DB 系列微控制器的数据手册](http://inventory.fabcloud.io/Input%20Devices/inputs)，特别关注模拟接口部分
+ **基本引脚类型**： 
    - 数字输入/输出引脚（ports）
    - 模拟比较器（comparator）
    - 模拟数字转换器(A/D)
    - [I2C接口](https://www.nxp.com/docs/en/application-note/AN10216.pdf)

#### 2.2 信号处理基础
+ **模拟比较器**：快速比较两个电压值
+ **模拟数字转换器**：将模拟信号转换为数字值
+ **I2C通信**：越来越多的传感器使用I2C通信协议

### 3. 按钮和开关
#### 3.1 按钮原理
+ **按钮类型**：[瞬时按钮](https://www.digikey.com/en/products/detail/omron-electronics-inc-emc-div/B3SN-3112P/27856)、[滑动开关](https://www.digikey.com/en/products/detail/c-k/AYZ0102AGRLC/1640108)
+ **去抖动(Debouncing)**：按钮按下时会产生振荡信号，需要通过软件或硬件去抖

#### 3.2 按钮编程示例
以下是基于 ESP32-C3 的按钮示例：

+ [电路板设计](https://claude.ai/embedded_programming/ESP32-C3/hello.button-blink.C3.png)
+ [元件](https://claude.ai/embedded_programming/ESP32-C3/hello.button-blink.C3.jpg)
+ [Arduino代码](https://claude.ai/embedded_programming/ESP32-C3/hello.button-blink.C3.ino)
+ [MicroPython代码](https://claude.ai/embedded_programming/ESP32-C3/hello.button-blink.C3.py)
+ [演示视频](https://claude.ai/embedded_programming/ESP32-C3/hello.button-blink.C3.mp4)

其他微控制器示例：

+ SAM D11C: [板设计](https://claude.ai/chat/button/hello.button.D11C.png)、[代码](https://claude.ai/chat/button/hello.button.D11C.ino)
+ ATtiny412: [板设计](https://claude.ai/chat/button/hello.button.t412.png)、[代码](https://claude.ai/chat/button/hello.button.t412.ino)

### 4. 磁场传感器
#### 4.1 霍尔效应传感器
+ **原理**：[霍尔效应传感器](https://www.digikey.com/en/products/detail/allegro-microsystems/A1324LLHLT-T/2639989)测量磁场并输出相应电压
+ **方向性**：翻转传感器可以测量不同方向的磁场
+ **灵敏度**：足够灵敏可以测量地球磁场
+ **应用**：检测盖子关闭状态、轴接近终点
+ **示例**：[hello.mag.45](https://claude.ai/chat/mag/hello.mag.45)、[板设计](https://claude.ai/chat/mag/hello.mag.45.png)、[代码](https://claude.ai/chat/mag/hello.mag.45.c)、[视频](https://claude.ai/chat/mag/hello.mag.45.mp4)

#### 4.2 矢量磁力计
+ **3轴磁场测量**：[矢量磁力计](https://www.digikey.com/en/products/detail/infineon-technologies/TLE493DA2B6HTSA1/9808570)测量X、Y、Z三个方向的磁场
+ **应用**：[应用案例](https://www.infineon.com/dgdl/Infineon-3D_Magnetic_Sensors-ProductBrief-v05_00-EN.pdf?fileId=5546d46261d5e6820161e7571b2b3dd0)包括操纵杆、换挡杆位置检测、滚轮界面等
+ **I2C通信**：通过I2C协议传输数据
+ **示例**：[hello.TLE493D.t412](https://claude.ai/chat/mag/TLE493D/hello.TLE493D.t412)、[板设计](https://claude.ai/chat/mag/TLE493D/hello.TLE493D.t412.png)、[代码](https://claude.ai/chat/mag/TLE493D/hello.TLE493D.t412.ino)、[视频](https://claude.ai/chat/mag/TLE493D/hello.TLE493D.t412.mp4)
+ **模拟工具**：[3D磁传感器模拟器](https://design.infineon.com/3dsim/)

### 5. 可变电阻器(电位器)
+ **原理**：[电位器](https://www.digikey.com/en/products/detail/nidec-copal-electronics/ST4ETB103/738213)通过旋转或滑动改变电阻值
+ **应用**：调整设置、控制电机电流等
+ **示例**：[电机控制应用](http://academy.cba.mit.edu/classes/output_devices/DRV8428/hello.DRV8428-D11C-NEMA17.jpg)

### 6. 步进响应测量(Step Response)
#### 6.1 电容感应原理
+ **测量原理**：基于充放电时间测量电容变化
+ **应用范围**：测量电阻、电容、电感、位置、压力、倾斜、加速度、湿度、接近度等
+ **人体电容测量**：利用人体导电特性测量接触或接近
+ **动画演示**：[步进响应模拟](https://claude.ai/chat/step/sim/step.mp4)

#### 6.2 自电容测量(单引脚)
+ **硬件支持**： 
    - [QTouch](https://developerhelp.microchip.com/xwiki/bin/view/products/mcu-mpu/32bit-mcu/sam/samd21-mcu-overview/peripherals/ptc-overview/)
    - [FreeTouch库](https://github.com/adafruit/Adafruit_FreeTouch)
    - [ESP32触摸功能](https://docs.espressif.com/projects/esp-idf/en/stable/esp32/api-reference/peripherals/touch_pad.html)
+ **示例**： 
    - SAMD21：[hello.touch.D21](https://claude.ai/chat/step/D21/hello.touch.D21)、[代码](https://claude.ai/chat/step/D21/hello.touch.D21.ino)、[视频](https://claude.ai/chat/step/D21/hello.touch.D21.mp4)
    - ESP32S3：[hello.touch.S3](https://claude.ai/chat/step/ESP32S3/hello.touch.S3)、[代码](https://claude.ai/chat/step/ESP32S3/hello.touch.S3.py)、[视频](https://claude.ai/chat/step/ESP32S3/hello.touch.S3.mp4)
+ **自制电容测量**： 
    - RP2040：[hello.steptime1.RP2040](https://claude.ai/chat/step/RP2040/hello.steptime1.RP2040)、[代码](https://claude.ai/chat/step/RP2040/hello.steptime1.RP2040.py)、[视频](https://claude.ai/chat/step/RP2040/hello.steptime1.RP2040.mp4)

#### 6.3 远程处理器测量
+ **原理**：将处理器放在靠近电极的位置，避免线路噪声
+ **示例**：[hello.load.RP2040.t412](https://claude.ai/chat/step/t412/hello.load.RP2040.t412)、[代码](https://claude.ai/chat/step/t412/hello.load.RP2040.py)、[视频](https://claude.ai/chat/step/t412/hello.load.RP2040.t412.mp4)
+ **应用**：[电容式卡尺](https://claude.ai/chat/step/caliper.jpg)、[CVDT](https://claude.ai/chat/step/steptime/CVDT.jpg)、[视频](https://claude.ai/chat/step/steptime/CVDT.mp4)

#### 6.4 互电容测量(双引脚)
+ **原理**：一个电极发送信号，另一个接收
+ **优势**：不依赖于室内地线，更加稳定可靠
+ **示例**： 
    - RP2040：[hello.txrx2.RP2040](https://claude.ai/chat/step/hello.txrx2.RP2040)、[代码](https://claude.ai/chat/step/hello.txrx2.RP2040.ino)、[视频](https://claude.ai/chat/step/hello.txrx2.RP2040.mp4)
    - ATtiny1624：[hello.txrx.t1624](https://claude.ai/chat/step/hello.txrx.t1624)、[代码](https://claude.ai/chat/step/hello.txrx.t1624.ino)、[视频](https://claude.ai/chat/step/hello.txrx.t1624.mp4)

#### 6.5 远程放大器测量
+ **原理**：使用[运算放大器](https://www.digikey.com/en/products/detail/texas-instruments/TLV365DBVR/17748355)增强信号
+ **示例**：[hello.txrx.RP2040.op-amp](https://claude.ai/chat/step/RP2040/hello.txrx.RP2040.op-amp)、[代码](https://claude.ai/chat/step/RP2040/hello.txrx.RP2040.op-amp.ino)、[视频](https://claude.ai/chat/step/RP2040/hello.txrx.RP2040.op-amp.mp4)

#### 6.6 介电谱测量
+ **高频应用**：在更高频率下可以测量物质特性
+ **物质识别**：可以区分酒精、马提尼、啤酒、葡萄酒等不同液体
+ **参考资料**：[应用说明](https://claude.ai/chat/DS.pdf)、[理论](https://claude.ai/chat/meas.pdf)、[测量方法](http://cba.mit.edu/docs/theses/17.06.VanWyk.pdf)

#### 6.7 应用示例
+ [触摸界面](http://fab.cba.mit.edu/classes/863.10/people/matt.blackshaw/week8.html)
+ [多点触控](http://fab.cba.mit.edu/classes/863.11/people/matthew.keeter/multitouch/index.html)
+ [力传感器](https://fabacademy.org/2020/labs/leon/students/adrian-torres/adrianino.html#step)
+ [弯曲测量](https://dl.acm.org/doi/pdf/10.1145/3313831.3376269)

### 7. 温度传感器
#### 7.1 热敏电阻桥式电路
+ **原理**：[NTC热敏电阻](https://www.digikey.com/en/products/detail/amphenol-thermometrics/NHQ103B375T10/374815)或[RTD热敏电阻](https://www.digikey.com/en/products/detail/vishay-beyschlag-draloric-bc-components/PTS120601B1K00P100/1666188)的电阻值随温度变化
+ **示例**：[hello.temp.45](https://claude.ai/chat/temp/hello.temp.45)、[代码](https://claude.ai/chat/temp/hello.temp.45.c)、[视频](https://claude.ai/chat/temp/hello.temp.45.mp4)

#### 7.2 红外温度测量
+ **原理**：[红外温度传感器](https://www.digikey.com/catalog/en/partgroup/mlx90614-15/20353)测量物体发出的红外辐射
+ **应用**：非接触式温度测量

### 8. 光传感器
#### 8.1 光电晶体管
+ **类型**：[红外](http://www.digikey.com/product-detail/en/everlight-electronics-co-ltd/PT15-21B-TR8/1080-1379-1-ND)和[可见光](http://www.digikey.com/product-detail/en/everlight-electronics-co-ltd/PT15-21C-TR8/1080-1380-1-ND)光电晶体管
+ **示例**：[hello.light.45](https://claude.ai/chat/light/hello.light.45)、[代码](https://claude.ai/chat/light/hello.light.45.c)、[视频](https://claude.ai/chat/light/hello.light.45.mp4)

#### 8.2 同步检测
+ **原理**：[同步检测技术](http://www.cambridge.org/us/knowledge/isbn/item6598594/The%20Physics%20of%20Information%20Technology/?site_locale=en_US)消除环境光干扰
+ **示例**：[hello.reflect.45](https://claude.ai/chat/light/hello.reflect.45)、[代码](https://claude.ai/chat/light/hello.reflect.45.c)、[视频](https://claude.ai/chat/light/hello.reflect.45.mp4)

#### 8.3 颜色传感器
+ **RGB传感器**：[颜色传感器](https://www.digikey.com/en/products/detail/vishay-semiconductor-opto-division/VEML6040A3OG/5168308)测量红、绿、蓝三种颜色
+ **示例**：[hello.VEML6040.t412](https://claude.ai/chat/color/hello.VEML6040.t412)、[代码](https://claude.ai/chat/color/hello.VEML6040.ino)、[视频](https://claude.ai/chat/color/hello.VEML6040.mp4)
+ **手势传感器**：[APDS-9960](https://www.broadcom.com/products/optical-sensors/integrated-ambient-light-and-proximity-sensors/apds-9960)可检测手势

### 9. 运动传感器
#### 9.1 多普勒雷达
+ **原理**：[多普勒雷达模块](https://www.amazon.com/RCWL-0516-Detection-Microwave-Raspberry-Detector/dp/B07GCHY9K6)测量多普勒频移，检测运动
+ **示例**：[hello.RCWL-0516](https://claude.ai/chat/radar/hello.RCWL-0516)、[代码](https://claude.ai/chat/radar/hello.RCWL-0516.c)、[视频](https://claude.ai/chat/radar/hello.RCWL-0516.mp4)

#### 9.2 热释电传感器
+ **传感器**：[HC-SR501](https://www.amazon.com/DIYmall-HC-SR501-Motion-Infrared-Arduino/dp/B012ZZ4LPM)
+ **示例**：[hello.HC-SR501](https://claude.ai/chat/motion/hello.HC-SR501)、[代码](https://claude.ai/chat/motion/hello.HC-SR501.c)、[视频](https://claude.ai/chat/motion/hello.HC-SR501.mp4)

### 10. 距离传感器
#### 10.1 激光飞行时间测量
+ **传感器**： 
    - [VL53L0X](https://www.digikey.com/en/products/detail/stmicroelectronics/VL53L0CXV0DH-1/6023691)、[库](https://github.com/pololu/vl53l0x-arduino)
    - [VL53L1X](https://www.digikey.com/en/products/detail/stmicroelectronics/VL53L1CXV0FY-1/8258055)、[模块](https://www.digikey.com/en/products/detail/pololu-corporation/3415/10451121)
    - [VL53L5CX](https://www.digikey.com/en/products/detail/stmicroelectronics/VL53L5CXV0GC-1/14552424)（8x8阵列）
+ **示例**： 
    - [hello.VL53L0X.D11C](https://claude.ai/chat/tof/hello.VL53L0X.D11C)、[代码](https://claude.ai/chat/tof/hello.VL53L0X.D11C.ino)、[视频](https://claude.ai/chat/tof/hello.VL53L0X.D11C.mp4)
    - [hello.VL53L1X.t1614](https://claude.ai/chat/tof/hello.VL53L1X.t1614)、[代码](https://claude.ai/chat/tof/hello.VL53L1X.ino)、[视频](https://claude.ai/chat/tof/hello.VL53L1X.mp4)

#### 10.2 超声波传感器
+ **传感器**：[HC-SR04](http://www.amazon.com/SunFounder-Ultrasonic-Distance-Mega2560-Duemilanove/dp/B00E0NXTJW)
+ **示例**：[hello.HC-SR04](https://claude.ai/chat/sonar/hello.HC-SR04)、[代码](https://claude.ai/chat/sonar/hello.HC-SR04.c)、[视频](https://claude.ai/chat/sonar/hello.HC-SR04.mp4)

### 11. 实时时钟(RTC)
+ **芯片**：[PCF8523](https://www.digikey.com/en/products/detail/nxp-usa-inc/PCF8523T-1-118/2530422)、[模块](https://www.adafruit.com/product/3295)
+ **示例**：[hello.PCF8523.RP2040](https://claude.ai/chat/RTC/hello.PCF8523.RP2040)、[代码](https://claude.ai/chat/RTC/hello.PCF8523.ino)、[视频](https://claude.ai/chat/RTC/hello.PCF8523.RP2040.mp4)

### 12. 位置和时间传感器(GPS)
+ **系统**：[GNSS](https://www.gps.gov/systems/gnss)、[NMEA协议](https://gpsd.gitlab.io/gpsd/NMEA.html)
+ **模块**：[NEO-6](https://www.u-blox.com/sites/default/files/products/documents/NEO-6_DataSheet_(GPS.G6-HW-09005).pdf)、[GT-U7](https://images-na.ssl-images-amazon.com/images/I/91tuvtrO2jL.pdf)
+ **示例**：[hello.GPS.t1614](https://claude.ai/chat/GPS/hello.GPS.t1614)、[代码](https://claude.ai/chat/GPS/hello.GPS.t1614.ino)、[视频](https://claude.ai/chat/GPS/hello.GPS.t1614.mp4)

### 13. 加速度、旋转和方向传感器
#### 13.1 三轴加速度计
+ **芯片**：[ADXL343](https://www.digikey.com/en/products/detail/analog-devices-inc/ADXL343BCCZ/3542918)
+ **示例**：[hello.ADXL343](https://claude.ai/chat/accel/hello.ADXL343)、[代码](https://claude.ai/chat/accel/hello.ADXL343.c)、[视频](https://claude.ai/chat/accel/hello.ADXL343.mp4)

#### 13.2 六轴IMU(惯性测量单元)
+ **芯片**：[MPU-6050](https://invensense.tdk.com/products/motion-tracking/6-axis/mpu-6050)、[模块](https://www.amazon.com/HiLetgo-MPU-6050-Accelerometer-Gyroscope-Converter/dp/B00LP25V1A)、[ICM-20609](https://invensense.tdk.com/products/motion-tracking/6-axis/icm-20609)
+ **示例**：[hello.MPU-6050.RP2040](https://claude.ai/chat/imu/6050/hello.MPU-6050.RP2040)、[代码](https://claude.ai/chat/imu/6050/hello.MPU-6050.ino)、[视频](https://claude.ai/chat/imu/6050/hello.MPU-6050.RP2040.mp4)

#### 13.3 九轴IMU
+ **芯片**： 
    - [BNO085](https://www.digikey.com/en/products/detail/ceva-technologies-inc/BNO085/9445940)、[模块](https://www.adafruit.com/product/4754)
    - [BNO086](https://www.digikey.com/en/products/detail/ceva-technologies-inc/BNO086/14114190)
    - [MPU-9250](https://invensense.tdk.com/products/motion-tracking/9-axis/mpu-9250)
    - [ICM-20948](https://invensense.tdk.com/products/motion-tracking/9-axis/icm-20948/)、[模块](https://www.adafruit.com/product/4554)
+ **示例**：[hello.4754.RP2040](https://claude.ai/chat/imu/hello.4754.RP2040)、[代码](https://claude.ai/chat/imu/hello.4754.RP2040.py)、[视频](https://claude.ai/chat/imu/hello.4754.RP2040.mp4)

### 14. 声音传感器
#### 14.1 MEMS麦克风
+ **数字接口**：
    - [I2S协议](https://www.sparkfun.com/datasheets/BreakoutBoards/I2SBUS.pdf)
    - [底部端口](https://www.digikey.com/en/products/detail/tdk-invensense/ICS-43434/6140298)、[模块](https://www.adafruit.com/product/6049)
    - [顶部端口](https://www.digikey.com/en/products/detail/cui-devices/CMM-4030D-261-I2S-TR/13164051)
+ **示例**：
    - [hello.ICS-43434.RP2040](https://claude.ai/chat/mic/I2S/hello.ICS-43434.RP2040)、[代码](https://claude.ai/chat/mic/I2S/hello.ICS-43434.RP2040.ino)、[视频](https://claude.ai/chat/mic/I2S/hello.ICS-43434.RP2040.mp4)
    - [hello.CMM-4030D-261-I2S-TR.t1614](https://claude.ai/chat/mic/hello.CMM-4030D-261-I2S-TR.t1614)、[代码](https://claude.ai/chat/mic/hello.CMM-4030D-261-I2S-TR.ino)、[视频](https://claude.ai/chat/mic/hello.CMM-4030D-261-I2S-TR.mp4)
+ **模拟接口**：
    - [SPU0414HR5H](https://www.digikey.com/en/products/detail/knowles/SPU0414HR5H-SB-7/2420969)
    - **示例**：[hello.SPU0414HR5H](https://claude.ai/chat/mic/hello.SPU0414HR5H)、[代码](https://claude.ai/chat/mic/hello.SPU0414HR5H.c)、[视频](https://claude.ai/chat/mic/hello.SPU0414HR5H.mp4)

#### 14.2 驻极体麦克风
+ **放大器**：[AD8615运算放大器](http://www.digikey.com/product-detail/en/AD8615AUJZ-REEL7/AD8615AUJZ-REEL7CT-ND)
+ **示例**：[hello.mic.45](https://claude.ai/chat/mic/hello.mic.45)、[代码](https://claude.ai/chat/mic/hello.mic.45.c)、[视频](https://claude.ai/chat/mic/hello.mic.45.mp4)

### 15. 其他传感器简介
+ **振动传感器**：[压电传感器](http://www.jameco.com/webapp/wcs/stores/servlet/ProductDisplay?langId=-1&storeId=10001&catalogId=10001&productId=1956784)
+ **力传感器**： 
    - [电容式](http://www.designworldonline.com/capacitive-sensors-measure-low-forces)
    - [电阻式](https://github.com/IvDm/Z-probe-on-smd-resistors-2512)
    - [力敏电阻](http://www.interlinkelectronics.com/standard-products.php)
    - [应变计](http://www.omega.com/guides/straingages.html)
    - [称重传感器](http://www.omega.com/prodinfo/loadcells.html)
+ **角度传感器**：[编码器](https://www.digikey.com/products/en/sensors-transducers/encoders/507)
+ **压力传感器**：[DPS310](https://www.digikey.com/product-detail/en/infineon-technologies/DPS310XTSA1/DPS310XTSA1CT-ND/)
+ **脉搏传感器**：[MAX30102](https://www.digikey.com/product-detail/en/maxim-integrated/MAX30102EFD/MAX30102EFD-ND/6166869)
+ **空气污染传感器**：[颗粒物传感器](https://www.digikey.com/en/products/filter/particle-dust-sensors/509)
+ **气体传感器**：[各类气体传感器](https://www.pololu.com/category/83/gas-sensors)

### 16. 图像传感器
#### 16.1 摄像头模块
+ **ESP32摄像头**： 
    - [ESP32S3 XIAO Sense](https://www.seeedstudio.com/XIAO-ESP32S3-Sense-p-5639.html)、[模块](https://claude.ai/chat/image/hello.ESP32-Sense.jpg)、[摄像头](https://www.seeedstudio.com/OV5640-Camera-for-XIAO-ESP32S3-Sense-With-Heat-Sink-p-5739.html)
    - [ESP32-CAM](https://www.amazon.com/s?k=ESP32-CAM)
+ **示例**： 
    - [hello.ESP32-Sense.ino](https://claude.ai/chat/image/hello.ESP32-Sense.ino)、[视频](https://claude.ai/chat/image/hello.ESP32-Sense.mp4)
    - [hello.ESP32-CAM](https://claude.ai/networking_communications/ESP32/hello.ESP32-CAM)、[代码](https://claude.ai/networking_communications/ESP32/hello.ESP32-CAM.ino)、[视频](https://claude.ai/networking_communications/ESP32/hello.ESP32-CAM.mp4)

#### 16.2 人工智能模块
+ [Grove Vision AI模块](https://wiki.seeedstudio.com/Grove-Vision-AI-Module/)

#### 16.3 网络摄像头
+ [网络摄像头](https://www.google.com/search?tbm=shop&q=webcam)与[嵌入式Linux板](https://www.google.com/search?tbm=shop&q=embedded+linux+board)

#### 16.4 图像处理库
+ [OpenCV](http://opencv.org/)、[OpenCV.js](https://github.com/ucisysarch/opencvjs)、[SimpleCV](http://simplecv.org/)
+ [libuvc](https://int80k.com/libuvc/doc/)、[guvcview](http://guvcview.sourceforge.net/)

#### 16.5 网页图像处理
+ [WebRTC](https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API)
+ [示例](https://claude.ai/chat/video.html)、[视频](https://claude.ai/chat/video.mp4)

## 作业要求
### 小组作业
探测输入设备的模拟电平和数字信号：

1. 选择一种输入设备（如按钮、传感器等）
2. 使用示波器或其他工具测量其模拟电平
3. 观察并记录数字信号的特性
4. 分析信号模式并理解其工作原理

### 个人作业
测量某物：向你设计的微控制器板添加传感器并读取数据

1. 设计并制作一个包含微控制器和传感器的电路板
2. 编程读取传感器数据
3. 数据可视化（通过Arduino/Thonny内置绘图工具或Python代码）
4. 记录过程并撰写文档，说明传感器原理、应用和测量结果

**参考示例**：[Adrian Torres的输入设备项目](http://fabacademy.org/2020/labs/leon/students/adrian-torres/fabxiao.html#inputs)

**建议**：如果不确定选择哪种传感器，尝试使用步进响应(step response)技术，因为它只需要处理器引脚和简单的铜电极即可制作出各种有趣的界面和传感器。

## 学习资源
### 参考资料
1. [Input Devices课程主页](http://inventory.fabcloud.io/?purpose=Input%20Devices)
2. [AVR DB系列数据手册](https://claude.ai/embedded_programming/DB/AVR128DB28-DS40002247A.pdf)
3. [I2C通信协议说明](https://www.nxp.com/docs/en/application-note/AN10216.pdf)

### 编程库和工具
1. [Adafruit FreeTouch库](https://github.com/adafruit/Adafruit_FreeTouch)
2. [ESP32触摸传感器API](https://docs.espressif.com/projects/esp-idf/en

