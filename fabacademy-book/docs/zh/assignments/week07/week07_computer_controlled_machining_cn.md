---
layout: doc
title: "第7周：计算机控制加工 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第七周：学习使用大型数控铣床制作大型物品，掌握机器类型、加工材料、刀具选择、固定方法、加工策略和安全操作"
head:
  - - meta
    - name: keywords
      content: fab academy, 计算机控制加工, CNC, 数控铣床, ShopBot, 大型加工, 木工, 刀具, 板材, 安全操作
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第6周：个人作业：第一次尝试设计PCB'
  link: '/zh/assignments/week06/week06_individual_assignment_pcb_design_cn'
next:
  text: '第7周：小组作业：CNC切割'
  link: '/zh/assignments/week07/week07_group_assignment_cnc_cutting_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第 7 周：Fab Academy 2025 计算机控制加工课程教案
> 本文档内容由我向 Claude 3.7 Sonnet 提供[课程大纲](http://academy.cba.mit.edu/classes/computer_machining/index.html)内容，以及本课程视频会议课程的字幕后，由 AI 提炼生成。
>

## 课程概要
本课程介绍计算机控制加工技术，专注于使用大型数控铣床（如ShopBot）制作大型物品（米级尺寸）。课程内容包括机器类型、加工材料、刀具选择、固定方法、加工策略和安全操作。学生将学习如何设计、加工和组装大型物品，如家具或结构部件，而无需使用螺钉或胶水。

## 详细的课程内容介绍
### 一、课程导入
本周将学习如何制作大型物品，涉及更大的力、更大的材料和更重要的安全考虑。与之前的激光切割相比，我们将在更大的尺度上工作，通常使用4英尺×8英尺（约1.2米×2.4米）的木板作为材料。

### 二、成功案例展示
1. 学生作品展示：如Sam制作的船，展示了本课程能够实现的可能性。
2. 立式办公桌：一名学生为女友设计的立式办公桌，后来发展成了一个商业项目。
3. 开放式家具平台：OpenDesk和AtFab等平台提供开源家具设计。
4. 住房项目：从Shelter 2.0应急避难所到WikiHouse完整住宅，甚至包括Fab Lab House这样的完整功能住宅。

### 三、机器设备介绍
#### 1. 小型手持设备
+ Shaper：带计算机视觉的手持铣刀，可以带到工件处而非工件带到机器处
+ Maslow：使用绳索和锚点代替大型龙门架的平价替代方案

#### 2. 中型设备
+ ShopBot：最常见的数控铣床，尺寸通常为4×8英尺，价格在2-3万美元
+ Onsrud：更坚固的机器，能够更积极地加工，无需频繁调整，价格约10万美元

#### 3. 大型/专业设备
+ Haas/Hurco：价格约30万美元，具有多自由度（5轴）移动能力，可加工钛和不锈钢
+ Zund：配备移动真空台和高速机头，用于广告展示、赛车复合材料等
+ 自制机器：如Fellesverkstedet项目，可以使用机器本身制造的部件来构建机器

### 四、加工材料
#### 1. 常见材料
+ 纸板：三折纸板可用于制作轻量家具，最好用刀切割而非铣削
+ 刚性泡沫：用于建筑绝缘，易于加工，可作为建筑铸造的模具
+ 胶合板：木材层压，美观但较贵，约100美元/张
+ 中密度纤维板(MDF)：木纤维与树脂混合，结构性能较差但表面光滑，适合需要平滑表面的应用
+ 中密度覆盖板(MDO)：胶合板芯与MDF表面，结合两者优点
+ 定向刨花板(OSB)：由木材碎片粘合，结构性能不错且便宜（10-15美元/张），表面粗糙但可打磨和涂层处理

#### 2. 其他材料
+ 高密度聚乙烯(HDPE)：切菜板材质，易于加工
+ 聚碳酸酯(Lexan)：高硬度、高韧性聚合物，不能用普通激光切割但可以铣削
+ Garolite：纤维增强复合材料，易于加工且刚性好
+ 铝及铝复合材料：如Hylite，铝包覆聚合物芯，轻量且易于加工和折叠

### 五、刀具选择
#### 1. 刀具类型
+ 钻头与铣刀的区别：钻头仅设计为垂直切削，铣刀可横向和垂直切削
+ 铣刀与木工刀具：木工刀具（如修边机刀）设计用于2D切削，铣刀可制作任意3D形状

#### 2. 铣刀特性
+ 刃数(Flutes)：常见有2、3或4刃，更多刃产生更光滑的表面但芯片排出空间减少
+ 涂层：陶瓷涂层可显著延长工具寿命
+ 中心切削能力：中心切削刀可垂直向下切入材料，非中心切削刀需要侧向进入
+ 上切/下切：上切刀在顶部留下毛边但有利于排屑，下切刀在顶部产生干净表面但在底部留下毛边
+ 刀头形状：平头铣刀vs球头铣刀，球头适合平滑曲面和进入狭窄空间

### 六、速度与进给
+ 切屑负载(Chip Load)：每个刀刃每转切削的材料量，通常为0.001-0.010英寸 计算公式：进给率(英寸/分钟)/(RPM×刀刃数)
+ 切深(Cut Depth)：每次通过的切削深度，通常为刀具直径左右
+ 步进(Step-over)：相邻路径的重叠量，通常为刀具直径的一半

### 七、工件固定
#### 1. 固定方法
+ 虎钳：专业加工虎钳比普通虎钳贵得多，表面更平、更精确
+ 杆夹：简单的夹具，但不适用于大面积
+ 螺丝：使用电动螺丝刀和木工螺丝，可分布固定力
+ 特殊钉子：如Raptor Nails，由聚合物制成，可以直接铣削通过
+ 真空台：生产环境首选，需要定期维护
+ 其他方法：楔子、重物、胶水、胶带或包封

#### 2. 牺牲层与校正
+ 牺牲层：在机床台面上放置一层材料，先将其铣平，然后在上面固定工件
+ 校正：定期检查机器精度，确保切割出的方形部件确实是方形的

### 八、弯曲和联接技术
#### 1. 弯曲技术
+ 柔性铰链(Flexures)：切割多个小梁，每个可略微弯曲，使刚性平板变得可弯
+ 开槽(Kerfing)：不完全切透，保留连续的外表面同时允许弯曲
+ 蒸汽弯曲：通过蒸汽加热软化木材，弯成形状后定型
+ 特殊胶合板：单向层压，结构性能较差但易于弯曲

#### 2. 连接技术
+ 木工榫接：数百种传统连接方法，如楔形榫接是一种很好的选择，允许一定的材料厚度变化
+ 互惠框架(Reciprocal Frame)：一种结构设计，利用榫接创建特殊结构
+ 张拉整体结构(Tensegrity)：结合张力元素（绳索）和压缩元素（梁）创建看似悬浮的结构
+ 麦克斯韦准则(Maxwell Criterion)：计算自由度和约束，确保结构稳定性

### 九、刀具路径策略
#### 1. 切削方向和方法
+ 顺铣与逆铣：顺铣对机器施加更大力但留下更干净的切口，逆铣力小但表面较粗糙
+ 粗加工与精加工：粗加工快速去除材料，精加工产生光滑表面
+ 2D、2.5D与3D加工：从简单的2D切割到完全的3D曲面加工

#### 2. 特殊考虑
+ T形槽和狗骨形：为关节留出额外空间，补偿刀具形状限制
+ 避免碰撞：考虑刀具、刀柄和主轴的尺寸，确保有足够空间
+ 标签和表皮：保持零件在切削过程中的固定，避免切割完成时零件脱落
+ 嵌套：高效排列零件以最大化材料利用
+ 引入和引出：控制刀具进入和离开材料的方式
+ 测试切割和空切：在实际切割前进行测试，验证程序正确性
+ 仿真与原型：使用软件模拟切割过程，或用纸板/3D打印制作小比例原型

### 十、CAM软件
+ VCarvePro：ShopBot配套软件
+ Fusion 360：集成CAD和CAM，无缝过渡
+ FreeCAD Path：开源选项，功能不断改进
+ Mods：可进行基本的轮廓切割、粗加工和精加工

### 十一、文件格式
+ G代码：最通用的格式，但不同机器可能有细微差异
+ SBP：ShopBot专用格式
+ 其他专用格式

### 十二、安全操作
#### 1. 常见危险
+ 碎片、割伤、烧伤和冲击：材料碎片锋利，工具和材料会变热，部件可能飞出
+ 火灾风险：过度切削产生的热量可能点燃木屑，尤其在集尘器中危险
+ 工具破损：小铣刀破损只是烦人，大型工具破损可能产生高速弹片

#### 2. 安全预防措施
+ 个人防护：护目镜、合适的鞋子、无松散衣物、扎起头发
+ 感官警觉：观察、倾听和闻味，了解正常与异常状态
+ 正确操作：永远不要用手代替工具，不要伸手进入运转中的工具
+ 紧急停止：开始前了解如何停止机器
+ 协助与状态：不要单独操作大型设备，确保身体和心理状态良好

## 作业要求
### 小组作业
1. 完成实验室的安全培训
2. 测试机器的偏摆、对齐、固定、速度、进给、材料和刀具路径

### 个人作业
1. 设计、铣削和组装一个大型物品（约米级尺度）
2. 尝试不使用紧固件或胶水（加分项）
3. 尝试包含曲面（加分项）

### 注意事项
+ 合理安排时间，不要把所有加工工作都留到最后
+ 安全是首要考虑因素，确保在适当监督下操作

## 学习资源
### 1. 机器和设备
+ [Shaper Tools](https://www.shapertools.com/)
+ [Maslow CNC](https://www.maslowcnc.com/)
+ [ShopBot](https://www.shopbottools.com/)
+ [Tormach](https://www.tormach.com/)
+ [Haas](https://www.haascnc.com/)

### 2. 材料来源
+ [McMaster-Carr](http://www.mcmaster.com/#raw-materials)（综合材料）
+ [US Plastics](https://www.usplastic.com/)（塑料材料）
+ 当地木材和金属供应商

### 3. 连接和设计技术
+ [数字化接头参考海报](https://www.instructables.com/50-Digital-Joints-poster-visual-reference/)
+ [弯曲技术资源](https://www.google.com/search?q=steam+bending&tbm=isch)
+ [麦克斯韦准则](https://digital.nls.uk/scientists/archive/74629052)

### 4. 软件工具
+ [CAMotics](https://camotics.org/)（开源仿真）
+ [Fusion 360 CAM教程](https://www.autodesk.com/products/fusion-360/blog/getting-started-introduction-to-cam-and-toolpaths/)
+ [OpenBuilds CAM](https://cam.openbuilds.com/)
+ [Deepnest](https://deepnest.io/)（零件嵌套优化）

### 5. 安全资源
+ [安全培训指南](http://ehs.mit.edu/site/training)
+ [车间安全审查](http://www.popularmechanics.com/home/<USER>/yale-students-tragic-death-prompts-a-shop-safety-review)

