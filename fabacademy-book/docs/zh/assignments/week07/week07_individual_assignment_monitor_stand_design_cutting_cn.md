---
layout: doc
title: "第7周：个人项目：显示器支架设计与切割 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第七周个人作业：设计并制作一个大型显示器支架，通过无紧固件的设计实现简洁有效的组装"
head:
  - - meta
    - name: keywords
      content: fab academy, 显示器支架, 家具设计, Fusion 360, 参数化设计, CNC切割, 板材加工, 无紧固件设计
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第7周：小组作业：CNC切割'
  link: '/zh/assignments/week07/week07_group_assignment_cnc_cutting_cn'
next:
  text: '第8周：电子生产'
  link: '/zh/assignments/week08/week08_electronics_production_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第 7 周个人项目：显示器支架设计与切割

这节课的要求是设计、铣削和组装一个大型物品（约米级尺度），并尝试不使用紧固件或胶水（加分项）。我考虑为我的 1.6m × 0.6m 的可升降桌，制作一个宽大的显示器支架。

![](/images/week07/w7-p-1.jpg)

> 我的 1.6m × 0.6m 的可升降桌的样子，我希望在桌上增加一个显示器支架，以便更有效的利用桌面空间
>

## Fusion 360设计过程
设计目标：为 1.6m × 0.6m 的电脑升降桌，设计一个 1.5m × 0.25m，高 0.12m 的显示器支架。

**参数化设计**：通过参数化设计，可以轻松调整以适应后期加工配合问题，对应不同板材厚度。

### 第一步：基础绘制与参数化设计
1. 创建参考桌面
    - 进入草图环境，在顶视图创建基础2D草图
    - 设计一个长方形，尺寸为1600mm × 250mm作为顶板基础
    - 在长方形四角添加R30.00圆角，使边缘更美观安全

草图设计如下图所示。

![](/images/week07/w7-p-2.png)

> 用参数化设计创建参考升降桌面的草图
>

2. 然后挤出参考桌面的实体，我还指定了一个木质材质效果，如下图所示。

![](/images/week07/w7-p-3.png)

> 挤出可升降桌面，用于评估显示器支架的效果
>

### 第二步：顶板绘制与特征添加
1. 设置顶板草图关键参数
    - 长度：1500mm
    - 宽度：250mm
    - 板材厚度：18mm（根据CNC加工规格）
    - 圆角半径：R25.00（四角）
2. 细化草图，添加必要的连接孔和定位孔
    - 在顶板两端绘制多个矩形孔（40mm × 18mm）用于支撑卡槽连接，在进行切割前，徐工将矩形孔调整为 46mm × 18mm，以便能够顺利安装
    - 设置对称约束确保设计平衡
    - 在中心位置添加导引线和定位点
3. 添加尺寸标注
    - 顶板总长：1500mm
    - 总宽：250mm
    - 连接槽长度：40mm
    - 连接槽宽度：18mm

![](/images/week07/w7-p-4.png)

> 参数化设计的顶板草图，初始设计嵌入板材的槽孔为 40mm × 18mm。
>

根据实际加工要求，我将方形槽孔的宽度，由原来的 40mm，调整为 46mm，以便让板材能顺利嵌入。

![](/images/week07/w7-p-5.png)

> 修改后的槽孔调整为 46×18 mm，左右两侧靠边的槽孔深度也调整为 43mm
>

4. 挤出顶板
    - 选择主体草图
    - 使用"挤出"命令创建实体
    - 设置挤出厚度：18mm
    - 应用适当的材质（木材纹理）

![](/images/week07/w7-p-6.png)

> 挤出后的顶板效果图
>

### 第三步：支架设计
1. 创建支架草图
    - 在合适的工作平面（xz）上绘制支架截面
    - 设计支架形状， 做了 2 个齿以使其能够稳固支撑顶板
    - 支架齿宽度：40mm
    - 支架齿高度：18mm
    - 支架宽度：150mm
    - 支架高度：120mm
    - 支架内部圆角：R20.00

![](/images/week07/w7-p-7.png)

2. 挤出支架
    - 将支架草图挤出至合适厚度
    - 复制支架，在顶板底部均匀分布共7个支架
    - 确保支架间距合理，提供足够支撑力

![](/images/week07/w7-p-8.png)

### 第四步：整体效果
最终完成的效果如下图所示，整个结构通过板材嵌入进行连接，不需要额外的钉子。

![](/images/week07/w7-p-9.png)

> 桌面显示器最终模样的效果图
>

![](/images/week07/w7-p-10.png)

> 可以看到为方便嵌入预留宽度在效果图中的痕迹
>

## 导出准备加工
1. 确认各部件的正确定位和尺寸。
2. 将设计草图（顶板与支撑）导出为DXF文件，便于CNC加工。

![](/images/week07/w7-p-11.png)

> 在草图上点击右键，导出支撑的 DXF
>

现在有了 2 个供切割的 DXF 文件。

![](/images/week07/w7-p-12.png)

> 从 Fusion 草图导出的顶板与支撑的 dxf 文件
>

接下来，我在 Adobe Illustrator 中，将两个 dxf 合并，并排练支撑所需的 7 个支撑。

![](/images/week07/w7-p-13.png)

> 在 Adobe Illustrator 中合并 dxf 文件为 1 个，并按实际切割需要的支撑数量复制和排列
>

最终导出一个新的 dxf 文件。

## MasterCAM X6加工准备
在MasterCAM中，在徐国群工程师的指导下，我们进行了以下步骤：

1. **导入设计文件**：导入或DXF文件，并定义板材空间，如下图所示。

![](/images/week07/w7-p-14.png)

> 导入 dxf
>

2. **布局加工设计文件与刀具路径**：这部分工作比较复杂，基本是徐工操作，我们观摩。

为刀具路径设置刀具路径外形

![](/images/week07/w7-p-15.png)

> 为刀具路径设置刀具路径外形
>

![](/images/week07/w7-p-16.png)

> 设置刀具，包括刀具直径 8mm，进给率 5000，主轴转速 15000 等关键参数
>

![](/images/week07/w7-p-17.png)

> 设置切削参数
>

![](/images/week07/w7-p-18.png)

> 刀具外形铣削的共同参数设置
>

还需要对刀具路径进行串联管理设置，根据前面介绍的“由内到外，由小到大”的原则，安排刀具的切割路径。

![](/images/week07/w7-p-19.png)

> 规划刀具的切割路径
>

通过模拟刀具路径，可以检查切割过程的细节，注意需要掏空的孔洞刀具线条是在线条的内援运动，用于剪切外轮廓的刀具路线在线条的外援，如下图模拟的刀具路径所示，可以看到切割过程是分层进行的。

![](/images/week07/w7-p-20.png)

> 分层的刀具切割路径模拟
>

![](/images/week07/w7-p-21.png)

> 可以在 3D 模式下模拟刀具的切割过程
>

3. **设置零点**：完成布局后，需要设置加工零点。
4. **生成加工文件**：设置正确的参数后，生成最终 [G-Code](https://en.wikipedia.org/wiki/G-code) 加工文件。

![](/images/week07/w7-p-22.png)

> 我的书桌最终用于 CNC 切割的 G-Code 文件
>

## CNC切割过程
在切割过程中，我们遵循了以下步骤：

1. **放置加工板**
2. **固定板材**
3. **调整加工零点
4. **开启空气处理设备
5. **确认加工设置
6. **CNC切割过程监控**

**切割过程中失去和大板连接的小部件，要及时暂停（按 F10 键），等刀停转后，移走，否则容易在最后一圈破坏位移的零件。**

![](/images/week07/w7-p-23.jpg)

> 需要及时清理脱离主板材的小部件，注意移走活动部件要在暂停后钻头停转进行
>

1. **清理加工区域**

## 组装显示器支架
在切割完所有部件后，我进行了组装：

1. **检查部件**
2. **最终切割效果**
3. **内角修整**
4. **组装连接**：用橡胶锤将修整好的板材敲打在一起，完成成品。

![](/images/week07/w7-p-24.jpg)

> 打印出的电脑桌零件
>

![](/images/week07/w7-p-25.jpg)

> 因为有上下 3mm 的预留，所以支撑腿很容易就插进顶板了
>

![](/images/week07/w7-p-26.jpg)

> 安装好后测试了一下支撑能力，很结实
>

## 成果展示
支架和顶板的缝隙我用热熔胶枪做了填充，让支撑能稳固的和顶板结合。

![](/images/week07/w7-p-27.jpg)

> 用热熔胶枪填充了接缝，人生的第一件自己设计制造的家具完工
>

实际使用效果让我很满意。

![](/images/week07/w7-p-28.jpg)

> 马上就投入使用，摆在了我的桌面上
>

## 设计源文件
我的显示器支架的 Fusion 设计文件获取：[https://a360.co/41OZWJv](https://a360.co/41OZWJv)

DXF 文件，G-Code 的 zip 文件下载：[Lei-Feng-desk-dxf-nc.zip](/images/week07/Lei-Feng-desk-dxf-nc.zip)

## 经验总结
1. **设计考虑加工限制**：在设计时需考虑CNC加工的特性，避免内角等难以加工的结构。
2. **刀具选择重要性**：优质的刀具对于成功切割至关重要，软刀具容易断裂导致工作中断。
3. **参数化设计优势**：通过参数化设计，可以轻松适应不同厚度的材料和调整尺寸。
4. **内孔设计**：对于需要镶嵌板材的细小方形内孔，应将宽度左右各增大3mm，确保板材可以嵌入。
5. **安全第一**：在整个过程中，安全始终是首要考虑因素，包括正确佩戴防护装备和了解紧急停止程序。