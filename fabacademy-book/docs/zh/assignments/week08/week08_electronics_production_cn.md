---
layout: doc
title: "第8周：电子产品制作 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第八周：学习电子产品制作技术，包括PCB铣削、元件焊接及测试，掌握从设计到实物原型的完整流程"
head:
  - - meta
    - name: keywords
      content: fab academy, 电子产品制作, PCB铣削, SMT焊接, 电路板, CNC, 元件焊接, 测试调试
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第7周：个人项目：显示器支架设计与切割'
  link: '/zh/assignments/week07/week07_individual_assignment_monitor_stand_design_cutting_cn'
next:
  text: '第8周：小组作业：PCB设计规则特性化与外发制作'
  link: '/zh/assignments/week08/week08_group_assignment_pcb_rules_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第 8 周：电子产品制作
> 本文档内容由我向 Claude 3.7 Sonnet 提供[课程大纲](http://academy.cba.mit.edu/classes/electronics_production/index.html)内容，以及本课程视频会议课程的字幕后，由 AI 提炼生成。
>

## 课程概要
本周课程是关于电子产品制作的实践。在之前的课程中，我们已经学习了编程和模拟，并且已经设计了电子产品。现在，我们将进一步学习如何实际制作电路板，这是进入下一阶段学习（传感器和输出设备等）的基础。本课程将介绍不同的电路板制作方法，重点是数控铣削（CNC milling）技术，并会涵盖元件焊接、调试和测试等关键技能。

## 详细课程内容
### 1. 电路板制作方法简介
#### 1.1 非推荐方法：Dead Bug 电路
"Dead Bug"是一种简单但不稳定的电路制作方法，即直接在元件之间连接导线。这种方法虽然作为临时解决方案偶尔会用到，但由于结构不稳定且不可靠，因此不推荐在专业环境中使用。

#### 1.2 蚀刻法（Etching）
蚀刻是一种使用化学品腐蚀铜层以创建电路的方法：

+ **优点**：可批量处理，可实现非常精细的分辨率
+ **缺点**： 
    - 需要制作遮罩（可通过光刻、墨粉转移或印刷实现）
    - 使用危险化学品（如氯化铁、氯化铜、过硫酸铵/钠等）
    - 产生大量化学废物，对环境不友好
    - 耗水量大（1平方米的PCB可能消耗1立方米的水）

由于这些环境问题，Fab Lab通常不推荐蚀刻工艺，除非需要极高的精度或大批量生产。

#### 1.3 数控铣削法（CNC Milling）- 推荐方法
数控铣削是Fab Academy推荐的主要制作方法：

+ **优点**：
    - 无需设置复杂工艺，设计可直接进入机器
    - 几乎没有废物（只有少量可轻松处理的粉尘）
    - 设计完成后10-15分钟内即可完成铣削
    - 结果高度一致可预测
+ **工具**：
    - 使用小型高精度铣床
    - 常用刀具： 
        * 0.010英寸（0.25毫米）铣刀：用于极小特征，但非常脆弱
        * 1/64英寸（约0.4毫米）铣刀：常用于切割走线
        * 1/32英寸（约0.8毫米）铣刀：用于切割板子和钻孔
        * V型刀头：成本较低，适合走线隔离，但不适合大面积铜层移除
        * 锥形刀头：结合了V型刀头和常规铣刀的优点
+ **固定与支撑**：
    - 板材必须平整固定，任何弯曲都可能导致刀具断裂
    - 使用胶带或专用夹具固定
    - 需要底层材料（underlay）作为支撑，随着使用会逐渐磨损，需定期更换
+ **零点设置（Zeroing）**：
    - 关键步骤，刀具必须精确定位在铜层表面
    - 常用方法： 
        * 手动降低并固定（注意拧紧时可能导致工具轻微上移）
        * 纸张法：放置纸张并逐步降低刀具，当纸张不能移动时，再降低刀具纸张厚度的距离
        * 探针法：使用电导来自动测量零点位置
+ **刀具使用寿命**：
    - 新刀具非常锋利，边缘特征精细
    - 略微钝化后实际效果更好
    - 随着使用，刀具会逐渐变钝，需要及时更换
+ **后处理**：
    - 去毛刺处理：使用砂纸、锉刀或直边工具去除细小毛刺
    - 清洁：加工后应清洁电路板，避免指纹油污导致铜层氧化

#### 1.4 其他制作方法
##### 1.4.1 乙烯基切割法（Vinyl Cutter）
+ 比铣削更快，因为只需移动刀具轨迹而非整个区域
+ 需要"剥离"（weeding）过程，这需要一定技巧
+ 走线可以放置在任何表面，灵活性高
+ 步骤： 
    - 使用底层支撑
    - 将铜转移到环氧胶片或聚碳酸酯膜上
    - 切割（确保深度适中）
    - 剥离不需要的部分
    - 建议最后进行封装处理以增强附着力，特别是连接器部分

##### 1.4.2 激光切割法（Laser Cutting）
+ 普通CO2激光切割机无法用于PCB制作
+ 光纤激光（约1微米波长）可以有效去除铜层
+ 新型设备如XTool F1 Ultra（约4000美元）结合了二极管激光和光纤激光
+ 特点： 
    - 不受工具直径限制，仅受光束直径限制
    - 对激光焦点非常敏感
    - 去除铜层可能需要多次通过（约10次）
    - 可以处理非常细小的特征
    - 可同时处理大面积和细小特征

##### 1.4.3 其他方法
+ 激光诱导石墨烯（LIG）：使用激光在Kapton上直接写入石墨烯导电材料
+ 导电墨水印刷：通过印刷导电墨水创建电路
+ 电镀技术：用于商业低成本RFID标签等
+ 导电线缝纫：使用导电线在织物上创建低分辨率电路

### 2. PCB材料
#### 2.1 刚性电路板材料
+ **FR4**（环氧玻璃纤维）：
    - 商业上最常用
    - 不推荐在Fab Lab中铣削，因为： 
        * 玻璃纤维会磨损工具
        * 玻璃微粒是危险废物，不宜吸入
+ **FR1**（酚醛纸基板）：
    - Fab Academy推荐使用
    - 易于铣削，无有害粉尘
    - 耐温性略低于FR4，但足够手工焊接
+ **Garolite**：类似于FR1但没有铜层

#### 2.2 柔性电路板材料
+ **Kapton/Pyralux**：商业柔性电路常用材料
+ **环氧胶片和铜带**：可用于DIY柔性电路

#### 2.3 铜层厚度
+ 0.5盎司：17.5微米
+ 1.0盎司：35微米（标准）
+ 2.0盎司：70微米

### 3. 商业PCB制造
#### 3.1 常用PCB制造商
+ JLCPCB、PCBWay、OSH Park、Aisler等
+ 特点：成本低廉（数美元即可获得多块板）
+ 运费通常是主要成本

#### 3.2 设计规则
+ 走线宽度/间距：商业通常为5密耳（0.127毫米）
+ 自制PCB通常需要更宽的走线和间距（约15密耳/0.38毫米）

#### 3.3 层数
+ 单面板：所有走线在一侧
+ 1.5面板：使用零欧姆电阻器作为交叉连接
+ 双面板：两面都有走线，需要翻转对准
+ 多层板：商业上常见4层（顶层、底层、电源层、地层）

#### 3.4 过孔（Vias）
+ 通孔连接的方法： 
    - 导线：适合少量连接
    - 铆钉：手工制作多层板的好选择
    - 电镀：商业PCB标准方法
+ 特殊过孔类型（仅商业生产）： 
    - 盲孔：从表面连接到内部层
    - 埋孔：仅在内部层之间连接

### 4. 电子元件
#### 4.1 元件类型
+ 通孔元件（Through-hole）：少用，主要用于需要加固的特殊部件
+ 表面贴装元件（Surface-mount）：推荐使用
+ 芯片级封装（Chip-scale）：极小元件，需要特殊焊接工艺

#### 4.2 为什么不使用面包板（Breadboard）
+ 问题： 
    - 需要带引脚的元件（与PCB设计不符）
    - 无文档记录，难以转移到PCB设计
    - 电气性能差（噪声、频率特性）
    - 机械性能差（走动时连接可能松动）
+ 推荐：直接学习制作电路板，这样更可靠、有文档记录，且可从原型直接过渡到生产

### 5. 焊接技术
#### 5.1 焊接设备
+ 烙铁/焊台：控制温度
+ 烟雾排除器：提供良好通风
+ 安全注意事项：避免烫伤

#### 5.2 焊料类型
+ 无铅焊料（推荐）：环保，符合ROHS标准，需稍高温度
+ 含铅焊料：焊接温度较低，但不环保
+ 低温焊料：特殊应用

#### 5.3 形式：
+ 焊丝：手工焊接常用
+ 焊膏：回流焊接用

#### 5.4 手工焊接技巧
1. 保持烙铁尖端干净、有光泽
2. 在烙铁尖涂一层薄焊料以辅助导热
3. 同时加热元件引脚和PCB焊盘（不只是一处）
4. 先加入少量焊料帮助传热
5. 耐心等待焊料自然流动（约10-15秒）
6. 焊料流动后继续保持加热一段时间
7. 移开烙铁

良好的焊点：光滑有光泽；不良焊点：呈颗粒状、暗淡

#### 5.5 元件固定技巧
1. 先在一个焊盘上加少量焊料
2. 放置元件，用该焊盘临时固定
3. 焊接其他引脚
4. 最后回到第一个焊点，加入适量焊料完成良好焊接

#### 5.6 回流焊接
+ 适用于极小元件或批量生产
+ 步骤： 
    1. 制作模板（可用聚碳酸酯材料铣削）
    2. 通过模板涂抹焊膏
    3. 放置元件
    4. 加热使焊膏熔化（可用热风枪、热板或回流炉）
+ 技巧：热风枪不要太快接近，以免吹走元件

#### 5.7 拆焊技术
+ 焊芯（Solder Braid）：先加一小球焊料"引启"，然后焊芯会吸走焊料
+ 热风重力法：用热风枪加热所有引脚，利用重力使元件脱离

#### 5.8 修复技巧
+ 走线切断与跳线：当设计有错误时，可以切断走线并添加跳线
+ 元件重新放置：使用拆焊技术移除错误放置的元件

### 6. CAM与文件格式
#### 6.1 常用格式
+ Gerber/RS-274X：行业标准，用于商业生产
+ PNG：易于查看和编辑，适合自制PCB（无压缩伪影）

#### 6.2 CAM软件
+ FlatCAM、pcb2gcode：将PCB文件转换为铣床G代码
+ gerber2img、gerber2png：将Gerber文件转换为图像
+ MODS：工作流程自动化工具

#### 6.3 设计考虑
+ 走线宽度根据制作工具选择
+ 焊盘设计注意事项：可能需要调整标准焊盘以适应铣削工具
+ 标准焊盘间距： 
    - 50密耳：容易实现
    - 0.65毫米：相对容易
    - 0.5毫米或更小：自制PCB挑战很大

### 7. 调试技巧
当电路板不工作时，按照以下步骤系统调试：

1. 检查焊点：确保光滑有光泽，无焊接桥接
2. 检查元件方向与值：确认元件方向正确，使用了正确的元件值
3. 查看数据表：确认引脚定义正确
4. 确认连接器方向
5. 测量供电电压：确保各处电压正确
6. 探测I/O信号：使用示波器等工具

## 作业要求
### 小组作业：
1. 表征您实验室内部PCB生产过程的设计规则 
    - 运行测试图案，确定可实现的最小走线宽度和间距
    - 确保工作流程运行正常，结果应当高度一致
2. 向PCB制造商提交PCB设计 
    - 完成设计上传和价格估算流程
    - 实际订购可选

### 个人作业：
1. 制作并测试您设计的微控制器开发板 
    - 铣削电路板
    - 焊接元件
    - 运行您编写的程序
    - 验证功能正常
2. 额外加分：使用另一种制作工艺 
    - 从铣削开始（最简单可预测）
    - 尝试乙烯基切割或激光铣削等其他方法

## 时间管理建议
+ 更新设计以符合设计规则：1天
+ 学习制作电路板：1天
+ 制作电路板：1天
+ 焊接：1天
+ 加载代码：1天
+ 留出充足时间进行调试（至少2天）

## 学习资源
### 电路板制作方法
+ [Dead Bug电路示例](https://spectrum.ieee.org/with-the-dead-bug-method-hobbyists-can-break-through-the-highfrequency-barrier)
+ [PCB蚀刻指南](http://pub.fabcloud.io/helloworld/uncharted/acid_etch.html)
+ [PCB铣削视频教程](http://www.youtube.com/watch?v=XdamEhs2RIk&list=PL-xEsC0ZUCUM42QNHaOOdoOwYg0j251dU&index=1)

### 焊接资源
+ [ROHS标准信息](http://ec.europa.eu/environment/waste/rohs_eee/index_en.htm)
+ [冷焊点识别指南](https://www.google.com/search?q=cold+solder+joint&source=lnms&tbm=isch)

### CAM工具
+ [FlatCAM](http://flatcam.org/)
+ [pcb2gcode](https://github.com/pcb2gcode/pcb2gcode)
+ [MODS](https://modsproject.org/?program=programs/machines/G-code/mill%202D%20PCB)

### PCB制造商
+ [JLCPCB](https://jlcpcb.com/)
+ [PCBWay](https://www.pcbway.com/)
+ [OSH Park](https://oshpark.com/)
+ [Aisler](https://aisler.net/)

### 调试指南
+ [PCB调试视频](https://vimeo.com/518231668)

### 优秀学生作业示例
+ [Quentin的PCB制作示例](https://fabacademy.org/2020/labs/ulb/students/quentin-bolsee/assignments/week05/)
+ [Isaak Freeman的第6周作业](https://fab.cba.mit.edu/classes/863.24/people/IsaakFreeman/week6/week6.html)

## 任务计划
- [ ]  3 月 14 日周五：确定作业电路板并准备元件（可能需要根据找到的元件修改电路板）。
- [ ]  3 月 15 日周六：学习制作电路板。
- [ ]  3 月 16 日周日：切割电路板及焊接测试。
- [ ]  3 月 17 日周一：代码及文档。
- [ ]  3 月 18 日周二：网站上传及补作业。

