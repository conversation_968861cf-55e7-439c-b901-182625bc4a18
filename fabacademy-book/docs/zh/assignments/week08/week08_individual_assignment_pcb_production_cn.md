---
layout: doc
title: "第8周：个人作业：电子电路板生产 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第八周个人作业：使用CNC铣床制作XIAO ESP32C3扩展板，完成元件焊接与测试，实践电子产品制造全流程"
head:
  - - meta
    - name: keywords
      content: fab academy, 电子电路板, CNC铣床, XIAO ESP32C3, 焊接技术, 元件选择, PCB制造, Arduino编程
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第8周：小组作业：PCB设计规则特性化与外发制作'
  link: '/zh/assignments/week08/week08_group_assignment_pcb_rules_cn'
next:
  text: '第9周：输入设备'
  link: '/zh/assignments/week09/week09_input_devices_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 第八周个人作业：电子电路板生产

本次个人作业的目标是使用 CNC 机器生产一块基于XIAO ESP32C3的扩展板，并完成元件焊接和测试。具体任务包括：

1. 使用CNC切割机生产PCB板
2. 焊接所需电子元件
3. 编程测试电路板功能

## PCB 制造

在第6周的个人作业部分，我为这周的作业设计好了准备要切割的 PCB。

![](/images/week08/w08-g2-1.png)

> 第 6 周个人作业准备好的 PCB 设计文件

### 材料与工具准备

#### 电路板材料

+ 单面覆铜板（FR-4，1.6mm厚）
+ 尺寸：约 55mm x 36mm

![](/images/week08/w08-p-1.jpg)

> 柴火创客空间为我们此次课程准备的单面覆铜板

#### 电子元件清单

作为 Seeed 的员工，我开始以为为自己找合适的元件是很容易的事情，作为一个电子产品设计和制造企业，觉得自己所需应该很容易满足。根据XIAO ESP32C3扩展板的BOM表，需要以下元件：

| 编号 | 位号              | 封装                                   | 数量 | 名称                                   | 物料准备情况 |
| ---- | ----------------- | -------------------------------------- | ---- | -------------------------------------- | ------------ |
| 1    | R1,R6,R3,R4,R5,R2 | R_1206                                 | 6    | R_1206 220Ω                           | 无           |
| 2    | R7,R9,R8          | R_1206                                 | 3    | R_1206 10KΩ                           | 无           |
| 3    | D5,D2,D1,D3,D4,D6 | LED_1206                               | 6    | LED_1206                               | 有           |
| 4    | J1                | PinHeader_01x08_P2.54mm_Horizontal_SMD | 1    | PinHeader_01x08_P2.54mm_Horizontal_SMD | 无           |
| 5    | M1                | SeeedStudio_XIAO_ESP32C3               | 1    | Module_XIAO-ESP32C3                    | 有           |
| 6    | SW1               | Button_Omron_B3SN_6.0x6.0mm            | 1    | Switch_Tactile_Omron                   | 有           |

真正找开始找这些细小的元件，发现并不是一个容易的过程。因为我是自己设计的电路板，所以使用了一些和 fab xiao 不同的的元件。

最容易找到的是 [Seeed Studio XIAO ESP32C3](https://www.seeedstudio.com/Seeed-XIAO-ESP32C3-p-5431.html)，这种糖果包装的 MCU，在 Seeed 到处都是。

![](/images/week08/w08-p-2.jpg)

> 糖果包装的  [Seeed Studio XIAO ESP32C3](https://www.seeedstudio.com/Seeed-XIAO-ESP32C3-p-5431.html)

然后就是是 1206 规格的 LED，成卷几乎到处都是，我也是毫不费力的找到一条，数量对我来说也足够了。
![](/images/week08/w08-p-3.jpg)

> 适合贴片包装的 1206 规格的 LED

![](/images/week08/w08-p-4.jpg)

> 1206 规格的 LED 背面，有方向指示箭头阴极和阳极，焊接安装的时候要注意，别贴反了

![](/images/week08/w08-p-5.jpg)

> 1206 规格的 LED 正面也有一个极小的绿箭头再次指示方向

按钮很快我也找到了一盒，我想要的也在里面（右上角那种）
![](/images/week08/w08-p-6.jpg)

> 发现有一盒各种各样的按钮

但 PinHeader_01x08_P2.54mm_Horizontal 怎么都找不到合适的，作业时间有限，只能找到接近的替代品，不过我还是准备采买一些符合要求的。

![](/images/week08/w08-p-7.jpg)

> KiCAD 里 3D 视图显示出的 8 针引脚的样子

![](/images/week08/w08-p-8.jpg)

> 这是找到的最接近的引脚排针，看看能否替代使用

在找 R_1206 220Ω，R_1206 10KΩ 的贴片电阻遇到困难，Seeed 的工程师说 1206 规格的电阻对于目前大多项目来说都太大，已经很少采买使用了，他们更多的是规格尺寸更小的电阻，并给我了一个“祖传”元件样品簿，上面是更小的 0805 规格的电阻，Seeed 的工程师说可以用这个替代，只是个头小点而已。这个元件样品簿从 0Ω 起步，按电阻阻值大小逐步递增，我很快就找到了自己需要的 220Ω 和 10K 欧姆的。

![](/images/week08/w08-p-9.jpg)

只是它们的个头，真的小的超乎想象，下图是我从元件样本册中取出的几个 10KΩ 的电阻，小的难以置信。

![](/images/week08/w08-p-10.jpg)

> 从样本册中取出的 0805 规格的电阻

元件基本凑齐了，可以开始制作了。

### 工具

+ CNC切割机，配套使用 Mash3 的控制电脑
+ 烙铁与焊接工具套装
+ 万用表
+ 放大镜（辅助小元件焊接）
+ Arduino IDE（编程测试）

## 详细工作流程

### 1. 制作电路板

制造电路板的详细过程，可以参看本周的小组作业。

#### 1.1 准备G代码文件

我在第6周的电路设计作业中已经完成了PCB设计，并导出了两个PNG文件：

+ `traces_top_layer_0.png`：铜层电路（已完成覆铜操作）
+ `outline_top_layer_1.png`：电路板外框切割线

![](/images/week08/w08-p-11.jpg)

> 第 6 周个人作业输出的 PCB 设计的 PNG 文件

使用[Mods项目网站](https://modsproject.org/?program=programs/machines/G-code/mill%202D%20PCB)将这些PNG图像转换为G代码：

1. 导入 `traces_top_layer_0.png`，使用"isolate traces (1/64)"设置
2. 生成 `traces_top_layer_0.png.nc`文件

![](/images/week08/w08-p-12.png)

> 导入 `traces_top_layer_0.png`，使用"isolate traces (1/64)"设置，并生成.nc 文件的位置说明

![](/images/week08/w08-p-13.png)

> 生成的电路部分的切割路径

3. 导入 `outline_top_layer_1.png`，使用"mill outline (1/32)"设置
4. 生成 `outline_top_layer_1.png.nc`文件

![](/images/week08/w08-p-14.png)

> 导入 `outline_top_layer_1.png`，使用"mill outline (1/32)"设置，生成.nc 文件

![](/images/week08/w08-p-15.png)

> 生成的外框部分切割路径

#### 1.2 CNC切割过程

在本周的小组作业中解决了CNC机器的连接问题后，我进行了以下步骤：

1. 固定PCB板：使用双面胶和固定架牢固地将铜板固定在CNC工作台上。

![](/images/week08/w08-g-8.jpg)

> 覆铜板在切割前，需要再背面贴上双面胶，以便能更稳固的固定再作为垫板的木版上。然后还需要将黏在木版上的覆铜板用夹具固定

将铜板固定在CNC工作台上。

1. 安装切削工具：
   - 首先安装1/64英寸 V 型刀头用于切割电路走线
   - 设置起点位置并校准 Z 轴高度

![](/images/week08/w08-g-9.jpg)

> Matthew 指导我们如何对设备归零和校准 Z 轴高度

2. 执行走线切割：
   - 加载 `traces_top_layer_0.png.nc`文件
   - 启动切割过程并监控
   - 切割过程非常顺利，没有出现断刀或过切情况

![](/images/week08/w08-g-15.jpg)

> 完成走线的切削，这一步完成后，切记不要动板子

3. 更换工具并切割外形：
   - 安装1/32英寸直槽铣刀
   - 重新校准Z轴高度
   - 加载 `outline_top_layer_1.png.nc`文件
   - 执行外形切割

![](/images/week08/w08-g-16.jpg)

> 更换  1/32英寸（0.8mm）直槽铣刀刀头

使用直槽铣刀切割电路板外形。

![](/images/week08/w08-g-18.jpg)

> 切割边框会比较深，会出很多碎屑

清理与检查：

    - 使用砂纸轻轻打磨电路板边缘，去除毛刺
    - 用放大镜检查走线质量，确保没有短路或断路

![](/images/week08/w08-p-16.jpg)

> 加工完成的XIAO ESP32C3扩展板

### 嘉立创生产 pcb

本周的小组作业我还体验了在线下单生产 PCB 的过程，有兴趣的可以阅读那部分的内容。

所以这周我拿到了 2 个版本的成果。

![](/images/week08/w08-p-17.jpg)

> 使用铣削工艺生产的 PCB（中）以及使用蚀刻工艺（量产模式）生产的 PCB

### 2. 元件焊接

由于前面铣削 PCB 耽误了很多时间，我只能趁着周二晚上在 Seeed 的硬件工程师下班后，在他们的焊台上焊接，好处是这里装备齐全。

#### 2.1 准备工作

1. 整理所有元件并确认与BOM表一致
2. 预热烙铁至约320℃（适合锡膏熔点）
3. 准备助焊剂和焊锡丝

![](/images/week08/w08-p-18.jpg)

> Seeed 的硬件工程师使用的焊台，焊接前的准备好元件、烙铁和放大镜（还有万用表没有拍进来），后面在焊接过程中也是需要频繁使用的

#### 2.2 焊接顺序

我按照从小到大、从低到高的原则进行焊接：

1. 首先焊接电阻：
   - 220Ω电阻（R1,R2,R3,R4,R5,R6）用于LED的限流
   - 10KΩ电阻（R7,R8,R9）用于按钮的上拉

![](/images/week08/w08-p-19.jpg)

2. **焊接XIAO ESP32C3模块(M1)**：
   - 采用先焊接一侧引脚，确认位置正确后再焊接另一侧的方法
   - 在这个过程中，Seeed 的硬件工程师瞿翔楠看到我焊接技术不专业，提供了宝贵指导
   - 他教授了预先加热元件、使用焊丝快速点击连接等专业技巧，极大提高了后续焊接效率

![](/images/week08/w08-p-20.jpg)

> Seeed 的硬件工程师瞿翔楠看到我焊接技术不专业，教授了预先加热元件、使用焊丝快速点击连接的焊接技巧

![](/images/week08/w08-p-21.jpg)

> 终于把 XIAO 焊上去了

3. **焊接轻触开关(SW1)**：
   - 按照瞿工程师的建议，先确保开关平整放置
   - 焊接时注意力度，避免损坏开关结构
   - 这个过程有点痛苦，没有把握好焊锡的量，焊锡糊成了一片……

![](/images/week08/w08-p-22.jpg)

> 焊按钮的时候没把握好，焊锡量过大

![](/images/week08/w08-p-23.jpg)

> 最后勉强给焊上了，并补全了 3 颗 10KΩ 的电阻

### 3. 编程与测试

#### 编程环境设置

1. 安装 Arduino IDE 及 XIAO ESP32C3 支持包，可以参考[官方 Wiki 文档](https://wiki.seeedstudio.com/XIAO_ESP32C3_Getting_Started/)。
2. 配置开发板选项：选择"XIAO ESP32C3"。
3. 安装必要的库文件。
4. **初步功能测试**：
   - 对已焊接的 `LED(D0)`编写了简单的 Blink 程序进行测试。

```cpp
// define led according to pin diagram in article
const int led = D0; // there is no LED_BUILTIN available for the XIAO ESP32C3.

void setup() {
    // initialize digital pin led as an output
    pinMode(led, OUTPUT);
}

void loop() {
    digitalWrite(led, HIGH);   // turn the LED on 
    delay(1000);               // wait for a second
    digitalWrite(led, LOW);    // turn the LED off
    delay(1000);               // wait for a second
}
```

4. 菩萨保佑啊，成功点亮LED，证明基本电路和焊点连接正常。

![](/images/week08/w08-p-24.jpg)

> 单颗 LED 成功点亮

### 4. 继续扩展焊接：
- 深夜继续焊接工作，尝试焊接了另外几个LED和对应电阻（最终焊了 5 组）
- 遇到挑战：0805 规格的电阻过小，操作困难
- 焊接完成后发现部分电阻可能短路(用万用表测阻值为0)

![](/images/week08/w08-p-25.jpg)

> 周二深夜最终的工程进度，焊接了 5 组 LED

**焊接体会与困难**：

+ 0805 封装的SMD元件对于初学者来说确实很小，需要高度集中注意力
+ 电阻极易短路，需要多次拆卸重焊
+ 最终完成板子虽然外观不够美观，但基本功能可用
+ 五个LED中有四个可以正常工作(D0、D1、D3、D4)，一个(D2)未能点亮

#### 继续测试程序

 进一步改写了一个简单的 6 个 LED 闪烁和按钮检测程序：

```cpp
// Define LED pins D0 through D5 on XIAO ESP32C3
const int ledPins[] = {D0, D1, D2, D3, D4, D5};
const int numPins = 6; // Total number of LED pins

void setup() {
    // Initialize all LED pins as outputs
    for (int i = 0; i < numPins; i++) {
        pinMode(ledPins[i], OUTPUT);
    }
}

void loop() {
    // Turn all LEDs on
    for (int i = 0; i < numPins; i++) {
        digitalWrite(ledPins[i], HIGH);
    }
    delay(1000); // Wait for a second

    // Turn all LEDs off
    for (int i = 0; i < numPins; i++) {
        digitalWrite(ledPins[i], LOW);
    }
    delay(1000); // Wait for a second
}
```

![](/images/week08/w08-p-26.jpg)

> 回到家用改写的 Blink 程序测试 5 只安装的 LED，发现混了一只红色的，还有一只不亮

### 5. 包括按钮的测试程序

继续写了一个包括按钮的测试程序：

```cpp
// XIAO ESP32C3扩展板测试程序
// 使LED指示灯闪烁并检测按钮状态

// 定义LED引脚
const int LED_PINS[] = {D0, D1, D2, D3, D4, D5};
const int LED_COUNT = 6;
const int BUTTON_PIN = D6; // 连接到轻触开关的引脚

int buttonState = 0;

void setup() {
  // 初始化LED引脚为输出
  for (int i = 0; i < LED_COUNT; i++) {
    pinMode(LED_PINS[i], OUTPUT);
  }
  
  // 初始化按钮引脚，启用内部上拉电阻
  pinMode(BUTTON_PIN, INPUT_PULLUP);
  
  Serial.begin(115200);
  Serial.println("XIAO ESP32C3扩展板测试程序启动");
}

void loop() {
  // 读取按钮状态
  buttonState = digitalRead(BUTTON_PIN);
  
  if (buttonState == LOW) {
    // 按钮按下，所有LED同时点亮
    for (int i = 0; i < LED_COUNT; i++) {
      digitalWrite(LED_PINS[i], HIGH);
    }
    Serial.println("按钮按下 - 所有LED点亮");
  } else {
    // 按钮未按下，LED逐个循环点亮
    for (int i = 0; i < LED_COUNT; i++) {
      // 清除所有LED状态
      for (int j = 0; j < LED_COUNT; j++) {
        digitalWrite(LED_PINS[j], LOW);
      }
      // 点亮当前LED
      digitalWrite(LED_PINS[i], HIGH);
      delay(200);
    }
  }
}
```

## 问题与解决方案

上传后，测试结果如下：

+ 5 个 LED 还是只有 4 个能够按照程序指令正常工作
+ 发现开关始终保持被按下的状态，所有LED 常亮，通过串口监视器的输出也验证了这一点，怀疑按钮没有焊接好，出现短路情况。

![](/images/week08/w08-p-27.png)

> 串口监视器显示按钮始终是按下状态

细小元件的焊接挑战巨大，需要进一步练习。

## 成果与反思

### 成功之处

+ 成功使用 CNC 机器切割出高质量的电路板，走线清晰，无明显缺陷
+ 在硬件专家的指导下，学习并初步掌握了SMD元件焊接技巧
+ 成功完成部分元件（4个LED、部分电阻和XIAO模块）的焊接
+ 通过Arduino编程验证了基本功能，实现了LED控制

### 挑战与不足

+ 0805 封装的 SMD 元件对初学者来说仍然很小，导致焊接质量不稳定
+ 电阻焊接部分出现短路问题，需要进一步提高焊接技术
+ 未能完成全部元件的焊接，特别是8针卧式排针
+ D2 LED 未能正常工作，需要检修

### 后续计划

+ 购买另一批 1206 规格的电子元件，重新进行焊接尝试
+ 使用专业焊台和助焊工具提高焊接精度
+ 完成全部元件焊接并进行全面功能测试
+ 记录更详细的故障排除过程和经验

### 学习心得

这次 PCB 制作经历让我深刻体会到电子制造的挑战和乐趣。CNC 切割 PCB 相对简单，但元件焊接对初学者来说是一个显著的技术门槛。很感谢 Seeed 的硬件工程师瞿翔楠提供的专业指导，这极大地帮助我理解了 SMD 焊接的要点。

我认识到电子制造需要耐心、细致和持续的实践。虽然这次没有完成所有元件的焊接，但获得的经验和技巧对未来项目至关重要。

## 参考资源

+ [XIAO ESP32C3官方文档](https://wiki.seeedstudio.com/XIAO_ESP32C3_Getting_Started/)
+ [Arduino ESP32编程指南](https://docs.espressif.com/projects/arduino-esp32/)
+ [SMD焊接技巧指南](https://www.sparkfun.com/tutorials/36)
+ [CNC PCB制作最佳实践](http://academy.cba.mit.edu/classes/electronics_production/index.html)

再次感谢 Seeed Studio 硬件工程师瞿翔楠提供的现场指导。

## 使用嘉立创制作的 PCB 再次焊接

不甘心覆铜板 PCB 失败的焊接经历，我又采购了适合的 1206 的电阻（10K Ω 和 220 Ω），以及 8 针的卧贴排针。

![](/images/week08/w8-p2-1.jpg)

> 白色标签的是重新采购的元件，准备在嘉立创生产的 PCB 上再次尝试焊接

首先尝试焊了一个 1206 的 LED，有了第一个 PCB 的经验，这次感觉容易多了，如下图所示。

![](/images/week08/w8-p2-2.jpg)

> 先尝试焊接了一个 1206 的 LED

因为有电路保护层，所以这次焊接非常顺利，我几乎没花多长时间，就把 LED，电阻，按钮和卧贴排针都焊上去了。焊接过程中，还需要不断测试，焊点之间是否为通断或成功连接，下图是用万用表测试按钮，按下时 2 个焊点的阻值趋向为 0 表示按钮正常工作。

![](/images/week08/w8-p2-3.jpg)

> 测试按钮按下时电路是否被联通

元件被焊好的样子，如下图所示，准备焊 XIAO ESP32C3。

![](/images/week08/w8-p2-4.jpg)

> 元件部分都焊好的样子，1206 的电阻比上个板子用的 0805 的好焊很多

上一个 PCB 的 XIAO ESP32C3 被焊死在板子上，不便拆卸测试，所以我考虑这次使用焊接了引脚的 XIAO，并找到了一些 8 孔的排母，如下图所示。
![](/images/week08/w8-p2-5.jpg)

> 焊接了引脚的 XIAO ESP32C3 和 8 针排母

使用钳子剪掉 1 个针座，如下图所示。

![](/images/week08/w8-p2-6.jpg)

> 将 8 孔排母剪去 1 孔，让其变成 7 孔排母

再将 7 孔排母的引脚折叠 90 度，让其能坐在 PCB 上（因为这批生产的 PCB 没有打孔），然后把带引脚的 XIAO 套上排母放在 PCB 上焊接，如下图所示。

![](/images/week08/w8-p2-7.jpg)

> 把 XIAO 套上排母摆放在 PCB 上开始焊接

焊接完成的板子如下图所示，多了一个 XIAO 的插座。

![](/images/week08/w8-p2-8.jpg)

> 现在多了一个 XIAO 的插座

直接使用前面包括按钮的测试程序：

```cpp
// XIAO ESP32C3扩展板测试程序
// 使LED指示灯闪烁并检测按钮状态

// 定义LED引脚
const int LED_PINS[] = {D0, D1, D2, D3, D4, D5};
const int LED_COUNT = 6;
const int BUTTON_PIN = D6; // 连接到轻触开关的引脚

int buttonState = 0;

void setup() {
  // 初始化LED引脚为输出
  for (int i = 0; i < LED_COUNT; i++) {
    pinMode(LED_PINS[i], OUTPUT);
  }
  
  // 初始化按钮引脚，启用内部上拉电阻
  pinMode(BUTTON_PIN, INPUT_PULLUP);
  
  Serial.begin(115200);
  Serial.println("XIAO ESP32C3扩展板测试程序启动");
}

void loop() {
  // 读取按钮状态
  buttonState = digitalRead(BUTTON_PIN);
  
  if (buttonState == LOW) {
    // 按钮按下，所有LED同时点亮
    for (int i = 0; i < LED_COUNT; i++) {
      digitalWrite(LED_PINS[i], HIGH);
    }
    Serial.println("按钮按下 - 所有LED点亮");
  } else {
    // 按钮未按下，LED逐个循环点亮
    for (int i = 0; i < LED_COUNT; i++) {
      // 清除所有LED状态
      for (int j = 0; j < LED_COUNT; j++) {
        digitalWrite(LED_PINS[j], LOW);
      }
      // 点亮当前LED
      digitalWrite(LED_PINS[i], HIGH);
      delay(200);
    }
  }
}
```

插上 XIAO ESP32C3，如下图所示，现在可以开始准备测试。

![](/images/week08/w8-p2-9.jpg)

> 把 XIAO ESP32C3 插在排母插座上

这次非常顺利，一次通过，当不按按钮的时候，6 个 LED 依次点亮，如下图所示。

<video controls width="100%">
  <source src="/images/week08/pcb-run-ok.mp4" type="video/mp4">
  你的浏览器不支持视频标签
</video>

> 当不按按钮的时候，6 个 LED 依次点亮

当持续按下按钮时，所有 LED 都同时点亮，如下图所示。

![](/images/week08/w8-p2-10.jpg)

> 持续按下按钮，所有 LED 同时点亮

成功！
