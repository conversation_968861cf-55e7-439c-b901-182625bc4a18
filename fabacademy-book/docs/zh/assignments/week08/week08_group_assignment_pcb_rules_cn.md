---
layout: doc
title: "第8周：小组作业：PCB设计规则特性化与外发制作 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第八周小组作业：测试实验室CNC铣床的PCB制作能力，记录设计规则参数，并体验商业PCB制造流程"
head:
  - - meta
    - name: keywords
      content: fab academy, PCB设计规则, 小组作业, CNC铣床, 嘉立创, 电路板制造, 参数设置, 工艺流程
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第8周：电子产品制作'
  link: '/zh/assignments/week08/week08_electronics_production_cn'
next:
  text: '第8周：个人作业：电子电路板生产'
  link: '/zh/assignments/week08/week08_individual_assignment_pcb_production_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 第 8 周小组作业：电子生产设计规则特性化与PCB外发制作

[[查看小组作业完整内容]](https://fabacademy.org/2025/labs/chaihuo/docs/week8/chaihuo/week8_group_assignment)

文档撰写：冯磊

## 作业目标

本次小组作业的主要目标是：

1. 针对实验室内部PCB生产过程，特性化设计规则，记录进给速度、下刀速率、切割深度（走线和外框）以及所使用的工具
2. 将设计好的PCB通过网络方式发送到电路板厂家制作。

更多小组作业内容请看：[https://fabacademy.org/2025/labs/chaihuo/docs/week8/week08_group_assignment_pcb_rules/](https://fabacademy.org/2025/labs/chaihuo/docs/week8/week08_group_assignment_pcb_rules/)

## 实验室小型 CNC 机器介绍

在柴火创客空间，我们使用的是一台晶研仪器生产的小型 CNC 雕刻机 JY5300-2，`<font style="color:rgb(28, 30, 33);">`它可以通过电脑和有线遥控器进行控制`</font>`。Matthew 对我们进行了设备使用的培训和指导。

![](/images/week08/w08-g-1.jpg)

> 柴火创客空间的小型 CNC雕刻机，Matthew 在向我们培训如何使用设备

![](/images/week08/w08-g-2.jpg)

> Matthew 所指的按钮，是控制箱上断开/连接 PC 的按钮

该机器的主要技术规格如下：

+ 工作台面积：约40cm x 30cm
+ 最大雕刻深度：约5mm
+ 定位精度：0.01mm
+ 重复精度：0.05mm
+ 控制方式：并口连接计算机
+ 控制 CNC 的软件：Mach 3

![](/images/week08/w08-g-3.jpg)

> 晶研仪器生产的小型 CNC 雕刻机

![](/images/week08/w08-g-4.jpg)

> 设备手册上的参数表

![](/images/week08/w08-g-5.jpg)

> 设备手册上的功能按键说明

## 工具与材料

### 切削工具

实验室配备了多种不同规格的铣刀，以满足不同的切割需求：

![](/images/week08/w08-g-6.jpg)

> 各种不同类型的切削工具：1/64英寸铣刀、1/32英寸铣刀和V型刀头

我们在本次作业中主要使用了以下工具：

1. **1/64英寸（0.4mm）V型刀头（下图左）**：用于精细切割电路走线
2. **1/32英寸（0.8mm）直槽铣刀（下图右）**：用于钻孔和切割电路板外形轮廓

经过比较测试，V型刀头在切割电路走线时效果更好，表面更加平整光滑。

![](/images/week08/w08-g-7.jpg)

> **我们准备用的 1/64英寸（0.4mm）V型刀头（左）和1/32英寸（0.8mm）直槽铣刀（右）**

### PCB材料

我们使用的是标准的单面覆铜板：

+ 材料：FR-1/FR-4玻璃纤维环氧板
+ 厚度：1.6mm
+ 铜层厚度：35μm（1oz）

![](/images/week08/w08-g-8.jpg)

> 覆铜板在切割前，需要再背面贴上双面胶，以便能更稳固的固定再作为垫板的木版上。然后还需要将黏在木版上的覆铜板用夹具固定

![](/images/week08/w08-g-9.jpg)

> Matthew 指导我们如何对设备归零和校准 Z 轴高度

## 工作流程与G代码生成

CNC机器需要G代码来执行切割操作。我们使用 Mods CE 转换图像

1. 访问 [Mods项目网站](https://modsproject.org/?program=programs/machines/G-code/mill%202D%20PCB)

![](/images/week08/w08-g-10.png)

> G-Code Mill 2D PCB

2. 上传PNG或SVG格式的图像文件（无损分辨率格式）

![](/images/week08/w08-g-11.png)

![](/images/week08/w08-g-11-2.png)

> 这个界面是 MODs 软件中用于导入和处理 PCB 设计 PNG 文件的模块。解释一下各个元素的功能：
>
> ##### 顶部控制区
>
> + read png：当前模块的名称，用于读取和处理 PNG 图像文件。
> + edit delete：编辑或删除当前模块的选项。
>
> ##### 中央区域（图像显示）
>
> + 显示当前加载的 PCB 设计图像（这里是 XIAO 扩展板的 PCB 走线图）。
> + 图像显示为黑白两色，黑色区域代表铜层，白色区域代表要铣削的区域。
>
> ##### 底部控制按钮
>
> + select png file：点击选择并上传 PNG 文件
> + view：查看当前图像的完整视图
> + invert：反转图像颜色（黑变白，白变黑），用于转换铣削策略
> + flip H：水平翻转图像
> + flip V：垂直翻转图像
> + rotate 90CW：将图像顺时针旋转 90 度
>
> ##### 图像信息区域
>
> + dpi：图像分辨率（每英寸点数），这里设置为 999.998
> + 2220 x 1491 px：图像像素尺寸（宽 x 高）
> + 56.388 x 37.871 mm：实际物理尺寸（毫米）
> + 2.220 x 1.491 in：实际物理尺寸（英寸）
> + traces_top_layer_0.png：当前加载的文件名
>
> ##### 右侧输出区域
>
> + outputs：输出信息区域标题
> + image (RGBA)：输出的图像数据（RGBA 格式）
> + imageInfo (object)：包含图像的详细信息的对象
>
> ##### 重要使用说明
>
> 1. DPI 设置的重要性：
>    - DPI 值决定了从像素到实际物理尺寸的转换比例
>    - 确保 DPI 设置正确，否则铣削尺寸会不准确
> 2. 图像方向：
>    - 使用翻转和旋转按钮确保图像方向正确
>    - 需要根据您的 CNC 机器坐标系统进行调整
> 3. 反转功能：
>    - **黑色区域**代表要铣削掉的部分（即电路隔离沟槽）
>    - **白色区域**代表要保留的铜层（即电路走线和焊盘）
>    - 在 PCB 铣削过程中，CNC 机器会沿着黑色的线条和区域进行铣削，移除铜层形成隔离沟槽，从而创建出所需的电路图案。如果导入的图像与此约定相反（例如白色表示要铣削的部分），则需要使用"invert"按钮反转颜色。
> 4. 尺寸确认：
>    - 在进行实际铣削前，确认显示的物理尺寸（mm 或 in）与您预期的设计尺寸相符
>
> 导入 PNG 文件后，需要将此图像传递给我们之前讨论的铣削模块（如 isolate traces、mill outline 等），以生成实际的 G 代码文件用于 CNC 机器。

3. 配置切割参数

![](/images/week08/w08-g-12.png)

> 这张图显示的是 MODs 软件中用于 PCB 铣削的默认参数设置，下面详细解释各个参数的含义：
>
> ##### 整体功能区域
>
> + **set PCB defaults**：设置 PCB 铣削的默认参数
> + **edit delete**：编辑或删除当前设置
>
> ##### 工艺类型（四种不同的加工方式）
>
> 1. **isolate traces (1/64)**：隔离走线（使用 1/64 英寸刀具）
> 2. **clear copper (1/32)**：清除铜层（使用 1/32 英寸刀具）
> 3. **mill outline (1/32)**：铣削板子外框（使用 1/32 英寸刀具）
> 4. **mill traces (10 mil)**：铣削精细走线（使用 10 mil 刀具）
>
> ## 共同参数解释
>
> 每种工艺都有以下参数设置：
>
> + **tool diameter (in)**：刀具直径（英寸）
>   - 不同工艺使用不同尺寸刀具（从 0.01 英寸到 0.0312 英寸不等）
> + **cut depth (in)**：单次切削深度（英寸）
>   - 表示每次通过时切削的深度
>   - 走线隔离通常在 0.004-0.006 英寸
>   - 外框切割较深，为 0.024 英寸
> + **max depth (in)**：最大切削深度（英寸）
>   - 铣削的总深度
>   - 对于外框，设置为 0.072 英寸（约 1.83mm），略大于标准 1.6mm PCB 厚度
> + **offset number**：偏移次数
>   - 表示围绕目标路径的切削次数
>   - 走线隔离通常设为 4，确保完全隔离铜线
>   - 外框切割只需 1 次精确沿线切割
> + **offset stepover**：偏移步进量
>   - 相邻切削路径之间的重叠比例（0.5 表示重叠 50%）
>   - 所有工艺都设为 0.5，这是一个平衡效率和质量的值
> + **speed (mm/s)**：切削速度（毫米/秒）
>   - 刀具移动的速度
>   - 较粗的工具速度较快（4mm/s）
>   - 精细刀具降低到 2mm/s 避免损坏
> + **tab width (in)** 和 **tab length (in)**：连接桥宽度和长度（英寸）
>   - 切割外框时保留的小连接点，防止板子在切割完成前脱落
>   - 设为 0 表示不使用连接桥
>
> ## 各工艺特点对比
>
> 1. **隔离走线 (1/64)**：
>    - 使用细刀具（0.0156 英寸/约 0.4mm）
>    - 浅切削（0.006 英寸）
>    - 多次偏移（4次）确保线路完全隔离
> 2. **清除铜层 (1/32)**：
>    - 使用较粗刀具（0.0312 英寸/约 0.8mm）
>    - 相同的切深（0.006 英寸）
>    - 无偏移（0），直接清除指定区域
> 3. **外框切割 (1/32)**：
>    - 使用同样的粗刀具（0.0312 英寸）
>    - 较深的切削（0.024 英寸每次）
>    - 较深的最大深度（0.072 英寸/约 1.83mm）确保完全切透
> 4. **精细走线 (10 mil)**：
>    - 使用最细刀具（0.01 英寸/约 0.25mm）
>    - 最浅的切削（0.004 英寸）
>    - 最慢的速度（2mm/s）保护精细刀具
>
> 这些参数是经过优化的默认值，适用于大多数标准 PCB 铣削任务。根据您的具体设备和材料，可能需要进行一些微调。

4. 生成.nc格式的G代码文件，通过 mill raster 2D 模块输出，点击此模块的“**calculate**（计算刀具路径）”按钮，还会在计算完成后立即获得.nc 文件下载。

![](/images/week08/w08-g-13.png)

> 个界面显示的是 MODs 软件中的 "mill raster 2D" 模块，它用于将之前导入的 PNG 图像转换为 CNC 铣床可执行的刀具路径。下面详细解释这个界面的各个部分和参数：
>
> #### 主要区域
>
> ##### 顶部控制区
>
> + **mill raster 2D**：模块名称，用于将位图（栅格）图像转换为铣削路径
> + **edit delete**：编辑或删除当前模块
>
> ##### 左侧输入区（黄色）
>
> + **inputs**：输入数据标签
> + **imageInfo (object)**：从上一步导入的图像信息
> + **path (array)**：定义的路径数组
> + **settings (object)**：配置设置对象
>
> ##### 右侧输出区（橙色）
>
> + **outputs**：输出数据标签
> + **diameter (number)**：计算出的刀具直径
> + **offset (number)**：计算出的偏移量
> + **toolpath (object)**：生成的刀具路径对象，将被发送到 CNC 控制器
>
> #### 中央配置区（灰色）
>
> ##### 刀具设置
>
> + **tool diameter**：刀具直径，同时显示毫米（0.39624mm）和英寸（0.0156英寸）
>   - 这是标准的1/64英寸铣刀，用于PCB走线隔离
>
> ##### 切削深度设置
>
> + **cut depth**：单次切削深度，0.1016mm（0.004英寸）
>   - 每次通过铣削的深度
> + **max depth**：最大切削深度，也是0.1016mm（0.004英寸）
>   - 对于走线隔离，通常只需切削到足够切断铜层的深度
>
> ##### 偏移设置
>
> + **offset number: 4**：偏移次数
>   - 值为4表示会创建4条平行的切削路径，确保完全隔离走线
>   - 说明"(0 = fill)"指出设为0时会进行填充铣削
> + **offset stepover: 0.5**：偏移步进量
>   - 值为0.5表示相邻路径重叠50%
>   - 说明"(1 = diameter)"表示设为1时步进量等于刀具直径
>
> ##### 切削方向和路径设置
>
> + **direction**：选择铣削方向
>   - **climb**（顺铣）：已选择，通常产生更好的切削质量
>   - **conventional**（逆铣）：未选择
> + **path merge: 1**：路径合并阈值
>   - 值为1（等于刀具直径）表示相距小于刀具直径的路径会被合并
> + **path order**：路径顺序
>   - **forward**（正向）：已选择
>   - **reverse**（反向）：未选择
> + **sort distance**：已勾选，表示将按距离排序路径，减少刀具移动
>
> ##### 底部控制按钮
>
> + **calculate**：计算刀具路径，还会在计算完成后立即获得.nc 文件下载
> + **view**：查看计算出的路径
>
> ##### 底部预览区
>
> + 显示计算出的刀具路径预览
> + 可以看到刀具将沿着电路板走线的边缘移动，创建隔离沟槽
> + 图像中显示的是您的XIAO扩展板设计的铣削路径
>
> #### 工作原理
>
> 这个模块的工作过程是：
>
> 1. 读取导入的PNG图像（黑色区域表示要铣削的部分）
> 2. 根据设置的参数（刀具尺寸、切削深度等）计算出铣削路径
> 3. 生成优化的刀具移动轨迹（如图中所示的线条）
> 4. 这些线条表示CNC机器将如何移动刀具来创建隔离沟槽
>
> 在预览图中，可以看到刀具将沿着电路板走线的边缘铣削，形成隔离沟槽。每条线代表一次刀具通道，多条平行线（由offset number参数控制）确保完全切断铜层以隔离电路。
>
> 点击"calculate"后，系统会生成详细的刀具路径；点击"view"可以预览这些路径，确保它们符合您的设计意图。

## 参数测试与记录

因为和 2024 年的小组使用的相同设备，我们参数测试参考了 Matthew 的文档：[CNC cutting, MCU development board making and testing](https://fabacademy.org/2024/labs/chaihuo/students/matthew-yu/docs/quentorres/)。

## 小组个人作业切割

通过 Mods CE 导出的 2 个.nc 文件，通过 U 盘，在运行 Mach3 的电脑上分别加载，通常的顺序是：

1. 先换 1/64英寸（0.4mm）V型刀头，加载隔离走线的 .nc 文件，在 Mach3  设置好原点，切割出走线，如下图所示。

![](/images/week08/w08-g-14.jpg)

> Mach3 在切割走线文件

![](/images/week08/w08-g-15.jpg)

> 完成走线的切削，这一步完成后，切记不要动板子

2. 完成走线切削后，切记不要动板子，更换 1/32英寸（0.8mm）直槽铣刀刀头，然后加载边框切割文件，切割边框。

![](/images/week08/w08-g-16.jpg)

> 更换  1/32英寸（0.8mm）直槽铣刀刀头

![](/images/week08/w08-g-17.jpg)

> 加载边框切割.nc 文件切割

![](/images/week08/w08-g-18.jpg)

> 切割边框会比较深，会出很多碎屑

我和刘鸿泰分别完成了各自作业的切割工作，鸿泰担心自己的线路部分有点太细（他按 0.25mm 宽的走线设计的 PCB），这会给后期的焊接带来进一步的挑战。我是按 0.5mm 宽的走线设计，感觉对于铣削制造 PCB 会更友好一些。

![](/images/week08/w08-g-19.jpg)

> 左侧鸿泰的板子使用 0.25mm 的走线宽度，感觉有些过细。我使用 0.5mm 的走线宽度，对于铣削工艺制造 PCB 感觉要友好一些

## 挑战与解决方案

在完成这次小组作业的过程中，我们遇到了一些挑战，但都成功解决了：

### 设备连接问题

我们在周五晚上发现驱动CNC的电脑无法点亮，这台电脑已经使用多年。经过多次尝试：

1. 周六，组员刘鸿泰购买了USB转并口适配器，尝试在另一台电脑上安装CNC软件，但设备仍无法被识别。
2. 周一上午，柴火创客空间管理员李晋豪为故障电脑更换了新电源，成功解决了电脑不能启动的问题，也由此损失了周末 2 天的宝贵时间，这直接导致我们后续的焊接和文档时间不足。

### 设备启动问题

周一晚上，在Matthew的指导下进行培训时，我们仍然无法让CNC机器工作。经管理员李晋豪提醒，发现问题出在急停按钮上：

急停按钮被按下后不会自动弹起，需要手动旋转释放，这是一种安全设计。

![](/images/week08/w08-g-20.jpg)

> CNC机器的红色急停按钮，如果电脑无法连接 CNC，手轮显示屏不亮，大概率是这个急停按钮没有释放

释放急停按钮后，机器恢复正常工作状态，我们得以顺利完成测试和切割。

## 经验总结

通过这次小组作业，我们总结出以下关键经验：

1. **预检查很重要**：使用设备前检查电源、连接和急停按钮状态
2. **参数优化是关键**：不同工具需要不同的切削参数，需要多次测试才能获得最佳效果
3. **材料固定要牢固**：PCB板在切割过程中容易翘起，需要使用双面胶和固定板同时固定
4. **工具选择影响质量**：V型刀头对于切割走线效果更好，而直槽铣刀适合切割外形
5. **清洁与维护**：切割完成后及时清理设备，延长工具寿命

## 参考资源

+ [Mods项目G代码生成工具](https://modsproject.org/?program=programs/machines/G-code/mill%202D%20PCB)
+ [PCB铣削最佳实践指南](http://academy.cba.mit.edu/classes/electronics_production/index.html)
+ [G代码基础教程](https://www.cnccookbook.com/g-code-tutorial/)

# 向PCB制造商提交设计：嘉立创制板经验分享

本文记录了我向嘉立创提交PCB设计的完整流程，作为第8周电子产品制作课程的小组作业成果。通过这次实践，我们掌握了从设计到提交、下单、审核再到最终生产的全过程，为以后的PCB制作积累了宝贵经验。

## 准备工作

### 设计文件准备

在第 6 周的作业中，我设计了一个基于XIAO ESP32C3的扩展板。

![](/images/week08/w08-g2-1.png)

> 第 6 周个人作业准备好的 PCB 设计文件

完成设计后，需要导出Gerber文件供制造商生产。最初，我通过 KiCAD 的顶部菜单“文件>制造输出>Gerbers(.gbr)” ，只准备了以下Gerber文件：

+ `XIAO_ESP32C3_Extension_CNC2-F_Cu.gbr` - 顶层铜箔
+ `XIAO_ESP32C3_Extension_CNC2-Edge_Cuts.gbr` - 板子外形轮廓

![](/images/week08/w08-g2-2.png)

> 第一次尝试制造输出，只提供了 F_Cu，Edge_Cuts 层的内容

### 2.2 学习要点

这次实践让我明白，**完整的PCB制造文件必须包含更多内容**。通过与制造商的沟通和自主学习，我补充了以下必要文件：

+ `XIAO_ESP32C3_Extension_CNC2-B_Mask.gbr` - 钻孔文件
+ `XIAO_ESP32C3_Extension_CNC2-F_Mask.gbr` - 阻焊层
+ `XIAO_ESP32C3_Extension_CNC2-F_Silkscreen.gbr` - 丝印层

这一经验告诉我，在商业PCB制造中，完整的文件准备对顺利生产至关重要。

## 提交与下单流程

### PCB制板下单

我在 2025 年 3 月 15 日下午，选择了嘉立创作为 PCB 制造商，开始进行 PCB 制版下单的尝试，以下是详细的下单步骤：

1. 进入[嘉立创 PCB 在线下单平台](https://www.jlc.com/)。

![](/images/week08/w08-g2-3.png)

> 嘉立创 PCB 制造首页

2. 选择基本参数：
   - 板材类别：FR-4
   - 板子尺寸：5.48 × 3.63 cm
   - 板子数量：5片
   - 板子层数：1层
   - 成品板厚：1.0mm
   - 应用行业：消费电子
   - 产品类型：经济型

![](/images/week08/w08-g2-4.png)

> PCB 在线下单选项众多，第一次看还是会有些晕的

3. 上传 Gerber 文件：提交准备好的 Gerber 文件的 zip 包（嘉立创平台只允许上传 zip 或 rar 的压缩包）。

我开始以为只需要提供下面这 2 个文件：

+ `XIAO_ESP32C3_Extension_CNC2-F_Cu.gbr` - 顶层铜箔
+ `XIAO_ESP32C3_Extension_CNC2-Edge_Cuts.gbr` - 板子外形轮廓

4. 等待技术审核：系统自动进行文件检查。

### 技术审核

我开始以为没啥问题了，结果晚饭后接到了嘉立创员工的电话，说我技术审核未通过：缺少必要的钻孔、阻焊及丝印文件，我不知道阻焊是什么意思，就询问嘉立创员工。员工说没有这个文件，会无法焊接。所以我恶补了这部分知识。

> ### PCB制造必要文件的基础知识
>
> #### 钻孔文件 (Drill File)
>
> + **功能**：定义PCB板上所有孔的位置、尺寸和类型
> + **文件格式**：通常为Excellon格式(.drl)或Gerber格式(.gbr)
> + **孔的类型**：
>   - 通孔(Through Hole)：连接PCB顶层和底层的孔
>   - 装配孔(Mounting Hole)：用于固定PCB的孔
>   - 过孔(Via)：连接不同铜层的电气连接
> + **重要性**：精确的钻孔文件确保元器件能够正确安装，电气连接可靠
>
> 因为我这个板子是为单面贴装设计，不用考虑钻孔问题，所以没有输出钻孔文件。如果你的项目需要输出钻孔文件，可以在顶部菜单的“文件>制造输出>钻孔文件（.drl）” 找到输出。
>
> #### 阻焊层文件 (Solder Mask)
>
> + **功能**：定义PCB上不需要覆盖阻焊油墨的区域，通常是焊盘和测试点
> + **文件格式**：Gerber格式，通常命名为 `*_Mask.gbr`
> + **作用**：
>   - 保护铜箔不被氧化
>   - 防止焊接时形成短路
>   - 提高电气绝缘性
>   - 在焊盘处形成开口，便于焊接
> + **重要性**：没有正确的阻焊层，PCB将难以焊接或可能在使用过程中出现短路
>
> 我在 KiCAD 找到了 PCB 的外观图层的 `F.Mask` 层，如下图所示。如果这一层没有输出给制造商，整个 PCB 上会涂满保护电路的阻焊油墨，而无法焊接元器件。

![](/images/week08/w08-g2-5.png)

> `F.Mask` 层代表首层阻焊层

![](/images/week08/w08-g2-6.png)

> `B.Mask` 层代表背面阻焊层
>
> #### 丝印层文件 (Silkscreen)
>
> + **功能**：定义PCB表面上的文字、标记、图案和标识
> + **文件格式**：Gerber格式，通常命名为 `*_Silkscreen.gbr`
> + **常见内容**：
>   - 元器件标识和编号(如R1, C3, U2等)
>   - 极性标记(如电容、二极管的正负极)
>   - 连接器引脚编号
>   - 警告标识和logo
>   - 版本号和日期
> + **重要性**：丝印帮助识别元器件位置，便于组装、调试和维修

![](/images/week08/w08-g2-7.png)

> F.Silkscreen 层代表首层丝印内容
>
> 了解了这些内容后，我重新在 KiCAD 的顶部菜单“文件>制造输出>Gerbers(.gbr)” 输出了以下层：F.Cu，F.Silkscreen，F.Mask，B.Mask，Edge.Cuts。

![](/images/week08/w08-g2-8.png)

> 第 2 次重新通过 KiCAD 的制造输出 Gerbers(.gbr)

> #### 文件准备注意事项
>
> + **对齐精度**：所有层文件必须完全对齐
> + **孔径标准化**：钻孔尺寸应遵循制造商的能力范围
> + **阻焊开窗**：焊盘周围的阻焊开窗通常比焊盘略大(扩展2-3mil)
> + **丝印位置**：避免丝印文字覆盖焊盘区域
> + **文件命名**：遵循标准命名约定，便于制造商识别
>
> 完整的PCB制造文件集通常包括铜箔层、阻焊层、丝印层、钻孔文件和板子外形文件，这些文件共同定义了PCB的物理特性和制造要求。缺少任何一个关键文件都可能导致制造问题或审核失败。

![](/images/week08/w08-g2-9.png)

> 将导出的.gbr 文件打包压缩为 zip 文件后再次提交

经过补充文件并重新提交后，审核通过并进入生产流程。这个过程教会了我PCB制造所需的完整文件体系。

### 贴片服务评估

除了基础PCB制造，我还探索了 SMT 贴片服务，SMT 在线下单需要提供 BOM 与坐标清单，如下图所示。

![](/images/week08/w08-g2-10.png)

> SMT 在线下单需要提供 BOM 与坐标清单

1. 上传 BOM 和坐标文件，这些都可以在 KiCAD 的顶部菜单“文件>制造输出” 下找到。元件定位文件我选了 CSV 格式，如下图所示。

![](/images/week08/w08-g2-11.png)

定位文件内容如下所示。

```plain
Ref,Val,Package,PosX,PosY,Rot,Side
"D1","LED_1206","LED_1206",150.100000,-85.950000,0.000000,top
"D2","LED_1206","LED_1206",150.100000,-89.200000,0.000000,top
"D3","LED_1206","LED_1206",150.100000,-92.450000,0.000000,top
"D4","LED_1206","LED_1206",150.100000,-95.700000,0.000000,top
"D5","LED_1206","LED_1206",150.100000,-98.950000,0.000000,top
"D6","LED_1206","LED_1206",150.100000,-102.200000,0.000000,top
"J1","PinHeader_01x08_P2.54mm_Horizontal_SMD","PinHeader_01x08_P2.54mm_Horizontal_SMD",185.280000,-112.250000,-90.000000,top
"M1","Module_XIAO-ESP32C3","SeeedStudio_XIAO_ESP32C3",176.165000,-94.880000,0.000000,top
"R1","R_1206","R_1206",159.150000,-85.950000,180.000000,top
"R2","R_1206","R_1206",159.150000,-89.210000,180.000000,top
"R3","R_1206","R_1206",159.150000,-92.470000,180.000000,top
"R4","R_1206","R_1206",159.150000,-95.730000,180.000000,top
"R5","R_1206","R_1206",159.150000,-98.990000,180.000000,top
"R6","R_1206","R_1206",159.150000,-102.250000,180.000000,top
"R7","R_1206","R_1206",154.750000,-114.750000,0.000000,top
"R8","R_1206","R_1206",193.250000,-97.375000,0.000000,top
"R9","R_1206","R_1206",193.250000,-93.250000,0.000000,top
"SW1","Switch_Tactile_Omron","Button_Omron_B3SN_6.0x6.0mm",155.250000,-108.250000,180.000000,top

```

嘉立创对定位文件有格式要求，需要注意字段名需要按其文档样式标准保持一致，否则会无法识别。所以我重新修改了定位 CSV 文件的字段名

![](/images/week08/w08-g2-12.png)

> 嘉立创提供的坐标文件示例

2. 系统自动匹配可用元器件，但提示我的 BOM 文件匹配度不高，大部分需要手动选择嘉立创平台提供的元件库的元件进行匹配。

![](/images/week08/w08-g2-13.png)

> 我的 BOM 文件匹配度不高

3. XIAO ESP32S3 模块无法直接通过嘉立创提供，只能标记为不贴的元器件，设置界面还能实时预览贴片后的效果图，如下图所示。

![](/images/week08/w08-g2-14.png)

> 替换无法匹配的元件，并查看贴片后的效果

最终贴 2 个板子的贴片方案报价约为304.33元，如下图所示。考虑到自行焊接的可行性和经济因素，我最终选择仅制作PCB板。

![](/images/week08/w08-g2-15.png)

> 嘉立创给出贴 2 个板子的价格是 304.33 元

## 价格分析

### PCB制板价格

最终确认的PCB制板订单价格详情：

+ 基础价格：104.49元
+ 数量：5片单面板
+ 单片成本：约20.90元

价格组成：

+ 测试费：30.51元
+ 材料费：30.00元
+ 确认生产费：3.00元
+ 其他费用：40.98元

### SMT贴片价格评估

虽然未最终选择SMT贴片服务，但获得了以下报价信息：

+ SMT贴片总价：约304.33元
+ 贴片数量：2片（剩余3片作为备用）

## 生产进度跟踪

嘉立创平台提供了详细的生产进度追踪功能，我的订单经历了以下生产步骤：

1. **下单**：03-15 19:08
2. **审核**：03-15 19:17
3. **付款**：03-15 19:17
4. 生产：03-15 23:18
5. 发货:  03-17 18:59

| 1 MI           | 03-15 23:18                                                  |
| -------------- | ------------------------------------------------------------ |
| 2 钻孔         | 03-16 02:03                                                  |
| 3 线路         | 03-16 02:19                                                  |
| 4 蚀刻         | 03-16 02:20                                                  |
| 5 AOI          | 03-16 05:07                                                  |
| 6 阻焊         | 03-16 09:16                                                  |
| 7 字符         | 03-16 13:29                                                  |
| 8 喷锡         | 03-16 14:08                                                  |
| 9 AVI 检验     | 03-16 15:50                                                  |
| 10 锣边、V-CUT | 03-17 10:17                                                  |
| 11 测试        | 测试完成                                                     |
| 12 QC          | 03-17 15:02                                                  |
| 13 发货        | `<font style="color:rgb(0, 0, 0);">`03-17 18:59`</font>` |

03-15 19:08 下单， 03-17 18:59发货，3 月 18 日上午，我就拿到了自己设计的 PCB 板子。这可能也是在深圳生活的好处之一。如果你愿意付更多钱，这个时间还能更短一些。

![](/images/week08/w08-g2-16.png)

3 月 18 日上我，我就收到了制造好的 PCB，拿到人生的第一批自己设计的“量产”PCB，还是很激动的。

![](/images/week08/w08-g2-17.jpg)

> 5 块 PCB 板子被密封包装，还放了干燥剂

![](/images/week08/w08-g2-18.jpg)

> 板子做了切割线，用手可以掰开

![](/images/week08/w08-g2-19.jpg)

> 感觉制造工艺很完美

## 6. 经验与教训

### 6.1 文件准备经验

1. **完整性至关重要**：PCB制造需要完整的文件集，包括铜箔层、钻孔、阻焊、丝印等
2. **格式标准化**：制造商通常需要标准Gerber格式文件
3. **预检查**：提交前应使用Gerber查看器检查文件的完整性和正确性

### 6.2 制造商选择经验

1. **技术支持**：嘉立创提供了及时的技术支持，在文件不完整时主动联系
2. **价格透明**：平台提供了详细的价格构成
3. **全流程服务**：从PCB制造到元器件贴装的一站式服务很便捷

## 7. 结论

通过这次向嘉立创提交PCB设计的实践，我获得了PCB商业制造的第一手经验。这个过程不仅完成了课程作业要求，更重要的是让我理解了从设计到实物的完整工作流程。

这次经历特别强调了准备完整制造文件的重要性，以及与制造商良好沟通的必要性。最终，我成功获得了5片高质量的PCB板，为后续的组装和测试奠定了基础。

制造商提供的在线平台大大简化了下单流程，详细的生产进度追踪也提供了很好的用户体验。这些工具和服务对于快速原型开发和小批量生产非常有价值。

## 8. 参考资料

1. 嘉立创PCB在线下单平台：[https://www.jlc.com/](https://www.jlc.com/)
2. Gerber文件格式规范：[http://www.gerber-format.com/](http://www.gerber-format.com/)
3. PCB制造工艺流程：[https://www.jlc.com/portal/vtechnology.html](https://www.jlc.com/portal/vtechnology.html)
