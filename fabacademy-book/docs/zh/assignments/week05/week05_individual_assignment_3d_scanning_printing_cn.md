---
layout: doc
title: "第5周：3D扫描与打印实践 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第五周个人作业：使用手机应用进行3D扫描并用3D打印机复制风狮爷雕像"
head:
  - - meta
    - name: keywords
      content: fab academy, 3D扫描, 摄影测量, KIRI Engine, 3D打印, Bambu Lab X1 Carbon, 模型修复, 风狮爷, 可变层高
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第5周：3D打印机设计规则测试'
  link: '/zh/assignments/week05/week05_group_assignment_3d_printer_test_cn'
next:
  text: '第5周：3D打印硅藻结构'
  link: '/zh/assignments/week05/week05_individual_diatom_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第 5 周个人作业：3D 扫描与打印

## 项目概述
本次项目选择了一个朋友送的礼物：日本旅游纪念品——风狮爷，作为3D扫描与打印的实验对象。风狮爷是成对售卖的小狮子雕像，表情各异，我选择了左边这只张嘴吐舌的。然后通过手机3D扫描应用获取模型，并使用Bambu Lab X1 Carbon 3D打印机完成最终打印。

![](/images/week05/w05-p-1.jpg)

## 材料与工具
+ 扫描对象：风狮爷雕像
+ 扫描工具：KIRI Engine: 3D Scanner App (手机应用)
+ 3D建模处理：Bambu Studio
+ 3D打印机：Bambu Lab X1 Carbon（0.4mm喷嘴）
+ 打印材料：Bambu ABS（白色）

## 详细工作流程
### 1. 3D扫描过程
因为手头没有专业 3D 扫描仪，就考虑使用摄影建模的方案。尝试比较了几个不同的手机 APP，和软件，最终使用 KIRI Engine: 3D Scanner App ([KIRI Engine](https://www.kiriengine.app/)) 进行扫描，该应用支持 iPhone 和 Android，基于摄影测量技术，通过多张照片重建 3D 模型。而且对于免费用户提供无限扫描功能（限制 3 个模型项目，对我的测试足够了），适合初学者。

#### 1.1 扫描准备
+ 将风狮爷雕像放置在光线充足、背景简单的平面上。
+ 打开手机KIRI Engine应用并选择视频扫描模式。

![画板](/images/week05/w05-p-2.jpg)

> 用手机 app KIRI Engine: 3D Scanner 拍摄风狮爷视频并上传
>

#### 1.2 扫描操作
+ 以雕像为中心，保持约30cm的距离。
+ 选择视频模式进行扫描，以便获得更连续的表面数据。
+ 在不同高度角度环绕雕像拍摄了两圈完整视频。
    - 第一圈：略高于水平线视角。
    - 第二圈：略低于水平线视角，以捕捉底部细节。

#### 1.3 模型生成与优化
+ 将视频上传至KIRI Engine云服务器进行处理。
+ 处理时间约15分钟，系统自动完成点云计算和网格生成。
+ 使用应用内的Crop（裁剪）功能移除多余的背景和底座。
+ 导出处理后的模型至邮箱。

![画板](/images/week05/w05-p-3.jpg)

> 上传后在服务器端处理获得模型，裁剪大小后导出模型
>

#### 1.4 模型获取
+ 从邮箱下载收到的链接文件（ZIP格式）。
+ 解压后获得以下文件：
    - `3DModel.obj` - 高精度模型几何数据
    - `3DModel.mtl` - 材质定义文件
    - `3DModel.jpg` - 模型纹理贴图
    - `3DModel_LowPoly.obj` - 简化版几何数据
    - `3DModel_LowPoly.mtl` - 简化版材质定义
    - `3DModel_LowPoly.jpg` - 简化版纹理贴图

### 2. 3D模型处理
#### 2.1 导入模型
+ 打开Bambu Studio软件。
+ 使用"添加模型"功能，选择`3DModel.obj`文件。
+ 模型成功导入，模型角度不对，而且软件出现提示警告，报告模型超过打印边界，并存在"100161 个非流形边"错误。

![](/images/week05/w05-p-4.png)

#### 2.3 模型调整
+ 使用"自动朝向"功能将模型调整至最佳打印方向。

![](/images/week05/w05-p-5.png)

> 使用自动朝向功能后，小狮子
>

+ 应用"缩放"功能将模型按比例缩小至合适尺寸。

![](/images/week05/w05-p-6.png)

> 使用缩放功能将模型调整到合适的大小
>

+ 使用"剪切"功能去除底部平面，确保打印底座平整。

![](/images/week05/w05-p-7.png)

> 使用切割工具去除底座平面
>

去除底座后的模型如下图所示。

![](/images/week05/w05-p-8.png)

> 去除了底座的模型
>

<model-viewer
  src="/2025/labs/chaihuo/students/lei-feng/models/3dmodel_lowpoly-shishi.glb"
  alt="展示低多边形模型"
  auto-rotate
  camera-controls
  shadow-intensity="1"
  style="width: 100%; height: 400px;"
></model-viewer>

> 展示低多边形模型
>

#### 2.2 模型修复
+ 使用 Bambu Studio 的"修复模型"功能处理非流形边（non-manifold edges），注意这个修复功能只在 Windows 电脑版本可用，所以我找了个 Windows 电脑完成了这个步骤。
+ 修复完成后检查模型完整性，确保表面封闭。

> **说明：** 非流形边是指模型中边缘连接不正确的部分，可能包括悬浮顶点或无厚度的面 ([Non-Manifold Edges Fix](https://www.sculpteo.com/en/3d-learning-hub/create-3d-file/fix-non-manifold-geometry/))。修复后确保模型适合打印。
>

#### 2.3 可变层高调整
在开始切片前，我尝试了 Bambu Studio 软件的“可变层高”功能，进入这个功能后，软件会用类似等高线的方式展示层高可能形成的表面纹理效果。由于设置了 0.2mm 的层高，目前看靠近顶部比较平坦的区域，可能形成比较明显的纹理。

![](/images/week05/w05-p-9.png)

> 进入“可变层高”功能后，屏幕最右侧会出现一个灰度线，展示不同高度的层高变化，因为现在是等高度（0.2mm），所以右侧可以看到是等间距的灰色条纹
>

我尝试点击了弹出窗口的“自适应”和“平滑模式”按钮，可以看到头顶部平坦区域出现纹路的情况被缓解。

![](/images/week05/w05-p-10.png)

> 可以看到软件自动添加了层高调整策略，并做了平滑处理，右侧展示不同高度的层高变化，绿色越深的部分表示层高越小（打印时间越长）
>

### 3. 切片与打印准备
#### 3.1 打印参数设置
+ 选择打印机型号：Bambu Lab X1 Carbon
+ 喷嘴直径：0.4mm
+ 材料类型：Bambu ABS（白色）
+ 层高设置：启用"可变层高"功能
    - 模式选择：自适应和平滑模式
    - 层高：0.2mm

#### 3.2 切片预览
+ 运行切片操作。
+ 软件显示预计耗材用量：3.75克。
+ 预计打印时间：41分钟。
+ 检查切片结果，确认所有细节都能正确打印。

![](/images/week05/w05-p-11.png)

> 切片单盘后给出报告
>

### 4. 3D打印过程
#### 4.1 打印机准备
+ 确认打印平台清洁。
+ 装载白色 Bambu ABS 耗材并确认挤出正常。
+ 将切片文件传输至打印机。

#### 4.2 打印执行
+ 启动打印任务并监控第一层的附着情况。
+ 观察打印过程中的层间粘合和细节成型。
+ 打印顺利完成，无层错位或翘边现象。

![](/images/week05/w05-p-12.jpg)

> 风狮爷原件与 3D 扫描与 3D 打印复刻件对比
>

### 5. 成果对比与分析
#### 5.1 打印成果
+ 打印件整体结构完整，细节保留情况并不理想，狮子嘴巴部分的空心结构细节没有表达出来，狮子身体下的空间也没有表现出来。
+ 白色ABS材料展现了良好的表面质感。
+ 可变层高技术有效提升了曲面的平滑度。

#### 5.2 与原件对比
+ 尺寸：打印件小于原件（按比例缩小）。
+ 细节：大部分细节如表情、毛发纹理均被成功复制。
+ 材质感：原件为陶瓷质感，打印件为塑料质感，视觉效果有明显区别。
+ 重量：打印件明显轻于原件。

## 总结与反思
### 成功之处
1. 使用手机应用完成了高质量的3D扫描，证明了消费级设备在简单对象扫描上的可行性。
2. 成功处理了模型中的非流形边等问题，保证了打印质量。
3. 可变层高技术显著提升了打印件的表面质量，减少了明显的层纹。

### 改进空间
1. 扫描过程中可增加更多角度，特别是底部和顶部的细节捕捉。
2. 考虑使用后处理技术（如打磨、上色）使打印件更接近原件质感。
3. 尝试使用更小直径喷嘴（如0.2mm）提高细节精度。

### 学习心得
这次项目展示了从实物到数字模型再到实物的完整工作流程，是数字制造中非常重要的技能。尽管存在一些技术限制，但随着消费级扫描和打印技术的进步，这种工作流程变得越来越平易近人，为创意设计和快速原型制作提供了极大便利。

#### 关键引用
+ [KIRI Engine: 3D Scanner App for iPhone, Android and Web](https://www.kiriengine.app/)
+ [KIRI Engine review: a free, powerful 3D scanning app for Android/iOS](https://www.aniwaa.com/review/3d-scanners/kiri-engine-review-a-free-powerful-3d-scanning-app/)
+ [Software Studio - Bambu Lab](https://bambulab.com/en/download/studio)
+ [GitHub - bambulab/BambuStudio: PC Software for BambuLab and other 3D printers](https://github.com/bambulab/BambuStudio)
+ [How to fix non-manifold geometry issues on 3D models](https://www.sculpteo.com/en/3d-learning-hub/create-3d-file/fix-non-manifold-geometry/)
+ [Variable Layer Height | Bambu Lab Wiki](https://wiki.bambulab.com/en/software/bambu-studio/adaptive-layer-height)

