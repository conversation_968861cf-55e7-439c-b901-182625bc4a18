---
layout: doc
title: "第5周：3D打印硅藻结构 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第五周个人作业：设计并3D打印具有内部空腔的硅藻结构模型"
head:
  - - meta
    - name: keywords
      content: fab academy, 3D打印, 微观结构, 硅藻, Bambu Lab A1 Combo, PETG, 支撑结构, 树状支撑, 可变层高
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第5周：3D 扫描与打印'
  link: '/zh/assignments/week05/week05_individual_assignment_3d_scanning_printing_cn'
next:
  text: '第5周：设计3D打印笔筒'
  link: '/zh/assignments/week05/week05_3d-pen-holder-assignment_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 第5周个人作业：3D打印硅藻结构

本周作业的要求之一，是“设计并 3D 打印一个小物体（几立方厘米，考虑打印时间限制），该物体无法通过减材制造完成，如内部空腔结构。”

在思考打印对象的时候，过去曾经看过的硅藻的画面就闪现在脑海中。

![](/images/week05/w05-d-1.jpg)

> 《[自然界的艺术形态](https://zh.wikipedia.org/wiki/%E8%87%AA%E7%84%B6%E7%95%8C%E7%9A%84%E8%97%9D%E8%A1%93%E5%BD%A2%E6%85%8B)》 (1904), 图版 4: Diatomeae
>

幸运的是，我在 [https://sketchfab.com/](https://sketchfab.com/) 找到了可以下载的硅藻结构模型（如下图所示，红色箭头指示的下载图标表明这是一个可以下载的模型），就决定试试这个。

![](/images/week05/w05-d-2.jpg)
> 在 sketchfab.com 上找到了可以下载的硅藻模型
> 

## 项目概述
本项目旨在使用 Bambu Lab A1 Combo 打印机和 PETG Translucent 材料打印微观生物——硅藻Campylodiscus hibernicus 的3D模型。模型从 [Sketchfab](https://sketchfab.com/) 获取，并通过适当缩放和打印设置，展示这种单细胞藻类的复杂硅质壳结构。

![](/images/week05/w05-d-3.jpg)
> 3D 模式查看硅藻模型结构，这是目前减材技术无法直接生成的结构
> 

## 材料与工具
+ 打印对象：硅藻Campylodiscus hibernicus 3D模型 ([Sketchfab链接](https://skfb.ly/oPUOq))
+ 3D建模处理：Bambu Studio
+ 3D打印机：Bambu Lab A1 Combo（0.4mm喷嘴）
+ 打印材料：PETG Translucent（透明）

## 详细工作流程
### 模型准备
#### 模型获取
+ 从[Sketchfab](https://skfb.ly/oPUOq)下载模型，解压后获得"`Diatom - Campylodiscus hibernicus.stl`"文件。
+ 该模型由 David Peterman 创建，适合教育和展示用途。

#### 模型检查与调整
+ 打开 Bambu Studio 软件并导入STL文件。
+ 缩放模型使其最长边为50mm，确保均匀缩放以保持比例，如下图所示。

![](/images/week05/w05-d-4.png)

> 在 Bambu Studio 软件中导入硅藻的 STL 文件，并缩放到最长边 50mm
>

+ 使用自动朝向功能，如下图所示，软件将模型立了起来，如下图所示。

![](/images/week05/w05-d-5.png)

> 使用自动朝向，硅藻模型被立了起来
>

+ 然后使用全局整理（快捷键为 A），调整模型方向，使底部平坦部分朝向打印平台，并将模型置于打印区域的中央，如下图所示。

![](/images/week05/w05-d-6.png)

> 使用全局整理，硅藻模型被放置在打印平台的中央
>

### 打印参数设置
#### 打印机与材料选择
+ 选择打印机型号：Bambu Lab A1 Combo
+ 喷嘴直径：0.4mm
+ 材料类型：PETG Translucent（1.75mm直径）

#### 2.2 打印参数优化
下面是拓竹 PETG Translucent 材料提供的产品参数，掌握这些参数对成功打印至关重要。

##### 物理性能
| 项目 | 数值 |
| --- | --- |
| 密度 | 1.25 g/cm³ |
| 维卡软化温度 | 79 °C |
| 热变形温度 | 74 °C |
| 熔融温度 | 228 °C |
| 熔融指数 | 11.7 ± 1.5 g/10 min |


##### 机械性能
| 项目 | 数值 |
| --- | --- |
| 拉伸强度 | 33 ± 4 MPa |
| 断裂伸长率 | 8.2 ± 1.3 % |
| 弯曲模量 | 1610 ± 130 MPa |
| 弯曲强度 | 68 ± 3 MPa |
| 冲击强度 | 37.4 ± 3.3 kJ/m² |


##### 打印指引
| 项目 | 数据 |
| --- | --- |
| 打印前的干燥条件 | 鼓风型烤箱: 65 °C, 8 h<br>X1 系列打印机热床: 75 - 85 °C, 12 h |
| 适用打印面板 | 工程材料打印面板 / 高温打印面板 / 纹理 PEI 打印面板 |
| 打印面板表面处理 | 涂胶 |
| 打印热床温度 | 65 - 75 °C |
| 喷嘴尺寸 | 0.2 / 0.4 / 0.6 / 0.8 mm |
| 打印喷嘴温度 | 230 - 270 °C |
| 打印速度 | < 220 mm/s |
| 打印、保存环境湿度 | < 20% RH (密封, 加干燥剂) |


根据产品参数我调整了一些打印设置项。

+ 温度设置： 
    - 喷嘴温度：250°C（提高透明度）
    - 床温：70°C（确保粘附）
+ 层高设置： 
    - 首层层高：0.2mm（捕捉细节）

#### 可变层高调整
可变层高的目的是找到提高表面质量与兼顾打印速度的平衡。

+ 进入 Bambu Studio 的"可变层高"功能。
+ 选择"自适应"和"平滑模式"按钮优化层高分布，如下图所示。
+ 查看等高线预览，确保硅藻细微结构能被正确捕捉。

![](/images/week05/w05-d-7.png)

> 使用自适应加平滑模式的可变层高效果
>

### 支撑选择与切片 
Bambu Studio 提供了普通支撑与树状支撑的选择，这里我分别尝试和切片，从下图给出的总预估报告可以看到：

普通支撑耗时 2h49m，树状支撑 2h57m。时间差异并不大，所以我选择树状支撑，据说更适合复杂结构。

![](/images/week05/w05-d-8.png)

> 普通支撑总耗时  2h49m
>

![](/images/week05/w05-d-9.png)

> 树状支撑总耗时  2h57m
>

### 4. 树状支撑 3D打印过程
#### 4.1 打印机准备
+ 确认打印平台清洁并校准。
+ 装载PETG Translucent耗材并确认挤出正常。
+ 预热打印机至设定温度。

拓竹的打印机自动化程度很高，推自动进行各种校准和准备工作。

#### 4.2 打印执行
+ 启动打印任务并监控第一层的附着情况。
+ 观察打印过程中的层间粘合和透明效果形成。
+ 完成打印后等待模型冷却。

模型用了差不多 3 个小时打印完，托盘上有些碎屑和细丝出现，顿觉不妙。

![](/images/week05/w05-d-10.jpg)

> 硅藻 1.0 版打印效果，使用了树形支撑
>

### 移除支撑发现打印失败
把打印件从盘上取下，我尝试用工具移除支撑，发现支撑和结构粘连的非常紧。尤其是在取下中间那些比较纤细的部分时，移除支撑时会把这些结构一起扯下。

![](/images/week05/w05-d-11.jpg)
> 移除支撑时变成大型翻车现场
>

![](/images/week05/w05-d-12.jpg)

#### 我分析导致失败可能有以下原因：
+ 可变层高对于这种纤细的结构并不友好，上面失败的样品上，可以看到明显的层纹痕迹
+ 树形支撑对有很多纤细结构并不友好

###  普通支撑无可变层高再次打印
这次我在 Banbu Studio 里换为了普通支撑并取消了可变层高。打印的时间也少了差不多 1 小时。

![](/images/week05/w05-d-13.png)

> 设置为普通支撑并取消了可变层高
>

这次打印看着比较顺利，托盘也很干净。

![](/images/week05/w05-d-14.jpg)

> 硅藻 2.0 版，普通支撑的打印结果看着还不错
>

下面开始着手拆除支撑，解除支撑依然是件辛苦的事。

![](/images/week05/w05-d-15.jpg)

> 左侧为普通支撑结果，右侧为树形支撑结果
>

这次稍微有进步，硅藻的外缘还不错，但内部纤细且中空的部分，支撑还是有粘连，我还没有想好

![](/images/week05/w05-d-16.jpg)

> 
>

## AI 失败总结与改进建议
对一个 3D 打印新手，这算第 2 次失败，我询问了 Claude，提供了项目的图片和文档，提示词如下：

> 我在尝试打印硅藻结构，最长边50mm 大小，分别尝试了普通支撑和树形支撑，普通支撑稍微好一点，但支撑和中空的细部结构粘连比较严重，请你帮我撰写一个失败总结，并给出改进建议。
>

![](/images/week05/w05-d-17.jpg)

> 向 Claude 求助
>

下面被横线包裹的是 Claude 给出的问题分析和改进建议。

---

### 问题分析
1. **支撑结构粘连问题**：无论是树形支撑还是普通支撑，都与模型的细小内部结构产生了严重粘连，导致在移除支撑时破坏了模型的完整性。
2. **结构精细度无法实现**：硅藻模型内部的纤细结构对于FDM打印机的精度要求很高，尤其是那些放射状的内部支柱和中空部分。
3. **层高设置不当**：可变层高设置可能对这种复杂的微观结构不友好，导致明显的层纹痕迹。
4. **PETG材料特性**：PETG虽然透明度好，但粘性较强，不利于支撑的分离。

### 改进建议
1. **模型修改与分割**
    - 考虑将模型分割成上下两半，水平打印可能减少对支撑的依赖
    - 增加关键纤细结构的厚度，牺牲一些原始细节以提高可打印性
    - 在CAD软件中为模型添加断裂点，便于后期支撑拆除
2. **打印参数优化**
    - 降低喷嘴温度至230-240°C，减少材料过度流动和粘连
    - 增加支撑与模型间的Z轴间隙（0.2mm→0.3mm）
    - 使用更低的层高（0.12mm或0.1mm）来提高细节表现
    - 降低支撑密度（15%-20%）并增加支撑与模型的XY距离
3. **材料与后处理选择**
    - 考虑使用水溶性PVA作为支撑材料（如果打印机支持双挤出）
    - 尝试使用光敏树脂SLA/DLP打印，对微观结构的表现更好
    - 使用PLA可能比PETG更易于支撑分离（虽然透明度会降低）
    - 使用超声波清洗器帮助溶解支撑与模型的粘连处
4. **打印方向调整**
    - 尝试将模型倾斜45°打印，可能减少内部支撑数量
    - 考虑多次小型测试，找到最佳打印方向
5. **其他技术探索**
    - 如有条件，考虑使用树脂打印机(SLA/DLP)打印这类精细结构
    - 可以尝试喷嘴直径更小的打印机（如0.2mm或0.25mm）
    - 使用专业支撑软件（如Meshmixer）生成更智能的支撑结构

### 可行性实验建议
1. 先打印模型的四分之一或八分之一部分进行测试，找到最佳参数
2. 调整模型，加粗关键细微结构至少至0.8mm以上
3. 使用热水或酒精浸泡试验，帮助PETG支撑与模型分离
4. 探索使用可断裂支撑设置，在Bambu Studio中将支撑连接点设置为最小

通过这些改进措施，硅藻的内部纤细结构可能会有更好的打印效果，特别是支撑与模型的分离问题。如果条件允许，建议考虑树脂打印机以获得最佳的微观结构表现。

---

因为手头只有一卷 **PETG 材料，所以我决定尝试调整参数，看看能否改进打印质量。于是我开启了打印机的高级设置，并将每一栏的设置发给 Claude，请 AI 帮我修改参数，如下图所示。**

![](/images/week05/w05-d-18.png)

> 给 Claude 提供目前打印机的参数设置，并请 AI 给出改进建议
>

提示词如下：

> 我给你提供了打印机的质量，强度，速度，支撑，其他这几个设置的截图，告诉我应该怎么调整才能改进硅藻的模型获取质量。
>

下面被横线包裹的是 Claude 给出的问题分析和改进建议。

---

根据你提供的硅藻打印过程和 Bambu Lab A1 打印机设置截图，我来分析一下如何优化设置以改善硅藻模型的打印质量，特别是解决支撑移除困难和细节保留问题。

## 优化打印参数建议
### 层高设置
+ **当前**: 0.12mm (Fine)，首层 0.2mm
+ **建议修改**: 
    - 保持 0.12mm 层高，适合细节打印
    - 关闭可变层高功能，对于这种微观结构更有利于一致性

### 支撑设置
+ **当前**: 普通支撑，接触角度 25°，XY 间距 0.35mm，Z 距离 0.12mm
+ **建议修改**: 
    - **增加 Z 距离**: 从 0.12mm 提高到 0.16-0.2mm，使支撑更容易分离
    - **增加 XY 间距**: 从 0.35mm 增加到 0.45-0.5mm
    - **降低接触角度**: 从 25° 降低到 15-20°，减少支撑数量
    - **支撑密度**: 降低到 10-12%（当前为 15%）
    - **勾选"使用不同的支撑/模型 XY 间距于首层"**，设置为 0.3mm

### 温度和冷却设置
+ **建议修改**: 
    - **降低喷嘴温度**: 对于 PETG Translucent，设置为 240°C（而非当前可能的 250°C）
    - **增加冷却风扇速度**: 对于纤细部分增加至 80-100%
    - **启用桥接设置**: 确保勾选"桥接风扇"选项，提高至 100%

### 打印速度
+ **当前**: 外墙 200mm/s，内墙 350mm/s
+ **建议修改**: 
    - **减慢外墙速度**: 降低到 120-150mm/s
    - **减慢内墙速度**: 降低到 200-250mm/s
    - **减慢支撑速度**: 从 150mm/s 降低到 100mm/s
    - **微小特征速度**: 降低到 30% 或更低

### 稀疏填充设置
+ **当前**: 15% 网格图案，线条间距较大
+ **建议修改**: 
    - **增加填充密度**: 对于硅藻结构，提高到 20-25%
    - **改变填充图案**: 使用"三角形"或"立方体"填充，比网格提供更好的支持

### 其他关键设置
+ **勾选"识别狭窄内部实心填充"**: 已启用，正确
+ **底部/顶部接触面层数**: 增加到 3-4 层，提高表面质量
+ **橦试梯设置**: 宽度维持 35mm，但清理量可提高到 60mm³，确保打印前喷嘴干净

## 硅藻模型特定优化
1. **模型修改建议**:
    - 在打印前，使用3D建模软件（如Meshmixer）手动增加一些断裂点，使支撑更容易移除
    - 将模型分割成上下两部分单独打印，然后粘合
2. **支撑移除技巧**:
    - 打印完成后，将整个模型浸入 40-50°C 的温水中 10-15 分钟
    - 使用精细的镊子和牙签等工具小心移除支撑
3. **替代打印方向**:
    - 尝试将模型旋转 45°，使顶部朝向打印平台
    - 这样可能减少支撑结构与内部精细结构的交互
4. **尝试不同支撑类型**:
    - 你的测试显示普通支撑略优于树形支撑
    - 使用普通支撑，但可以进一步降低支撑接触点大小

这些调整重点是改善支撑与模型的分离性，同时保持足够的支撑强度来确保模型完整性。对于PETG材料，增加支撑与模型的距离以及降低喷嘴温度是解决粘连问题的关键。

---

修改设置后切片，这次时间变成 2h51m（和之前的树形测试时间接近），如下图所示。然后启动打印。

![](/images/week05/w05-d-19.png)

> 修改参数后再次切片
>

经过近 3 个小时的打印，这次效果看上去也不错。

![](/images/week05/w05-d-20.jpg)

> 硅藻 3.0 版，背景能看到擦拭塔
>

为了提高打印质量，勾选了擦拭塔，托盘上会多出一个立方体结构，如下图所示。

> 擦拭塔(Wipe Tower，也称为清理塔或净化塔)在3D打印中有几个重要作用：
>
> 1. **清洁喷嘴** - 打印过程中，喷嘴可能会有残留材料或渗漏。擦拭塔允许打印机在每层开始打印前将喷嘴在塔上擦拭，清除这些残留物。
> 2. **稳定挤出** - 当打印机休息或移动到模型不同部分时，挤出可能变得不稳定。擦拭塔确保在开始打印模型的关键部分前，挤出流量已经稳定。
> 3. **多材料打印辅助** - 对于多材料或多色打印，擦拭塔用于材料切换过程中清理喷嘴，防止颜色混合或交叉污染。
> 4. **减少拉丝** - 通过提供喷嘴移动到模型前的目标点，减少了长距离移动导致的拉丝问题。
>
> 在你的硅藻模型打印中，擦拭塔可以帮助保持PETG材料的一致挤出，尤其是在打印那些需要精细细节的区域前。你在设置中已经启用了擦拭塔，宽度为35mm，清理量为45mm³，这对于复杂模型来说是合理的设置。
>
> 如果你发现喷嘴拉丝严重或者打印细节部分质量不佳，可以考虑稍微增加清理量到60mm³，确保喷嘴在打印关键部分前是完全清洁的。
>

![](/images/week05/w05-d-21.png)

> 勾选擦拭塔，托盘上会增加一个中空的立方体结构
>

实际打印的擦拭塔。

![](/images/week05/w05-d-22.jpg)

> 打印出来的擦拭塔
>

接着就是艰难的去除支撑的过程

![](/images/week05/w05-d-23.jpg)

> 去除支撑的过程依旧艰难，甚至比打印的时间还要长
>

花了几个小时去除了我认为能去除的支撑，但发现里面中空的部分还是无法成功取出，如下图右侧图像。

![](/images/week05/w05-d-24.jpg)

> 硅藻从左到右 1.0-3.0 版，还是没有办法取出中空结构内部的支撑
>

看来对这个结构，要想获得更好的效果，建议使用水溶性PVA作为支撑材料。