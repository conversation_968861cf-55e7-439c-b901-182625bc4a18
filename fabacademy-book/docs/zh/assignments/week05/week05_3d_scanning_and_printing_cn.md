---
layout: doc
title: "第5周：3D扫描与打印 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第五周 3D扫描与打印技术学习指南"
head:
  - - meta
    - name: keywords
      content: fab academy, 3D打印, 3D扫描, 摄影测量, 增材制造, PLA, PETG, 立体光刻
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第4周：基于AI的XIAO MG24 Sense应用开发'
  link: '/zh/assignments/week04/week04_individual_assignment_ai_xiao_mg24_sense_app_cn'
next:
  text: '第5周：小组作业：3D打印机设计规则测试'
  link: '/zh/assignments/week05/week05_group_assignment_3d_printer_test_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---
# 第五周：3D 扫描与打印技术

> 本文档内容由我向 [Grok 3](https://grok.com/chat/) 提供课程大纲链接 [http://academy.cba.mit.edu/classes/scanning_printing/index.html](http://academy.cba.mit.edu/classes/scanning_printing/index.html)，以及本课程视频会议课程的字幕后，由 AI 提炼生成。
>

### 概要：
**关键要点：**  

+ 本讲义基于 Fab Academy 3D 扫描与打印课程，适合初学者，涵盖 3D 打印和扫描的基础知识。  
+ 包括材料选择（如 PLA 和 PETG）、设计规则、打印与扫描技术，以及相关软件和作业。  
+ 令人惊讶的是，3D 打印不仅能制作复杂形状，还能减少浪费，适合嵌套部件制造。

#### 什么是 3D 打印和扫描？
3D 打印（增材制造）是通过逐层添加材料创建三维物体的方法，与传统减材制造（切削材料）不同。3D 扫描则是捕捉真实物体形状并转化为数字数据的过程。  

+ 3D 打印的优势包括处理复杂设计、减少浪费、适合嵌套部件制造。  
+ 扫描技术如摄影测量和激光扫描帮助创建数字模型。

#### 材料与安全
常用材料包括 PLA（易打印但易脆，玻璃化转变温度约 60°C）和 PETG（更坚韧，约 80°C）。  

+ 注意安全：打印机可能释放微粒和挥发性有机化合物，需确保通风；食品接触物需用安全材料。

#### 设计与技术
设计时需考虑支撑结构、壁厚和表面光洁度。打印技术包括 FDM（熔融沉积成型）、SLA（立体光刻）等。扫描技术如摄影测量（用多张照片重建模型）和激光扫描（用激光测量距离）。

#### 软件与作业
使用软件如 MeshLab（网格处理）和 PrusaSlicer（切片）来准备打印。作业包括设计并打印一个无法通过减材制造的物体，以及扫描一个物体（可选择打印）。

---

### 详细报告
本讲义基于 Fab Academy 课程“3D 扫描与打印”的大纲（[http://academy.cba.mit.edu/classes/scanning_printing/index.html](http://academy.cba.mit.edu/classes/scanning_printing/index.html)）和 Neil 教授视频字幕的转录内容，旨在为初学者提供全面且易懂的指导。以下是详细内容，涵盖课程的核心主题和技术细节。

#### 课程背景与概述
Fab Academy 是一门专注于数字制造的课程，其中“3D 扫描与打印”模块探讨增材制造和三维扫描的基本原理、材料、设计规则、工艺流程和应用。课程内容基于 Neil Gershenfeld 的讲授，结合互动讨论和实践作业，强调动手能力和可持续性。

#### 3D 打印的基础知识
3D 打印（增材制造）通过逐层添加材料创建三维物体，与减材制造（从材料块中切削）形成对比。其主要优势包括：  

+ **高复杂性低成本**：可以轻松处理复杂几何形状，传统方法可能成本高昂。  
+ **嵌套部件访问**：允许打印相互嵌套的部件，无需额外组装。  
+ **净形制造**：打印出的物体接近最终形状，减少后处理需求。  
+ **减少浪费**：仅在需要的地方添加材料，相比减材制造更环保。

转录内容中提到，3D 打印的局限性包括分辨率、时间成本和材料限制，但这些对初学者理解整体概念影响较小。

#### 材料选择与安全考虑
3D 打印的材料选择对打印质量和应用至关重要。常见材料包括：  

+ **PLA（聚乳酸）**：源自可再生资源（如玉米淀粉），易于打印，但较脆，玻璃化转变温度约 60°C。  
+ **PETG（聚对苯二甲酸乙二醇酯改性）**：基于石油，易回收，比 PLA 更坚韧，玻璃化转变温度约 80°C，抗紫外线性能更好。  
+ **复合材料**：如金属或木质填充材料，增强强度或改变外观。  
+ **其他聚合物**：ABS、HIPS、TPU、PVA，各有特定属性，适合不同应用。

**安全考虑**：  

+ 打印过程中可能释放超细颗粒和挥发性有机化合物（VOC），需确保良好通风或使用认证打印机。  
+ 对于食品接触物品，需选择符合安全标准的材料，转录中强调了这一点。  
+ 材料储存也需注意，如某些材料吸湿性强（如 PLA），需干燥保存。

#### 设计规则与最佳实践
设计 3D 打印模型时，需遵循以下规则以确保打印成功：  

+ **支撑结构**：悬垂部分可能需要支撑，以防止塌陷。  
+ **无支撑角度**：不同打印机和材料对无支撑角度有不同限制，通常建议小于 45 度。  
+ **壁厚**：壁厚需足够以保证稳定性，但过厚会浪费材料，建议 1-2mm 为初学者起点。  
+ **各向异性**：由于逐层打印，打印件的机械性能沿不同轴可能不同，需在设计时考虑。  
+ **表面光洁度**：层线可能影响表面平滑度，可通过后处理（如打磨）改善。  
+ **填充率**：内部填充结构可调整，以平衡强度和材料使用，初学者可从 20% 填充率开始。

转录中提到设计规则测试是小组作业的一部分，强调实践的重要性。

#### 3D 打印工艺
3D 打印技术多种多样，每种适合不同应用：  

+ **熔融沉积成型（FDM/FFF）**：通过挤出熔融材料逐层堆积，适合初学者和桌面打印机，如 Ultimaker 或 Prusa。  
+ **立体光刻（SLA）**：使用激光固化液态树脂，适合高精度模型，如珠宝设计。  
+ **选择性激光烧结（SLS）**：激光烧结粉末材料，适合功能性部件，无需支撑。  
+ **粘结剂喷射（Binder Jetting）**：在粉床上喷涂粘结剂，形成物体，适合快速原型制作。

转录中提到其他工艺如双光子纳米打印和生物打印，适合高级应用，但初学者可专注于 FDM 和 SLA。

#### 3D 扫描技术
3D 扫描是将真实物体转化为数字模型的过程，常用技术包括：  

+ **摄影测量（Photogrammetry）**：通过多张照片重建 3D 模型，使用软件如 Meshroom 或 Polycam，适合初学者。  
+ **激光扫描**：用激光测量距离，生成点云数据，适合高精度需求，如文物保护。  
+ **结构光扫描**：投影光图案并捕捉变形，适合中等精度应用，如人脸扫描。  
+ **LIDAR（光探测与测距）**：用激光脉冲测量距离，常见于大型环境扫描，如建筑测量。

转录中提到工具如 OpenKinect、Skanect 和 Scaniverse，强调扫描时的光线、背景和表面处理对结果的影响。

#### 软件工具与工作流程
3D 打印和扫描需要多种软件支持：  

+ **网格处理软件**：如 MeshLab、netfabb、meshmixer，用于处理扫描数据或 CAD 模型，转换为 STL 格式。  
+ **切片软件**：如 PrusaSlicer、Cura、Slic3r，将 3D 模型切分为打印层，生成 G-code。  
+ **打印控制软件**：如 Printrun、OctoPrint、Repetier，用于控制打印机运行。  
+ **固件**：如 Klipper，优化打印机性能。  
+ **分享平台**：如 Sketchfab、Thingiverse、Printables，用于分享和查看 3D 模型。

转录中提到这些工具的实际使用，强调初学者应从 PrusaSlicer 和 MeshLab 开始熟悉。

#### 作业与实践
课程包括以下作业，帮助初学者实践所学：  

+ **小组作业**：测试 3D 打印机的设计规则，如支撑角度、壁厚等，记录结果。  
+ **个人作业**：  
    - 设计并 3D 打印一个小物体（几立方厘米，考虑打印时间限制），该物体无法通过减材制造完成，如内部空腔结构。  
    - 3D 扫描一个物体，可选择打印扫描结果，练习摄影测量或激光扫描技术。

转录中提到作业还包括准备 AI 设计和嵌入式处理的讨论，适合扩展学习。

#### 总结与进一步阅读
本讲义为初学者提供了 3D 打印和扫描的全面入门指南，涵盖材料选择、安全设计、工艺流程和实践作业。转录内容显示课程注重互动和实践，Neil 教授强调可持续性和创新应用，如生物打印和增强现实（SLAM）。  

初学者可参考以下资源深入学习：  

+ Fab Academy 课程大纲 ([http://academy.cba.mit.edu/classes/scanning_printing/index.html](http://academy.cba.mit.edu/classes/scanning_printing/index.html))  
+ FabCloud 库存列表 ([http://inventory.fabcloud.io/?purpose=3D%20Scanning%20and%20Printing](http://inventory.fabcloud.io/?purpose=3D%20Scanning%20and%20Printing))  
+ PrusaSlicer 官方页面 ([https://www.prusa3d.com/page/prusaslicer_421](https://www.prusa3d.com/page/prusaslicer_421))  
+ MeshLab 官网 ([http://www.meshlab.net/](http://www.meshlab.net/))

#### 关键引用
+ [Fab Academy 3D 扫描与打印课程大纲](http://academy.cba.mit.edu/classes/scanning_printing/index.html)  
+ [FabCloud 3D 扫描与打印库存列表](http://inventory.fabcloud.io/?purpose=3D%20Scanning%20and%20Printing)  
+ [PrusaSlicer 官方下载与文档](https://www.prusa3d.com/page/prusaslicer_421)  
+ [MeshLab 网格处理软件官网](http://www.meshlab.net/)

