---
layout: doc
title: "第5周：设计3D打印笔筒 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第五周额外作业：设计并3D打印一个笔筒"
head:
  - - meta
    - name: keywords
      content: fab academy, 3D打印, 笔筒, Fusion 360, Bambu Lab A1 Combo, PETG
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第5周：3D打印硅藻结构'
  link: '/zh/assignments/week05/week05_individual_diatom_cn'
next:
  text: '第6周：电子设计与生产'
  link: '/zh/assignments/week06/week06_electronics_design_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
---

# 第5周：设计3D打印笔筒

## 项目概述
本次项目是设计并制作一个镂空螺旋结构的笔筒。在设计过程中，我使用Fusion 360软件创建了参数化的螺旋结构，并通过阵列和布尔运算完成了镂空设计。最终使用 Bambu Lab A1 Combo 3D 打印机完成打印。这个项目展示了3D参数化设计与制造的完整工作流程。

## 材料与工具
+ 设计软件：Autodesk Fusion 360
+ 切片软件：Bambu Studio
+ 3D打印机：Bambu Lab A1 Combo 3D（0.4mm喷嘴）
+ 打印材料：Bambu PETG Translucent（淡蓝色半透明）

## 详细工作流程
### 1. 设计构思
设计目标是创建一个既实用又具有视觉美感的笔筒。我选择螺旋结构作为主要设计元素，因为它能够：

+ 提供足够的结构强度
+ 创造独特的视觉效果
+ 减少打印材料用量
+ 展示3D打印技术的优势

设计参数确定：

+ 笔筒直径：75mm
+ 高度：85mm
+ 壁厚：2mm
+ 螺旋密度：0.25转/高度
+ 镂空元素数量：36个

### 2. Fusion 360建模过程
#### 2.1 创建螺旋结构
1. 打开Fusion 360，创建新设计
2. 点击"创建"菜单中的"螺旋"选项
3. 选择XY平面作为基准平面
4. 点击原点，设置以下参数： 
    - 直径：75mm
    - 转速：0.25
    - 高度：85mm
    - 截面形状：方形
    - 截面尺寸：2mm（内侧）
5. 按回车键完成螺旋结构创建

![](/images/week05/w05-p3-1.png)

> 按照参数创建 1 条方形截面的螺旋实体
>

#### 2.2 镜像复制
1. 点击"创建"菜单中的"镜像"选项
2. 选择螺旋体作为对象
3. 选择螺旋体底部平面作为镜像平面
4. 设置操作为"合并"
5. 确认完成镜像操作

![](/images/week05/w05-p3-2.png)

> 注意镜像平面要选择方形截面
>

#### 2.3 环形阵列
1. 点击"创建"菜单中的"阵列"，选择"环形阵列"
2. 选择螺旋体作为要阵列的对象
3. 选择Z轴作为阵列轴
4. 设置数量为36个
5. 确认完成阵列

![](/images/week05/w05-p3-2-2.png)

> 使用环形阵列功能快速形成整个网格结构
>

#### 2.4 合并螺旋结构
1. 框选所有螺旋体
2. 点击"修改"菜单中的"合并"选项
3. 点击确定，完成合并操作
4. 右键重命名为"螺旋结构"，暂时隐藏

![](/images/week05/w05-p3-3.png)

> 将网格组成部分合并为 1 个实体
>

#### 2.5 创建底座和顶部
1. 点击"创建"菜单中的"圆柱"
2. 选择XY平面，点击原点
3. 设置直径为75mm，高度为-2mm（向下延伸）
4. 使用"修改"菜单中的"移动"工具，选择平移，创建副本
5. 设置Z轴距离为87mm，创建顶部圆盘

![](/images/week05/w05-p3-4.png)

> 使用圆柱功能生成下底并通过移动复制获得顶部圆盘
>

#### 2.6 抽壳处理
1. 选择顶部圆盘
2. 点击"修改"菜单中的"抽壳"选项
3. 分别点击上盖圆盘的顶面和底面
4. 设置内侧厚度为2mm
5. 确认完成抽壳操作

![](/images/week05/w05-p3-5.png)

> 利用抽壳功能将顶部圆盘开口
>

#### 2.7 合并所有组件
1. 显示先前隐藏的螺旋结构
2. 框选所有组件
3. 点击"修改"菜单中的"合并"选项
4. 确认完成所有组件的合并

![](/images/week05/w05-p3-6.png)

> 再次将网格结构和上下结构合并为整体
>
获取设计源文件：[https://a360.co/42Xdxhh](https://a360.co/42Xdxhh)

### 3. 准备打印文件
1. 从 [Fusion 360](https://www.autodesk.com/products/fusion-360/) 导出 STEP 文件
2. 打开 Bambu Studio 软件
3. 导入 STEP 文件
4. 检查模型完整性，确保无错误
5. 设置打印参数： 
    - 喷嘴直径：0.4mm
    - 层高：0.2mm
    - 填充密度：15%
    - 填充图案：网格
    - 支撑：无（设计不需要支撑）
6. 预览切片结果，确认无问题，显示打印需要约 2 个多小时

![](/images/week05/w05-p3-7.png)

> 在 Banbu Studio 中进行切片预览
>
取打印用的 STEP 的 ZIP 文件：[vase.step.zip](/fabacademy-book/docs/public/images/week05/vase.step.zip)

### 4. 3D打印过程
1. 准备打印机： 
    - 确认打印平台清洁
    - 我选择了  PETG Translucent 材料
    - 确认喷嘴温度和平台温度打印机自动设定
2. 启动打印并监控初始层附着情况
3. 打印时间约 2 小时 53 分钟
4. 完成打印后等待平台冷却，移除成品

![](/images/week05/w05-p3-8.jpg)

> 刚刚完成打印的效果
>

### 5. 后处理
1. 轻轻清理底部残留毛刺
2. 检查螺旋结构的连接点和强度
3. 验证笔筒的稳定性和实用性

## 成果展示与分析
### 成果展示
最终完成的镂空螺旋笔筒如下图所示：

![](/images/week05/w05-p3-9.jpg)

### 设计分析
1. **结构强度**：螺旋结构提供了足够的支撑，笔筒整体稳固。
2. **视觉效果**：镂空设计创造了光影变化，增加了视觉趣味性。
3. **材料使用**：镂空设计减少了约65%的材料用量。
4. **打印质量**：层纹较为明显，但不影响整体美观和使用。
5. **实用性**：能够容纳多支笔，功能性良好。

## 总结与反思
### 成功之处
1. 成功运用参数化设计方法创建了复杂螺旋结构
2. 合理应用了阵列和布尔运算简化设计流程
3. 无需支撑结构即可完成打印，减少了材料浪费
4. 成品兼具美观性和实用性

### 改进空间
1. 可考虑增加底部重量，提高稳定性
2. 尝试不同的填充密度和图案，优化强度与材料使用的平衡
3. 可探索更多螺旋参数变化，创造更丰富的视觉效果
4. 考虑使用渐变色或多色PLA打印，提升美观性

### 学习心得
通过本次项目，我深入理解了参数化设计在3D建模中的应用。螺旋结构看似复杂，但通过合理设置参数和运用工具，可以高效完成。这种设计方法不仅简化了复杂结构的创建过程，还便于后期调整和优化。同时，本项目也展示了3D打印技术在制造复杂几何形状方面的优势，传统制造方法难以高效生产类似的镂空结构。

### 关键引用
+ [Fusion 360 螺旋工具使用指南](https://help.autodesk.com/view/fusion360/ENU/?guid=SLD-COIL-SOLID)
