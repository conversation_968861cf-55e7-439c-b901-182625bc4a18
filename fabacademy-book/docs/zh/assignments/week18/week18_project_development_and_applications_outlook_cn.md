---
layout: doc
title: "第18周：项目开发与应用前景展望 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第18周课程，涵盖最终项目规划、项目管理原则，以及回顾已学技能的应用前景。"
head:
  - - meta
    - name: keywords
      content: fab academy, 项目开发, 应用前景, 最终项目, 课程笔记, 第18周
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第17周：通配符周'
  link: '../week17/week17_wildcard_week_cn'
next:
  text: '第19周：发明、知识产权与收入'
  link: '../week19/week19_invention_intellectual_property_income_cn'
---

# 第18周：项目开发与应用前景展望 (Project Development and Applications Outlook)

> 本文档内容由我向 Claude 3.7 Sonnet 提供[课程大纲](http://academy.cba.mit.edu/classes/applications_implications/index.html)内容，以及本课程视频会议课程的字幕后，由 AI 提炼生成。
>

## 课程概要
本周课程标志着从学习独立技能到整合这些技能完成最终项目的转变。核心内容包括最终项目的规划、项目管理的关键原则，以及回顾已学技能能够应用的广阔领域，为最终的“杰作”做准备。学生需要开始具体规划自己的最终项目，并着手准备项目演示所需的材料。

## 详细的课程内容介绍

### 第一部分：应用前景展望 (回顾已学技能的应用领域)
这部分旨在通过展示已学技能在不同领域的应用实例，启发学生的最终项目构思。这些技能不仅仅是独立的，当它们被整合时，就能创造出有影响力的项目。

以下是一些可以应用Fab Lab技能的领域：

+ **医疗 (Medical)**: 例如，在疫情期间，Fab Lab网络在快速原型制作和生产个人防护装备(PPE)、材料测试等方面发挥了重要作用。
+ **电子产品 (Electronics)**: 学生已经学习了大量电子制作，可以将这些技能商业化，例如Modern Device公司就销售其开发的电子产品。
+ **测试设备 (Test Equipment)**: 例如，学生可以制作自己的示波器等测试工具，而不是购买。
+ **消费电子产品 (Consumer Electronics)**: 例如，制作一个完整的音箱 (Boombox)。
+ **电话 (Phone)**: 早期已有Fab Lab项目制作功能手机的案例。
+ **终端 (Terminal)**: 例如，制作一个可以连接到远程服务器的计算机终端。
+ **笔记本电脑 (Laptop)**: 例如，Bunnie Huang制作的开源笔记本电脑，或Nadia Peek设计的便于实验和添加功能的开放架构笔记本电脑。
+ **网络 (Network)**: 例如，FabFi项目，在缺乏电信基础设施的地区（如阿富汗、肯尼亚）利用Fab Lab设备制作抛物面天线，实现远距离（如10公里）无线网络连接，甚至构建城市范围的互联网。
+ **卫星 (Satellite)**: CubeSat（立方体卫星）是一个标准化的纳卫星平台，其许多部件都可以在Fab Lab中制造，使得启动个人太空项目变得越来越可行。Fab 25甚至计划有卫星工作坊。
+ **机器 (Machines)**: 学生们已经完成了机器制造周的作业，核心技能之一就是制造自己的Fab Lab设备。
+ **材料 (Materials)**: 例如Materiom项目，利用咖啡渣、蛋壳、海藻等常见物品，创建开源材料库及配方。
+ **机器人 (Robots)**: 例如Will Langford制作的精美弹跳机器人，或Otherlab利用充气结构制作的软体机器人和辅助性外骨骼。
+ **充气结构 (Inflatables)**: Otherlab利用缝纫制作大型充气机器人，并探索其驱动方式。
+ **船只 (Boats)**: 例如Sam Calisch用复合材料制作的皮划艇。
+ **自行车 (Bicycles)**: 例如Kenny MacCarthy制作的复合材料自行车架。
+ **滑雪板 (Skis)**: 已有多个制作滑雪板的Fab Lab项目。
+ **无人机 (Drones)**: 例如Danielle Ingrassia从头开始制作无人机的各个方面，包括控制器。
+ **汽车 (Cars)**: 巴塞罗那Fab Lab曾有制造Fab Car（电动汽车）的宏伟项目，展示了汽车的快速原型制作能力。
+ **环境 (Environment)**: 例如Smart Citizen项目，从Fab Academy的环境感知项目发展而来，通过公民科学进行区域数据收集，并对城市规划产生影响。
+ **能源 (Energy)**: 例如利用ShopBot制作风力涡轮机进行发电。
+ **农业/食品 (Agriculture/Food)**: 例如Aquapioneers项目（鱼菜共生系统），或AgriLab专注于为智能农业构建工具。Fab Lab也可以制作生物实验室的工具。
+ **生物学 (Biology)**: Fab Lab可以用来制造生物实验所需的许多工具，如移液机器人、PCR仪、离心机等。
+ **显微镜 (Microscope)**: 例如Manu Prakash开发的Foldscope，一种成本仅1美元的折叠显微镜，在全球产生了巨大影响。
+ **科学仪器 (Scientific Instruments)**: 例如OpenFlexure Microscope（开放式屈光显微镜）或开放版本的拉曼光谱仪，旨在普及先进科学仪器的制造。
+ **假肢 (Prosthetics)**: 有关注于Fab Lab制造辅助技术的Fab Care小组和开源假肢的项目。
+ **鞋子 (Shoes)**: 例如通过模具铸造制作高性能运动鞋，鞋底采用软硬材料结合的设计。
+ **服装 (Clothes)**: 例如Iterate项目，一个开源针织机，用于制作自己的服装。
+ **玩具 (Toys)**: 例如Vogue Fab Lab制作的精美互动玩具，强调表面处理和美观度。
+ **艺术 (Art)**: Fab Lab的技术可以与传统工艺结合，例如Haystack Mountain School of Crafts将数字制造融入玻璃、版画等艺术创作中。
+ **乐器 (Musical Instruments)**: 例如Alex แห่ง Vogue制作的一系列精美乐器，如低音吉他。冰岛也制作了许多吉他。
+ **家具 (Furniture)**: 例如以色列一个混合社区的Fab Lab，孩子们设计并制作了自己的家具，激活了社区空间。
+ **房屋 (Houses)**: 最具雄心的项目之一是巴塞罗那Fab Lab的Fab Lab House，一个完整的太阳能房屋，包括内部家具。
+ **实验室 (Labs)**: Danielle Ingrassia的Open Lab Starter Kit项目，提供了一整套开源设计的Fab Lab机器。Fab Lab本身也可以通过自制家具和设备进行升级改造。
+ **社区 (Communities)**: 例如底特律的Blair Evans领导的Fab Lab，在构建实验室周围社区方面发挥了领导作用，将实验室作为社区转型的引擎。
+ **城市 (Cities)**: Fab City倡议，源于巴塞罗那，旨在推动城市利用Fab Lab实现自给自足，生产其所消费的产品，从智能城市迈向能制造的城市。
+ **经济 (Economies)**: Fab Lab通过将生产资料本地化，从根本上改变经济运作方式，影响人们生活、学习、工作和娱乐的方式。

### 第二部分：最终项目规划与实施
最终项目是展示学生对课程中所学技能掌握程度的“杰作”。

#### 1. “杰作”的真正含义 (The True Meaning of a "Masterpiece")
“杰作”(Masterpiece)一词最初并非指伟大的艺术品，而是指手工业行会的成员为证明其掌握了行会技能而创作的作品。因此，Fab Academy的最终项目并非要求是毕生之作或惊世骇俗的发明，而是要清晰地展示学生对课程涵盖技能的综合运用和熟练掌握。它的核心在于整合技能。

#### 2. 规划最终项目的关键问题 (Key Questions for Planning Your Final Project)
学生需要为自己的最终项目创建一个专门的网站，并开始回答以下问题：

+ **它将做什么？ (What will it do?)** 明确项目的功能。
+ **谁事先做过什么？ (Who's done what beforehand?)** 进行背景调研，了解相关工作。
+ **你将使用什么资源？ (What sources will you use?)** 列出项目将依赖的文献、代码库、设计等。
+ **你将设计什么？ (What will you design?)** 明确个人原创设计的部分。
+ **将使用什么材料和组件？ (What materials and components will be used?)**
+ **它们从哪里来？ (Where will they come from?)** 材料和组件的来源。
+ **它们将花费多少？ (How much will they cost?)** 估算成本。
+ **将制作哪些零件和系统？ (What parts and systems will be made?)**
+ **将使用哪些流程？ (What processes will be used?)** 涉及的制造工艺。
+ **需要回答哪些问题？ (What questions need to be answered?)** 项目探索和解决的关键问题。
+ **如何评估？ (How will it be evaluated?)** 制定项目成功的标准。
+ **完成时间表 (Schedule for completion)** 需要制定详细的完成进度计划。

#### 3. 最终项目的核心要求 (Core Requirements for the Final Project)
最终项目必须体现以下技能的综合运用：

+ **二维和三维设计 (2D and 3D design)**。
+ **增材和减材制造工艺 (Additive and subtractive fabrication processes)**：这不仅仅指铣削电路板和3D打印。
+ **电子设计和生产 (Electronics design and production)**。
+ **嵌入式微控制器设计、接口和编程 (Embedded microcontroller design, interfacing, and programming)**。
+ **系统集成和封装 (System integration and packaging)**。
+ **尽可能制作而非购买 (Make rather than buy)** 项目中的零件。
+ 项目可以是个人或合作完成，但必须展示个人对技能的掌握，并且能够独立运作。

### 第三部分：项目管理要素 (Elements of Project Management)
在项目开发的最后冲刺阶段，以下项目管理原则至关重要：

+ **墨菲定律 (Murphy's Law)**：不是指“事情会出错，所以不是我的错”，而是指“任何可能出错的事情都将会出错，因此你需要预见并阻止它”。它不是借口，而是要求有预见性。
+ **80/20法则 (80/20 Principle)**，或者更像是95/5法则：20%的工作占据80%的时间（或者5%的工作占据95%的时间）。那些你认为在最后很简单的事情实际上会花费大部分时间。
+ **检伤分类 (Triage)**：你需要判断哪些部分能够完成，哪些不能，并果断放弃无法完成的部分。不要担心简单的事情，集中精力处理需要关注的部分。
+ **开发过程中的文档记录 (Documentation during development)**：你没有时间在最后补文档，唯一完成的方式是边做边记录。
+ **需求侧与供给侧时间管理 (Demand- vs. supply-side time management)**：不能指望通过完成任务来结束项目，必须规划好你的时间供给，提前安排如何使用时间，并制定详细的时间表。
+ **螺旋式开发 (Spiral development, DevOps)**：不要试图一次完成所有事情，而是先完成项目的一个小版本，然后再添加功能，不断迭代回到一个完成的项目版本。
+ **串行与并行任务 (Serial vs. parallel tasks)**：不要让所有任务串行进行，因为一个任务的阻塞会影响所有后续任务，尽量让任务并行推进。
+ **系统集成 (System integration)**：不要忘记你需要设计系统的集成方案。
+ **完成质量 (Finish quality)**：最终项目应该超越粗糙的激光切割盒子，展示良好的表面处理、设计人体工程学等。使用相同的工具，你可以制作出粗糙或精美的物品。

## 本周作业要求
本周的作业要求学生开始为最终项目奠定坚实的基础：

1. **最终项目站点与规划初稿**：
    - 建立你的最终项目页面。
    - 在该页面上初步回答“第二部分：最终项目规划与实施”中列出的所有关键问题。
2. **演示材料初稿**：
    - 准备最终项目**摘要幻灯片 (presentation.png)** 的初稿。
        * 格式：PNG图片 (presentation.png)。
        * 尺寸：1920x1080像素。
        * 内容：清晰展示你是谁，你的项目是什么，作为一个视觉缩略图。
    - 准备最终项目**视频片段 (presentation.mp4)** 的初稿。
        * 格式：MP4，使用HTML5兼容的编码 (如 H.264，避免 H.265)。
        * 分辨率：1080p。
        * 时长：大约1分钟（初稿可以很短，几秒钟也行，主要是为了测试流程）。
        * 大小：最终视频约 < 25MB (大致10MB/分钟)。
3. **文件放置与链接检查**：
    - 将 `presentation.png` 和 `presentation.mp4` 文件放在你学生仓库的根目录下 (即包含你主页 `index.html` 的目录)。
    - 检查这些文件是否已正确链接到最终演示日程表中（通常会自动更新，但需确认）。
    - **重要提示**：本周提交的幻灯片和视频是初稿/占位符，目的是确保流程通畅，之后可以随时更新替换为最终版本。

## 资源与往期项目回顾
+ **往期项目示例**:
    - 大量的最终项目案例可以在 [Project Development 课程页面](Project Development.html) 找到。
    - 在本次课程的录播中也展示和讨论了许多往年的优秀最终项目，可以作为灵感来源和参考。
+ **最终演示日程表**: 检查你的幻灯片和视频链接是否出现在 [最终演示日程表](http://fabacademy.org/2024/schedule.html) (链接为示例，请查找当年的正确链接)。

请充分利用周六的开放时间和周一的辅导时间，积极推进项目，并为最终的演示做好准备。
