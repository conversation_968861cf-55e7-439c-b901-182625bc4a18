---
layout: doc
title: "第3周个人作业：激光切割制作灯笼外壳 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第三周 激光切割灯笼外壳项目指南"
head:
  - - meta
    - name: keywords
      content: fab academy, 激光切割, 灯笼外壳, 参数化设计, 组装结构
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第3周：团队切割实验'
  link: '/zh/assignments/week03/week03_group_cutting_cn'
next:
  text: '第3周：乙烯基切割'
  link: '/zh/assignments/week03/week03_vinyl_cutting_assignment_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

# 个人作业：激光切割制作灯笼外壳

## 参数化设计与激光切割卡扣组件
参数化设计与激光切割卡扣组件的部分，我考虑设计并制作了两款具有中国传统文化元素的卡扣组件。这些组件采用参数化设计方法，充分体现了激光切割在创建精确卡扣结构中的优势。

## 设计理念
我选择了两个具有中国传统文化特色的元素作为卡扣组件的设计灵感：

1. **中国古代铜钱**：外圆内方的铜钱形状，具有丰富的文化底蕴和象征意义
2. **中国传统福字**：作为吉祥图案，在中国文化中广泛使用

这些设计不仅具有功能性（作为卡扣组件），还融入了文化元素，使作品更具个性和文化内涵。

![](/images/week03/w03-p2-1.jpg)

> 找了 2 个参考图案，一个是中国古代的铜钱，外圆内方；还有一个是中国古代的“福”字纹样
>

## 参数化设计过程
### 铜钱元素设计
根据小组作业的结论，激光切割机的误差范围是 `1.1741mm`。我以为我用于切割的木板也是 2.76mm 厚（事实证明，最好每拿到一块新板最好都测量一下其厚度）。

我使用 Autodesk Fusion 360 进行参数化设计，创建了基于古代铜钱形状的卡扣组件。铜钱的特点是"外圆内方"，我在设计中保留了这一特征：

+ 外圆直径：28mm  （和大部分铜钱的尺寸相当）
+ 内方正方形边长：10mm
+ 卡扣凹槽高度：3mm
+ 我估算卡扣凹槽宽度：<font style="color:rgb(28, 30, 33);background-color:rgb(246, 247, 248);">2.76 - 1.1741 / 2 = 2.17mm</font>

通过参数化设计，可以方便地调整这些尺寸，使组件能够适应不同的使用需求。

![](/images/week03/w03-p2-2.png)

> Fusion  中的铜钱参数化设计，右侧面板显示了相关参数设置
>

完成草图设计后，利用挤出和阵列，模拟了铜钱状卡扣连接组合的效果图。然后将草图导出为 DXF，如下图所示。

![](/images/week03/w03-p2-3.png)

> 将铜钱草图挤出为实体，并通过移动、复制和阵列模拟铜钱状卡扣连接组合的效果图，然后将草图导出为 DXF
>

### 福字元素设计
除了铜钱元素，我还设计了一款基于中国传统"福"字的卡扣组件。福字设计采用了简化的艺术形式，保留其识别性的同时使其适合激光切割工艺。这个图案设计的比较大，外直径 98mm，卡扣凹槽宽度：2.17mm，深度 5mm 如下图所示。

![](/images/week03/w03-p2-4.png)

![](/images/week03/w03-p2-5.png)

> Fusion 360 中的福字元素参数化设计
>

将福字草图挤出为实体，并通过移动、复制和阵列模拟福字卡扣连接组合的效果图，然后将草图导出为 DXF，如下图所示。

![](/images/week03/w03-p2-6.png)

> 将福字草图挤出为实体，并通过移动、复制和阵列模拟福字卡扣连接组合的效果图，然后将草图导出为 DXF
>

## 福字文件准备与优化
完成参数化设计后，我分别将草图导出为 DXF 格式。然后将 DXF 文件上传到激光切割机连接的电脑，并用其配套的软件“激光雕刻切割控制系统”导入 DXF 文件，然后使用 工具/阵列复制功能，x 个数设置为 4，y 个数设置为 2。确定后得到了一个 4x2 的阵列，如下图所示。

![](/images/week03/w03-p2-7.png)

> 使用 工具/阵列复制功能 获得 4x2 阵列
>

## 福字激光切割实施
然后使用“加载”功能，将福字阵列发送到激光切割机（大族粤铭速切 960B 激光切割机），进行了切割

![](/images/week03/w03-p2-8.jpg)

## 福字组装与测试
切割完成后，我测试了卡扣组件的组装效果。接口间隙感觉有点小，组件之间虽然能够卡扣，但需要用较大力气按压，会过紧导致组装困难。

![](/images/week03/w03-p2-9.jpg)

> 组装完成的铜钱元素卡扣组件
>

测量发现卡口宽度设计为 2.17，切割后实际为 2.5mm，偏小（测量实际板厚约 3.05mm）。

![](/images/week03/w03-p2-10.jpg)

> 卡扣切割的宽度测量为 2.5mm，小于 3mm 板厚
>

## 修正铜钱卡扣设计
由于卡扣间隙过小，我重新设计了铜钱的卡扣尺寸，由原来的 2.17mm 调整为 2.8mm。如下图所示。

![](/images/week03/w03-p2-11.png)

> 将铜钱的卡扣宽度设置为 2.8mm
>

这次我分别尝试切割了 2 组 2 个铜钱（1 组 2.17mm 宽，1 组 2.8mm 宽），以比较测试卡扣宽度是否合适。

![](/images/week03/w03-p2-12.jpg)

> 2.8mm 卡口宽度测试切割
>

安装发现 2.8mm 宽度正合适，松紧程度正好，稍微用点力气，可以很紧实的组合。下图左边是 2.17mm 的测试件，因为卡口太窄，无法完全嵌入。右图是 2.8mm 宽度的卡口，能够完全嵌入咬合。

![](/images/week03/w03-p2-13.jpg)

> 对 2.17mm 宽的铜钱卡扣组件（左）和 2.8mm 宽的（右）进行组装测试，2.8mm 的正合适，能够完全咬合
>

于是我使用 工具/阵列复制 功能，复制了 20 个并切割，如下图所示。

![](/images/week03/w03-p2-14.jpg)

> 复制 20 个并切割
>

这次组装非常成功，结构紧实坚固，安装力度也很合适。

![](/images/week03/w03-p2-15.jpg)

> 成功组装好的铜钱卡扣组件
>

## 设计反思
通过这个补充项目，我进一步巩固了参数化设计和激光切割的技能，特别是在以下几个方面：

1. **文化元素融入**：将中国传统文化元素融入现代数字制造技术
2. **参数优化**：通过测试和调整，找到最适合的接口参数
3. **效率提升**：利用切割软件的 工具/阵列复制 功能提高工作效率

## 铜钱卡扣设计文件分享
铜钱的 Fusion 设计文件：[https://a360.co/4j0Jw78](https://a360.co/4j0Jw78)

铜钱卡扣的 DXF 文件：[tq-2.dxf.zip](/images/week03/tq-2.dxf.zip)

## 参数化设计灯笼外壳
在第 2 周的作业：[3D 建模智幻灯笼](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/zh/assignments/week02/week02_3d_modeling_cn)，我已经尝试了用 Autodesk Fusion 软件为方便激光切割为灯笼的外壳建模，并将草图输出为激光切割机所需的 [DXF 文件](https://fabacademy.org/2025/labs/chaihuo/students/lei-feng/images/week02/RL-1-line.dxf))，如下图所示。

![](/images/week03/w03-p-1.jpg)

> 为激光切割建模，导出草图为 DXF 格式文件
>

### 选择切割材料
2025 年 2 月 9 日，之前的作业都是在电脑上完成，今天终于开始在设备上进行了。这次的作业，我们用了柴火创客空间的大族粤铭速切 960B 激光切割机，如下图所示。

![](/images/week03/w03-p-2.jpg)

> 柴火创客空间的大族粤铭速切 960B 激光切割机
>

本来我想用 3mm 厚的木板来切割，但因为周日在柴火创客空间，管理员不在，找不到大块的木板板材。找了半天，只找到整包的硬灰纸板，如下图所示。和木板相比，这个硬纸板要的强度要低很多，但我还是想试试看，测试一下这个在 CAD 里描绘的灯笼结构，大小能否符合我的想象。

![](/images/week03/w03-p-3.jpg)

### 榫口测试
在开始切割大块板材（切割区域大概是 38X50 cm）前，我决定先制作一个小块的榫口测试文件，这次使用了 [LaserMaker](https://www.lasermaker.com.cn) 来绘制这个小测试图。这个孔是为 3mm 板设计的，我将孔的高度设置为 3.2mm。

![](/images/week03/w03-p-4.png)

用之前做测试尺的参数（90%最大功率），用剩余的木板先切了这个榫口测试材料，成品如下图所示。感觉效果还不错。孔略大了点。我想如果下次正式切割 3mm 木板的话，我会把孔的高度调整为 3mm 整，应该会让榫口结合的更紧实一些。

![](/images/week03/w03-p-5.jpg)

然后我换了硬纸板，考虑到是纸板，我把最大功率调到了 35%，结果发现切不透。后来调整到 70%，才成功完全切割，如下图所。

![](/images/week03/w03-p-6.jpg)

> 右侧最大功率设置为 35%，没有切透，调整到 70%OK 了。
>

打印完成后，发现纸板的厚度不足 3mm，大概只有 1.5mm，3.2mm 的孔留出了很大间隙，如下图所示。

![](/images/week03/w03-p-7.jpg)

> 纸板的厚度应该只有 1mm 多，所以孔显得间隙巨大
>

### 切割灯笼外壳
因为是测试性打印，所以我没有修改参数，将之前设计好的 DXF 文件，直接导入激光切割机，使用 70%最大功率的设置开始切割。

![](/images/week03/w03-p-8.jpg)

> 激光切割机面板展示了要切割的文件和切割参数
>

切割完成啦！

![](/images/week03/w03-p-9.jpg)

> 灯笼结构零件刚刚完成切割的样子
>

### 组装灯笼外壳
由于纸板比较软，且榫口过大，我用了热熔胶枪进行固定。

![](/images/week03/w03-p-10.jpg)

> 用热熔胶枪固定连接部分
>

虽然没有木板那么结实，但这个 0.1 版的纸板结构实物，已经能让我充分评估灯笼结构的大小了。

![](/images/week03/w03-p-11.jpg)

> 纸板灯笼外壳成品
>

## 使用木版进行切割
4 月 6 日，采购到木版后，我使用了之前的参数进行切割，如下图所示，开始一切都很顺利。

![](/images/week03/w03-p3-1.jpg)

![](/images/week03/w03-p3-2.jpg)

> 使用之前的参数完成了切割
>

组装的时候发现有了新问题，外框的宽度我设计的为 68mm，缺少足够的间隙，会让最后一块无法压入——感觉就差 1mm。

![](/images/week03/w03-p3-3.jpg)

> 68mm 的外框宽度过大，导致六边形外框无法压入最后一边
>

我重新调整了外框参数，设置为 67mm，现在能感觉到参数化设计的好处。

![](/images/week03/w03-p3-4.png)

> 修改外框宽度为 67mm
>

再次输出 DXF 并切割，如下图所示。

![](/images/week03/w03-p3-5.jpg)

> 切割 67mm 宽度的侧栏
>

这次组装非常顺利，感觉严丝合缝，虽然已经比较紧实了，但我还是用热熔胶枪把结合的地方固定了一下。


![](/images/week03/w03-p3-6.jpg)

![](/images/week03/w03-p3-7.jpg)

> 组装好的灯笼外壳感觉很坚固的样子。
>

## 灯笼外壳设计文件分享
灯笼外壳设计文件：[https://a360.co/3XLW0a9](https://a360.co/3XLW0a9)

灯笼外壳的 DXF 文件：[revolving-lantern-dxf.zip](/images/week03/revolving-lantern-dxf.zip)
