---
layout: doc
title: "第15周：接口与应用程序编程 | 冯磊 Fab Academy 2025"
description: "Fab Academy 2025 第15周接口与应用程序编程课程，介绍软件界面构建、程序编写、数据可视化、输入输出设备接口等内容。"
head:
  - - meta
    - name: keywords
      content: fab academy, 接口, 应用程序, 软件界面, 数据可视化, 输入设备, 输出设备, 个人作业
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '第14周个人作业：模具制作与铸造'
  link: '/zh/assignments/week14/week14_molding_and_casting_personal_contribution_cn'
next:
  text: '第15周个人作业：接口与应用程序编程'
  link: '/zh/assignments/week15/week15_interface_application_programming_personal_cn'
lastUpdated: true
editLink:
---
# 第 15 周：接口与应用程序编程
> 本文档内容由我向 Claude 3.7 Sonnet 提供[课程大纲](https://academy.cba.mit.edu/classes/interface_application_programming/index.html)内容，以及本课程视频会议课程的字幕后，由 AI 提炼生成。
>

## 课程概要
本课程主要介绍如何构建软件界面，编写程序，制作图形、声音、视频，进行数学运算，提高性能以及引入机器学习和部署应用程序。课程的核心目标是构建用户与输入/输出设备之间的接口，实现数据可视化，以及从输入设备发送消息到输出设备。本课程将探讨多种编程语言、用户界面框架、图形渲染技术、声音处理、数据可视化、性能优化以及机器学习的基础应用。

## 详细的课程内容介绍
### 1. 选择编程语言
编程语言是构建接口的基础，不同的编程语言适合不同的应用场景：

#### 1.1 传统编程语言
+ **C语言**：介于低级和高级之间的经典语言
+ **C++**：C的扩展，支持面向对象编程，Arduino IDE就使用了C++
+ **.NET和C#**：来自微软，基于C的更现代框架
+ **Go和Rust**：更加现代的语言，解决了C的安全性和稳定性问题
    - Rust在嵌入式应用中越来越受欢迎，高性能且更加可靠

#### 1.2 函数式和脚本语言
+ **函数式语言**：Haskell, Lisp等，通过评估函数而非描述步骤来编程
+ **脚本语言**：Bash, Perl等，适用于编写简单脚本
+ **Python**：设计优美，文档完善，跨平台免费使用
    - 结合Conda可以为不同环境（嵌入式编程、Web开发、机器学习）设置不同的工具包
    - 现在几乎所有的机器学习都是用Python实现的

#### 1.3 Web和交互式编程语言
+ **Processing**：Arduino的灵感来源，适用于桌面环境的图形和声音处理
    - 也有JavaScript版本：P5.js
+ **JavaScript**：与Java无关，最初是为早期浏览器设计的脚本语言
    - 由于网络的商业重要性，已经得到大量投资
    - 现代JavaScript引擎实现了即时编译，性能可以媲美C语言
    - Node.js是一个可以在浏览器外运行的JavaScript版本

#### 1.4 低代码/无代码环境
如果不喜欢编写代码，可以使用可视化数据流环境：

+ **LabView**：用于实验室自动化
+ **Simulink**：用于仿真
+ **Max**：用于音乐
+ **Scratch**：基于积木的儿童编程
+ **App Inventor**：基于积木框架制作应用
+ **Node-RED**：用于事件接口的数据流框架
+ **Mods**：另一种数据流框架

### 2. 设备接口方式
要与设备进行通信，需要选择合适的接口方式：

#### 2.1 串行通信 (RS-232)
+ **Python**：使用PySerial库
+ **Node.js**：内置串行端口支持
+ **Web浏览器**：Chrome系列浏览器支持WebSerial API
    - 注意：Firefox和Apple浏览器出于安全考虑不支持

#### 2.2 其他通信协议
+ **I2C**：Python和Node有接口
+ **USB**：可以使用更高级的协议而非仅串行通信
+ **MQTT**：用于管理多设备生态系统
    - 基于发布/订阅模型，有一个中心化的"代理(broker)"
    - 设备可以向代理发布结果，其他设备可以订阅这些结果

### 3. 数据组织
当从多个传感器或来源收集数据时，需要有效组织数据：

+ **JSON**：以文本形式描述对象
+ **TOML**：一种标记语言
+ **数据库**：当数据量超过计算机内存时使用
    - SQLite, MariaDB, MongoDB等

### 4. 用户界面构建
用户界面是用户与设备交互的媒介：

#### 4.1 文本界面
+ **终端ANSI代码**：最简单的方式
+ **Curses**：文本界面的编程接口

#### 4.2 图形界面框架
+ **Tk**：跨平台外观一致的小部件集
+ **Wx**：设计为在每台计算机上看起来原生的界面
+ **Qt, GTK, Clutter, QV**：其他常用的小部件集

#### 4.3 Web界面
+ **HTML表单**：网页标准的早期部分，可用于构建用户界面
+ **jQuery**：最初为解决JavaScript限制而编写，现在功能更加丰富
+ **其他JavaScript框架**：如React, Vue, Angular等

#### 4.4 跨平台框架
+ 为不同的设备尺寸和操作系统设计应用程序
+ 编写一次，导出到任何类型的设备和语言

### 5. 图形编程
图形是数据可视化和用户交互的重要方式：

#### 5.1 JavaScript图形
+ **Canvas**：像素级绘图，类似于GIMP
    - 适用于直接绘制线条、图像
+ **SVG**：与Canvas不同，使用分辨率无关的图形元素
    - 图形元素具有身份，可以与之交互
    - 适合移动屏幕上的对象
+ **WebGL**：用于加速3D图形的标准
    - 通常通过Three.js使用
    - Three.js是一个场景描述语言，更高级别

#### 5.2 其他图形选项
+ **PyGFX**：Python中的3D环境
+ **Taichi**：Python框架，支持图形和加速
+ **游戏引擎**：如Blender，可通过Python脚本编程

### 6. 音频和视频处理
多媒体处理是现代界面的重要组成部分：

+ **SDL**：多媒体框架
+ **PyGame**：基于SDL的Python包装器
+ **OpenFrameworks**：多媒体框架
+ **HTML5**：为Web带来音频和视频扩展
    - 通过AudioContext可以在JavaScript中生成和处理声音
    - 可以访问相机，进行视频处理

### 7. 虚拟现实和增强现实
+ **VR**：虚拟现实，通过护目镜插入感官
+ **AR**：增强现实，在环境中添加虚拟元素
+ **Three.js**：可以将3D环境转换为VR或AR体验

### 8. 数学和数据可视化
数据分析和可视化对理解数据至关重要：

+ **NumPy**：扩展Python使其能够处理数学数据对象
+ **Matplotlib**：用于制作图表的框架
    - 可以创建静态或动态更新的图表
+ **Jupyter Notebook**：创建包含代码、结果和描述的交互式笔记本
+ **Plotly**：JavaScript和Python的开源绘图框架
+ **D3.js**：用于创建复杂和交互式数据可视化

### 9. 性能优化
当需要处理大量数据时，性能成为关键：

+ **NumPy**：比纯Python快10倍
+ **Numba**：编译Python代码提高速度
+ **Jax**：加速Python并支持计算导数
+ **JavaScript Workers**：在不同核心上并行运行代码
+ **Rust并行库**：如Rayon，自动并行化代码
+ **MPI, CUDA**：用于大型计算机和图形硬件加速

### 10. 机器学习入门
机器学习可以从数据中学习模式：

+ **基本概念**：网络架构、层、节点、函数
+ **训练过程**：定义错误度量、训练策略、反向传播
+ **框架**：TensorFlow, PyTorch
+ **部署**：Hugging Face, 云服务商API

### 11. 部署和安全性
将应用部署到生产环境并保证安全：

+ **云服务**：AWS, Google Cloud, Azure
+ **安全性考虑**：依赖项是历史性黑客攻击的来源
    - 保持依赖项的更新至关重要

## 作业要求
本周作业的目标是构建一个软件界面，用于与您制作的输入或输出设备进行交互。具体要求：

1. 选择一种编程语言或框架来构建界面
2. 实现与您之前制作的设备之间的通信
3. 构建用户界面，可视化输入设备的数据或向输出设备发送消息
4. 记录整个过程，包括：
    - 所选编程语言/框架和理由
    - 接口实现的详细说明
    - 代码示例和说明
    - 功能演示
    - 遇到的问题和解决方法

注意：

+ 如果您在周度作业上落后，可以利用最终项目来完成周度作业
+ 但您仍需在文档中展示如何在最终项目中使用相关技能
+ 时间管理至关重要，课程只剩下四周时间

## 学习资源
### 编程语言
+ C/C++: [https://www.cplusplus.com/](https://www.cplusplus.com/)
+ Python: [https://www.python.org/](https://www.python.org/)
+ JavaScript: [https://developer.mozilla.org/zh-CN/docs/Web/JavaScript](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript)
+ Processing: [https://processing.org/](https://processing.org/)

### 设备通信
+ PySerial: [https://pythonhosted.org/pyserial/](https://pythonhosted.org/pyserial/)
+ MQTT: [https://mqtt.org/](https://mqtt.org/)
+ WebSerial API: [https://developer.mozilla.org/en-US/docs/Web/API/Web_Serial_API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Serial_API)

### 图形和用户界面
+ Tkinter: [https://docs.python.org/zh-cn/3/library/tkinter.html](https://docs.python.org/zh-cn/3/library/tkinter.html)
+ HTML表单: [https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/form](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/form)
+ Three.js: [https://threejs.org/](https://threejs.org/)
+ D3.js: [https://d3js.org/](https://d3js.org/)

### 数据可视化
+ NumPy: [https://numpy.org/](https://numpy.org/)
+ Matplotlib: [https://matplotlib.org/](https://matplotlib.org/)
+ Plotly: [https://plotly.com/](https://plotly.com/)
+ Jupyter Notebook: [https://jupyter.org/](https://jupyter.org/)

### 机器学习
+ TensorFlow: [https://www.tensorflow.org/](https://www.tensorflow.org/)
+ PyTorch: [https://pytorch.org/](https://pytorch.org/)
+ Hugging Face: [https://huggingface.co/](https://huggingface.co/)

### 视频教程
+ Fab Academy课程视频: [https://vimeo.com/1080670727](https://vimeo.com/1080670727)

### 低代码/无代码选项
+ Node-RED: [https://nodered.org/](https://nodered.org/)
+ Mods: [https://academy.cba.mit.edu/classes/interface_application_programming/WebSockets/hello.ws-blink.mods.html](https://academy.cba.mit.edu/classes/interface_application_programming/WebSockets/hello.ws-blink.mods.html)
