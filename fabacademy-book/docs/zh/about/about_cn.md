---
layout: doc
title: 关于我 | 冯磊 Fab Academy 2025
description: 冯磊参与 Fab Academy 2025 的学习背景和经历
head:
  - - meta
    - name: keywords
      content: fab academy, 数字制造, 创客文化, seeed studio, 柴火创客空间
  - - meta
    - name: author
      content: 冯磊
aside: true
outline: deep
prev:
  text: '首页'
  link: '/zh/'
next:
  text: '第一周：项目管理'
  link: '/zh/assignments/week01/week01_project_management_cn'
lastUpdated: true
editLink:
  pattern: 'https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng/edit/main/docs/:path'
  text: '在 GitLab 上编辑此页面'
---

## 关于我

Hi，我是冯磊。在这个被代码和电路主宰的时代，我在深圳 [Seeed Studio](https://www.seeedstudio.com/) 工作了六年，现在负责这里的用户体验小组。说起来，用户体验这件事，就像是在数字世界中寻找那些最自然的路径。

### 为什么会参加这个课程

[Fab Academy](http://fabacademy.org/) 在 Seeed Studio 是个赫赫有名的存在。作为一家开源硬件的生产商，开源精神深深刻写在公司的基因里。而它的子公司[柴火创客空间](https://chaihuo.org/)——也是我这次上课的 Fab Lab 节点——可以说是中国创客文化的播火者。

2021 年的时候，我协助柴火创客空间组织编写了中文版开源读物《创客空间搭建及运营指南》。这是一次探索创客文明的旅程，我们希望通过这份指南，帮助中国的学校和组织系统地了解创客文化的渊源，为他们建设和运营创客空间提供切实可行的建议。在编写过程中，我像个考古学家一样，展开了广泛的阅读和调查研究，一路追溯到了 [Dale Dougherty](https://en.wikipedia.org/wiki/Dale_Dougherty) 2005 年创办的第一期 [Make:](https://makezine.com/) 杂志。有趣的是，翻开没几页，就遇见了"Welcome to the Fab Lab >>"这篇文章。Neil 的形象跃然纸上，充满激情的面容占据了整整一个版面。就这样，我开始了解 Fab Lab 和 Neil，随着指南的完成交付，这份了解逐渐转化为由衷的敬意。

![](/images/about/makerspace-setup-operation-guide-cover.png)

> 柴火创客空间组织编写的中文版开源读物《创客空间搭建及运营指南》

![](/images/about/make-volume-01.jpg)

> Make: 杂志的创刊号封面
> 图片来源：[https://www.makershed.com/products/make-volume-01-pdf](https://www.makershed.com/products/make-volume-01-pdf)

![](/images/about/welcome-to-the-fab-lab.jpg)

> Make: 杂志第一期中的 Welcome to the Fab Lab >> 开篇页面

而 Seeed Studio 有多位同事都陆续从 [Fab Academy](http://fabacademy.org/) 毕业，每次听他们谈起这段经历，都带着赞叹（尽管常伴随着对熬夜赶工的"痛苦"回忆）。2024 年底，这些故事最终给了我勇气，让我报名准备迎接这次学习挑战。我希望通过这个课程，能在创客知识教育领域探索更多可能性。

开课后的第一印象让我颇为感动——Neil 虽已满头白发，课程虽只能通过视频观看，但他的神情依然如同 20 年前 Make: 杂志上那样矍铄，执着而坚定......

### 我之所长

我的职业旅程像一场探索：从网站与杂志的编辑、网站交互设计师、互联网产品经理到游戏策划，最后在 2018 年加入了 Seeed Studio，在柴火创客教育负责课程设计。正是这份工作把我带入了技术写作的领域，并在其中找到了意外的乐趣。2021 年 2 月，我的第一本书《做游戏 玩编程——零基础开发微软 Arcade 掌机游戏》由清华大学出版社出版。从那以后，我保持着每年一本书的节奏，就像按时收获的农夫。

写这些书的过程，对我来说也是一次次学习的旅程。面对新技术时，我会先努力理解它，然后用我认为更容易理解的方式重新诠释。多亏了之前做网站和游戏的经历，让我熟悉了各种知识表达的工具。为了让书籍呈现出更好的效果，我甚至自学了 Adobe InDesign 来做排版。以下是已出版的书籍列表：

| ![](/images/about/book-arcade.webp)  | [《做游戏 玩编程——零基础开发微软 Arcade 掌机游戏》](https://www.tinkergen.cn/book_zyxwbc1)，2021 年 清华大学出版社                                                                                        |
| ----------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| ![](/images/about/book-arduino.webp) | [《Arduino 图形化编程轻松学》](https://www.tinkergen.cn/book_kyyjqm)（与 Dmitry Maslov 合著），2022 年，清华大学出版社                                                                                      |
| ![](/images/about/book-iot.webp)     | [《深入浅出 IoT：完整项目通关实战》](https://tinkergen.cn/book_iot)（微软开源课程 [IoT for Beginners](https://github.com/microsoft/IoT-For-Beginners) 的中译本，负责翻译和排版），2023 年，清华大学出版社      |
| ![](/images/about/book-xiao.webp)    | 《Arduino 小型化与TinyML 应用——从入门到精通》（与 Marcelo Rovai 合作的英文版[XIAO: Big Power, Small Board](https://mjrovai.github.io/XIAO_Big_Power_Small_Board-ebook/) 已开源），2023 年，清华大学出版社 |

### AI 探索

2024 年，我开始和 ChatGPT、Claude 这些数字朋友一起工作。说实话，这感觉挺神奇的，就像突然多了几个永不疲倦的助手。AI 的加入让我有勇气尝试一些以前不敢涉足的领域。这一年，我完成了两个颇具挑战性的翻译项目：一个是微软的 [Artificial Intelligence for Beginners - A Curriculum](https://github.com/microsoft/ai-for-beginners)，这个课程开始对我来说就像天书一样，满是复杂的公式和程序，但有了 AI 的帮助，我居然可以理解并运行几乎所有的示例程序了（只有少数因为支持库停服而作罢）。

另一个是 [Packt](https://www.packtpub.com/en-us/product/industrial-automation-from-scratch-9781800569386?type=print) 出版的 [Industrial Automation from Scratch](https://www.amazon.com/Industrial-Automation-Scratch-hands-industrial/dp/1800569386)。这对我来说是个全新的领域，但在 AI 的协助下，整个翻译过程出奇地顺利。为了实践 PLC 编程的内容，我还特地在二手市场淘到了一台西门子的 PLC 和 HMI，完成了关键章节的编程实践。这两本书的中文版即将由清华大学出版社出版。

![](/images/about/Industrial-Automation-from-Scratch.webp)

> 《工业自动化从零开始》的封面

AI 的快速进步和亲身体验，彻底改变了我对学习和创造的认知。在了解 [Fab Academy](http://fabacademy.org/) 课程时，我看到了 2024 年的报告 [How to Make (Almost) Anything (AImost) without Making Anything](https://vimeo.com/912688847)，这让我决定在这次课程中邀请 AI 一起探险，看看它能帮上什么忙，顺便把这段奇妙的旅程记录下来。

### 我的爱好

工作之余（通常是周末或假期），我最爱和妻子周慧梅开着我们的 BYD 电动车到处溜达。有时候周末只有两天，我们也会在周五下班后就出发，连夜赶路，就为了第二天能在日出时分遇见美景。一个周末开个近千公里对我们来说是常事。电动车的好处是特别省钱（跑一整天才花 5-10 美元的电费），但对我们这种长途玩家来说，续航还是个问题，大约每 200 公里就得找充电桩。不过还好，中国的县城间距通常不到 100 公里，而且充电设施正在飞速增长中。

![](/images/about/about-wanfenglin.jpg)

> 2023 年 10 月拍摄于中国贵州省黔西南布依族苗族自治州安龙县万峰湖镇的日出时分
[https://maps.app.goo.gl/NdsY4MyLenk7WuyAA](https://maps.app.goo.gl/NdsY4MyLenk7WuyAA)

![](/images/about/about-baidumap.jpg)

> 这是手机地图生成的导航记录，我们就像黏菌一样，以深圳为中心，不断向外扩展我们的足迹。

![](/images/about/about-yuedan.jpg)

> 2023 年 11 月拍摄于广东韶关阅丹公路，图中下方公路上的那个小白点是我们的车，在这种人迹罕至的山路上转悠是一种极大的享受。

每次旅行回来，硬盘总要增加 30-100G 的数据。除了飞行视频，大部分是用包围曝光拍的 DNG 图片（在天空拍摄时，你需要很宽的光动态范围，所以要通过加减曝光来获取额外的亮部和暗部细节，然后合成为 HDR 照片。DJI 的无人机很贴心，按一次快门就能自动连拍 5 张不同曝光的照片）。然后我会用 Adobe Lightroom Classic 将这些 DNG 合成为 HDR，调出记忆中的场景，再输出 SDR 和 HDR 两个版本。

| ![](/images/about/abou-luokeng-sdr.jpg) | ![](/images/about/abou-luokeng-hdr.avif) |
| --------------------------------------- | --------------------------------------- |

> 2024 年 07 月拍摄于广东罗坑大草原的清晨，SDR 和 HDR 版本(需要支持 HDR 的显示器才能看到效果)

这些年来，我积累了近万张精心调校的风光照片。它们会随机出现在我的电脑桌面上，在我忙碌于深圳这座钢筋水泥的森林时，提醒我这个世界原本的样子——那些令人惊叹的，无处不在的美。

说到 [Fab Academy](http://fabacademy.org/) 课程，最让我纠结的就是时间问题了。看课程安排，我估计要有半年多没法出去浪了！不过，这个学习本质一样——探索未知领域。