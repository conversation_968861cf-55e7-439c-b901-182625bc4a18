<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Fab Academy documentation site for Your Name">
    <title>Your name - Fab Academy</title>
    <link rel="stylesheet" href="../style.css">
  </head>
  <body>
    <div class="navbar">
      <div class="navbar-inner">
        <a href="../index.html">Weekly Assignments</a>
        <a href="../final-project.html">Final Project</a>
        <a href="../about.html">About me</a>
      </div>
    </div>

    <div class="content">

      <h1>1. Project management</h1>

      <p>This week I worked on defining my final project idea and started to getting used to the documentation process.</p>

      <h2>Research</h2>

      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>

      <quote>"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</quote>

      <h2>Useful links</h2>

      <ul>
          <li><a href="../final-project.html">Example link to page on this website</a></li>
          <li><a href="https://www.w3schools.com/html/html_intro.asp" target="_blank">W3 HTML chools (open in new window)</a></li>
          <li><a href="https://gitlab.com/pages" target="_blank">GitLab Pages examples</a></li>
      </ul>

      <h2>Code Example</h2>

  <pre>
  // the setup function runs once when you press reset or power the board
  void setup() {
    // initialize digital pin LED_BUILTIN as an output.
    pinMode(LED_BUILTIN, OUTPUT);
  }

  // the loop function runs over and over again forever
  void loop() {
    digitalWrite(LED_BUILTIN, HIGH);   // turn the LED on (HIGH is the voltage level)
    delay(1000);                       // wait for a second
    digitalWrite(LED_BUILTIN, LOW);    // turn the LED off by making the voltage LOW
    delay(1000);                       // wait for a second
  }
  </pre>

      <h2>Gallery</h2>

      <img src="../images/sample-photo.jpg" alt="Description of image" width="800" />
      <!-- While this is an image from the images folder. Never use absolute paths (starting with /) when linking local images, always relative. -->

      <h2>Video</h2>

      <p>From Vimeo</p>

      <iframe src="https://player.vimeo.com/video/10048961" width="640" height="480" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>
      <p><a href="https://vimeo.com/10048961">Sound Waves</a> from <a href="https://vimeo.com/radarboy">George Gally (Radarboy)</a> on <a href="https://vimeo.com">Vimeo</a>.</p>

      <hr />

      <p>From Youtube</p>

      <iframe width="560" height="315" src="https://www.youtube.com/embed/jjNgJFemlC4" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>

      <h2>3D Models</h2>

      <div class="sketchfab-embed-wrapper"><iframe width="640" height="480" src="https://sketchfab.com/models/658c8f8a2f3042c3ad7bdedd83f1c915/embed" frameborder="0" allow="autoplay; fullscreen; vr" mozallowfullscreen="true" webkitallowfullscreen="true"></iframe>

          <p style="font-size: 13px; font-weight: normal; margin: 5px; color: #4A4A4A;">
              <a href="https://sketchfab.com/models/658c8f8a2f3042c3ad7bdedd83f1c915?utm_medium=embed&utm_source=website&utm_campaign=share-popup" target="_blank" style="font-weight: bold; color: #1CAAD9;">Dita&#39;s Gown</a>
              by <a href="https://sketchfab.com/francisbitontistudio?utm_medium=embed&utm_source=website&utm_campaign=share-popup" target="_blank" style="font-weight: bold; color: #1CAAD9;">Francis Bitonti Studio</a>
              on <a href="https://sketchfab.com?utm_medium=embed&utm_source=website&utm_campaign=share-popup" target="_blank" style="font-weight: bold; color: #1CAAD9;">Sketchfab</a>
          </p>
      </div>

    </div>

    <footer>
      <p>Copyright 2025 &#60;Your name&#62; - Creative Commons Attribution Non Commercial </p>
      <p>Source code hosted at <a href="https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng" target="_blank">gitlab.fabcloud.org</a></p>
    </footer>
  </body>
</html>



