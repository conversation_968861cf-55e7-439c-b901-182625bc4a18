<!DOCTYPE html>
<html lang="en-us">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Fab Academy documentation site for Your Name">
    <title>Your name - Fab Academy</title>
    <link rel="stylesheet" href="style.css">
  </head>
  <body>
    <div class="navbar">
      <div class="navbar-inner">
        <a href="index.html">Weekly Assignments</a>
        <a href="final-project.html">Final Project</a>
        <a href="about.html">About me</a>
      </div>
    </div>

    <div class="content">

      <h1>Welcome to &#60;Your name&#62; Fab Academy site!</h1>

      <p>
        This is an example student documentation for the Fab Academy. You can edit this <a href="https://www.w3schools.com/html/default.asp" target="_blank">HTML</a> on <a href="http://gitlab.fabcloud.org" target="_blank">Gitlab Fabcloud</a>. Below you can find a example list to organize each week's documentation into separate pages. You can also change the looks of your website by using <a href="https://www.w3schools.com/css/default.asp" target="_blank">CSS</a> with the <code>style.css</code> file.
      </p>

      <h2>Weekly assignments</h2>
      <ol style="list-style: none;">
        <!-- This is how you can link to another page. Never use absolute paths (starting with /) when linking pages, always relative. -->
        <li><a href="assignments/week01.html">week 1. Project management</a></li>
        <li>week 2. Computer Aided design</li>
        <li>week 3. Computer controlled cutting</li>
        <li>week 4. Embedded programming</li>
        <li>week 5. 3D Scanning and printing</li>
        <li>week 6. Electronics design</li>
        <li>week 7. Computer controlled machining</li>
        <li>week 8. Electronics production</li>
        <li>week 9. Molding and casting</li>
        <li>week 10. Output devices</li>
        <li>week 11. Mechanical design &amp; machine design</li>
        <li>week 12. Input devices</li>
        <li>week 13. Networking and communications</li>
        <li>week 14. Interface and application programming</li>
        <li>week 15. Wildcard week</li>
        <li>week 16. Applications and implications</li>
        <li>week 17. Invention, intellectual property and income</li>
        <li>week 18. Project development</li>
      </ol>

    </div>

    <footer>
      <p>Copyright 2025 &#60;Your name&#62; - Creative Commons Attribution Non Commercial </p>
      <p>Source code hosted at <a href="https://gitlab.fabcloud.org/academany/fabacademy/2025/labs/chaihuo/students/lei-feng" target="_blank">gitlab.fabcloud.org</a></p>
    </footer> 

  </body>
</html>
